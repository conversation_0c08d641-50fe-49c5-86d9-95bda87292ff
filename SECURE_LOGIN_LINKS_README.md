# Secure Login Links System

This system provides secure, single-use login links that allow users to access the app instantly without showing the login screen. The links are secure, expire after one use or 24 hours, and include comprehensive audit logging.

## Features

- **Single-use tokens**: Each link can only be used once for security
- **Time-based expiration**: Links expire after 24 hours automatically
- **Secure token generation**: 128-character cryptographically secure tokens
- **Comprehensive audit logging**: All token generation and usage is logged
- **Admin-only generation**: Only admins and staff can generate secure links
- **Automatic cleanup**: Expired and used tokens are automatically cleaned up
- **Fallback to normal login**: Invalid/expired tokens redirect to normal login

## Installation

### 1. Run the Setup Script

First, run the setup script to create the necessary database components:

```
http://yourdomain.com/admin/setup_secure_login_tokens.php
```

This will create:
- `secure_login_tokens` table
- `CleanupExpiredLoginTokens` stored procedure
- `active_secure_login_tokens` view

### 2. Test the System

Run the test script to verify everything is working:

```
http://yourdomain.com/admin/test_secure_login.php
```

### 3. Set Up Cleanup (Optional)

Set up a cron job to automatically clean up expired tokens:

```bash
# Run cleanup daily at 2 AM
0 2 * * * /usr/bin/php /path/to/admin/cleanup_expired_tokens.php
```

Or run manually:
```
http://yourdomain.com/admin/cleanup_expired_tokens.php
```

## Usage

### For Admins/Staff

1. Go to any user's profile page (`user_view.php?id=USER_ID`)
2. Find the "Secure Login Link" section
3. Click "Generate Secure Link" to create a new link
4. Copy the generated link and share it with the user
5. The link will expire after 24 hours or first use

### For Users

1. Click on the secure login link provided by admin
2. You'll be automatically logged into the app
3. If the link is invalid/expired, you'll see the normal login screen

## Security Features

### Token Security
- 128-character cryptographically secure random tokens
- Single-use enforcement (token is marked as used after first login)
- Time-based expiration (24 hours maximum)
- Secure token validation with proper error handling

### Audit Logging
All actions are logged in the `admin_action_logs` table:
- Token generation (who generated, for which user, when)
- Token usage (when used, from which IP, user agent)
- Token revocation (manual revocation by admin)

### Access Control
- Only admins and staff can generate secure login links
- Users cannot generate their own links
- Links are tied to specific users and cannot be transferred

## Database Schema

### secure_login_tokens Table

```sql
CREATE TABLE secure_login_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(128) NOT NULL UNIQUE,
    generated_by_admin INT NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL DEFAULT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (generated_by_admin) REFERENCES admin_users(id) ON DELETE CASCADE
);
```

## API Endpoints

### Generate Token
```javascript
POST /admin/api/secure_login_token.php
{
    "action": "generate",
    "user_id": 123
}
```

### Revoke Tokens
```javascript
POST /admin/api/secure_login_token.php
{
    "action": "revoke",
    "user_id": 123
}
```

### List Active Tokens
```javascript
POST /admin/api/secure_login_token.php
{
    "action": "list",
    "user_id": 123
}
```

## Files Created/Modified

### New Files
- `admin/api/secure_login_token.php` - API endpoint for token management
- `admin/database/secure_login_tokens_schema.sql` - Database schema
- `admin/setup_secure_login_tokens.php` - Setup script
- `admin/cleanup_expired_tokens.php` - Cleanup script
- `admin/test_secure_login.php` - Test script

### Modified Files
- `admin/user_view.php` - Added secure login link UI and functionality
- `admin/login.php` - Added secure token login handling

## Troubleshooting

### Common Issues

1. **"Table doesn't exist" error**
   - Run `setup_secure_login_tokens.php` first

2. **"Access denied" error**
   - Make sure you're logged in as admin or staff

3. **"Invalid token" error**
   - Token may be expired or already used
   - Check if token exists in database

4. **JavaScript errors**
   - Check browser console for detailed error messages
   - Ensure API endpoint is accessible

### Debugging

1. Check the `admin_action_logs` table for audit trail
2. Run `test_secure_login.php` to verify system status
3. Check server error logs for PHP errors
4. Use browser developer tools to debug JavaScript issues

## Security Considerations

- Links should only be shared through secure channels
- Monitor the audit logs regularly for suspicious activity
- Set up regular cleanup to prevent token accumulation
- Consider shorter expiration times for high-security environments
- Ensure HTTPS is used in production for secure token transmission

## Maintenance

- Run cleanup script regularly (daily recommended)
- Monitor database size of `secure_login_tokens` table
- Review audit logs periodically
- Update token expiration time if needed (currently 24 hours)
