<!DOCTYPE html>
<html>
<head>
    <title>Test Login API</title>
</head>
<body>
    <h1>Test Login API</h1>
    <button onclick="testLogin()">Test Login</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('http://localhost:9001/admin/api/login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Origin': 'http://localhost:8080'
                    },
                    body: JSON.stringify({
                        username: 'jafersadik2',
                        pin: '6231',
                        device_id: 'test_device'
                    })
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                const text = await response.text();
                console.log('Response text:', text);
                
                resultDiv.innerHTML = `
                    <h3>Response Status: ${response.status}</h3>
                    <h3>Response:</h3>
                    <pre>${text}</pre>
                `;
                
                // Try to parse as JSON
                try {
                    const json = JSON.parse(text);
                    console.log('Parsed JSON:', json);
                } catch (e) {
                    console.error('Failed to parse JSON:', e);
                    resultDiv.innerHTML += `<p style="color: red;">Failed to parse as JSON: ${e.message}</p>`;
                }
                
            } catch (error) {
                console.error('Fetch error:', error);
                resultDiv.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
