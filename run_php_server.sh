#!/bin/bash

# KFT Fitness PHP Server Script
# Starts the PHP development server for the admin dashboard and API

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Server configuration
SERVER_HOST="0.0.0.0"
SERVER_PORT="9001"
DOCUMENT_ROOT="."

echo -e "${BLUE}🚀 Starting KFT Fitness PHP Server${NC}"
echo "=================================================="
echo -e "${YELLOW}Server Configuration:${NC}"
echo "  Host: $SERVER_HOST"
echo "  Port: $SERVER_PORT"
echo "  Document Root: $DOCUMENT_ROOT"
echo "  Admin Panel: http://localhost:$SERVER_PORT/admin/"
echo "  API Endpoint: http://localhost:$SERVER_PORT/admin/api/"
echo "=================================================="
echo

# Check if PHP is installed
if ! command -v php &> /dev/null; then
    echo -e "${RED}❌ PHP is not installed or not in PATH${NC}"
    echo "Please install PHP first:"
    echo "  - macOS: brew install php"
    echo "  - Ubuntu: sudo apt-get install php"
    echo "  - Windows: Download from https://www.php.net/downloads"
    exit 1
fi

# Check PHP version
PHP_VERSION=$(php -v | head -n 1 | cut -d ' ' -f 2)
echo -e "${GREEN}✅ PHP Version: $PHP_VERSION${NC}"

# Check if admin directory exists
if [ ! -d "admin" ]; then
    echo -e "${RED}❌ Admin directory not found${NC}"
    echo "Please ensure you're running this script from the project root directory"
    exit 1
fi

echo -e "${GREEN}✅ Admin directory found${NC}"

# Check database configuration
if [ -f "admin/includes/config.php" ]; then
    echo -e "${GREEN}✅ Database configuration found${NC}"
else
    echo -e "${YELLOW}⚠️  Database configuration not found${NC}"
    echo "Please ensure admin/includes/config.php is properly configured"
fi

echo
echo -e "${BLUE}Starting PHP development server...${NC}"
echo -e "${YELLOW}Press Ctrl+C to stop the server${NC}"
echo

# Start the PHP server
php -S $SERVER_HOST:$SERVER_PORT -t $DOCUMENT_ROOT
