<?php
/**
 * Create URL Shortener Database Tables
 * Run this script once to create the necessary tables for URL shortening
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';

// Check if admin is logged in
$auth = new Auth();
if (!$auth->isLoggedIn() || !$auth->hasRole(['admin', 'super_admin'])) {
    die('Unauthorized access. Please login as admin.');
}

$db = new Database();
$conn = $db->getConnection();

echo "<h2>Creating URL Shortener Tables</h2>";

try {
    // Create short_urls table
    $shortUrlsTable = "
    CREATE TABLE IF NOT EXISTS short_urls (
        id INT AUTO_INCREMENT PRIMARY KEY,
        short_code VARCHAR(10) UNIQUE NOT NULL,
        long_url TEXT NOT NULL,
        user_id INT NULL,
        expires_at DATETIME NULL,
        is_active BOOLEAN DEFAULT TRUE,
        click_count INT DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_accessed DATETIME NULL,
        INDEX idx_short_code (short_code),
        INDEX idx_user_id (user_id),
        INDEX idx_expires_at (expires_at),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    if ($conn->query($shortUrlsTable)) {
        echo "✅ Created 'short_urls' table successfully<br>";
    } else {
        echo "❌ Error creating 'short_urls' table: " . $conn->error . "<br>";
    }
    
    // Create short_url_logs table for analytics
    $shortUrlLogsTable = "
    CREATE TABLE IF NOT EXISTS short_url_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        short_code VARCHAR(10) NOT NULL,
        action ENUM('created', 'accessed', 'deactivated') NOT NULL,
        ip_address VARCHAR(45) NULL,
        user_agent TEXT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_short_code (short_code),
        INDEX idx_action (action),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    if ($conn->query($shortUrlLogsTable)) {
        echo "✅ Created 'short_url_logs' table successfully<br>";
    } else {
        echo "❌ Error creating 'short_url_logs' table: " . $conn->error . "<br>";
    }
    
    // Add foreign key constraints if users table exists
    $checkUsersTable = "SHOW TABLES LIKE 'users'";
    $result = $conn->query($checkUsersTable);
    
    if ($result && $result->num_rows > 0) {
        $addForeignKey = "
        ALTER TABLE short_urls 
        ADD CONSTRAINT fk_short_urls_user_id 
        FOREIGN KEY (user_id) REFERENCES users(id) 
        ON DELETE SET NULL ON UPDATE CASCADE;
        ";
        
        // Check if foreign key already exists
        $checkForeignKey = "
        SELECT CONSTRAINT_NAME 
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'short_urls' 
        AND CONSTRAINT_NAME = 'fk_short_urls_user_id'
        ";
        
        $fkResult = $conn->query($checkForeignKey);
        
        if ($fkResult && $fkResult->num_rows === 0) {
            if ($conn->query($addForeignKey)) {
                echo "✅ Added foreign key constraint successfully<br>";
            } else {
                echo "⚠️ Warning: Could not add foreign key constraint: " . $conn->error . "<br>";
            }
        } else {
            echo "ℹ️ Foreign key constraint already exists<br>";
        }
    }
    
    echo "<br><h3>✅ URL Shortener tables created successfully!</h3>";
    echo "<p>You can now use the URL shortening functionality for secure login links.</p>";
    echo "<p><a href='user_view.php'>← Back to User Management</a></p>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

$conn->close();
?>
