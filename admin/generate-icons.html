<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Icon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .icon-container {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        .icon {
            background: #27ae60;
            border-radius: 15%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .icon-72 { width: 72px; height: 72px; font-size: 24px; }
        .icon-96 { width: 96px; height: 96px; font-size: 32px; }
        .icon-128 { width: 128px; height: 128px; font-size: 42px; }
        .icon-144 { width: 144px; height: 144px; font-size: 48px; }
        .icon-152 { width: 152px; height: 152px; font-size: 50px; }
        .icon-192 { width: 192px; height: 192px; font-size: 64px; }
        .icon-384 { width: 384px; height: 384px; font-size: 128px; }
        .icon-512 { width: 512px; height: 512px; font-size: 170px; }
        
        .dumbbell {
            position: relative;
            display: inline-block;
        }
        
        button {
            background: #27ae60;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #219150;
        }
    </style>
</head>
<body>
    <h1>PWA Icon Generator for KFT Admin</h1>
    <p>Right-click on each icon and "Save image as..." to download the PNG files.</p>
    
    <div class="icon-container">
        <canvas id="canvas-72" class="icon icon-72"></canvas>
        <div>72x72</div>
        <button onclick="downloadIcon(72)">Download</button>
    </div>
    
    <div class="icon-container">
        <canvas id="canvas-96" class="icon icon-96"></canvas>
        <div>96x96</div>
        <button onclick="downloadIcon(96)">Download</button>
    </div>
    
    <div class="icon-container">
        <canvas id="canvas-128" class="icon icon-128"></canvas>
        <div>128x128</div>
        <button onclick="downloadIcon(128)">Download</button>
    </div>
    
    <div class="icon-container">
        <canvas id="canvas-144" class="icon icon-144"></canvas>
        <div>144x144</div>
        <button onclick="downloadIcon(144)">Download</button>
    </div>
    
    <div class="icon-container">
        <canvas id="canvas-152" class="icon icon-152"></canvas>
        <div>152x152</div>
        <button onclick="downloadIcon(152)">Download</button>
    </div>
    
    <div class="icon-container">
        <canvas id="canvas-192" class="icon icon-192"></canvas>
        <div>192x192</div>
        <button onclick="downloadIcon(192)">Download</button>
    </div>
    
    <div class="icon-container">
        <canvas id="canvas-384" class="icon icon-384"></canvas>
        <div>384x384</div>
        <button onclick="downloadIcon(384)">Download</button>
    </div>
    
    <div class="icon-container">
        <canvas id="canvas-512" class="icon icon-512"></canvas>
        <div>512x512</div>
        <button onclick="downloadIcon(512)">Download</button>
    </div>

    <script>
        const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
        
        function drawIcon(size) {
            const canvas = document.getElementById(`canvas-${size}`);
            const ctx = canvas.getContext('2d');
            
            // Set canvas size
            canvas.width = size;
            canvas.height = size;
            
            // Background
            ctx.fillStyle = '#27ae60';
            ctx.fillRect(0, 0, size, size);
            
            // Round corners
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, size * 0.15);
            ctx.fill();
            ctx.globalCompositeOperation = 'source-over';
            
            // Draw dumbbell
            ctx.fillStyle = 'white';
            
            const scale = size / 512;
            const centerX = size / 2;
            const centerY = size / 2;
            
            // Left weight
            ctx.fillRect(centerX - 180 * scale, centerY - 40 * scale, 60 * scale, 80 * scale);
            ctx.fillRect(centerX - 170 * scale, centerY - 50 * scale, 40 * scale, 100 * scale);
            
            // Right weight
            ctx.fillRect(centerX + 120 * scale, centerY - 40 * scale, 60 * scale, 80 * scale);
            ctx.fillRect(centerX + 130 * scale, centerY - 50 * scale, 40 * scale, 100 * scale);
            
            // Bar
            ctx.fillRect(centerX - 120 * scale, centerY - 8 * scale, 240 * scale, 16 * scale);
            
            // Grip
            ctx.fillRect(centerX - 60 * scale, centerY - 12 * scale, 120 * scale, 24 * scale);
            
            // Text
            ctx.fillStyle = 'white';
            ctx.font = `bold ${48 * scale}px Inter, sans-serif`;
            ctx.textAlign = 'center';
            ctx.fillText('KFT', centerX, centerY + 160 * scale);
            
            ctx.font = `${24 * scale}px Inter, sans-serif`;
            ctx.fillText('ADMIN', centerX, centerY + 200 * scale);
        }
        
        function downloadIcon(size) {
            const canvas = document.getElementById(`canvas-${size}`);
            const link = document.createElement('a');
            link.download = `pwa-icon-${size}x${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Draw all icons on page load
        window.onload = function() {
            sizes.forEach(size => drawIcon(size));
        };
    </script>
</body>
</html>
