/* Left-Side Popup Alert System */

/* Alert Container - Desktop: Lower Right, Mobile: Left Side */
.left-popup-alerts-container {
    position: fixed;
    z-index: 9999;
    max-width: 400px;
    width: 100%;
    pointer-events: none;

    /* Desktop: Lower right */
    bottom: 20px;
    right: 20px;
    top: auto;
    left: auto;
}

/* Mobile: Left side */
@media (max-width: 768px) {
    .left-popup-alerts-container {
        top: 80px;
        left: 10px;
        right: 10px;
        bottom: auto;
        max-width: none;
        width: auto;
    }
}

/* Individual Alert */
.left-popup-alert {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    margin-bottom: 16px;
    padding: 20px;
    border-left: 5px solid;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    pointer-events: auto;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.08);

    /* Desktop: slide from right */
    transform: translateX(120%);
}

/* Mobile: slide from left */
@media (max-width: 768px) {
    .left-popup-alert {
        transform: translateX(-120%);
    }
}

/* Show animation */
.left-popup-alert.show {
    transform: translateX(0);
    opacity: 1;
}

/* Hide animation - Desktop: slide to right */
.left-popup-alert.hide {
    transform: translateX(120%);
    opacity: 0;
}

/* Hide animation - Mobile: slide to left */
@media (max-width: 768px) {
    .left-popup-alert.hide {
        transform: translateX(-120%);
        opacity: 0;
    }
}

/* Alert Types - Professional Dashboard Colors */
.left-popup-alert.alert-success {
    border-left-color: #10b981;
    background: linear-gradient(135deg, #ffffff, #f0fdf4);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.left-popup-alert.alert-danger {
    border-left-color: #ef4444;
    background: linear-gradient(135deg, #ffffff, #fef2f2);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.left-popup-alert.alert-warning {
    border-left-color: #f59e0b;
    background: linear-gradient(135deg, #ffffff, #fffbeb);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.left-popup-alert.alert-info {
    border-left-color: #3b82f6;
    background: linear-gradient(135deg, #ffffff, #eff6ff);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.left-popup-alert.alert-primary {
    border-left-color: #6366f1;
    background: linear-gradient(135deg, #ffffff, #f0f9ff);
    border: 1px solid rgba(99, 102, 241, 0.2);
}

/* Alert Content */
.left-popup-alert-content {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.left-popup-alert-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 14px;
    margin-top: 2px;
}

.left-popup-alert.alert-success .left-popup-alert-icon {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.left-popup-alert.alert-danger .left-popup-alert-icon {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.left-popup-alert.alert-warning .left-popup-alert-icon {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.left-popup-alert.alert-info .left-popup-alert-icon {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.left-popup-alert.alert-primary .left-popup-alert-icon {
    background: rgba(99, 102, 241, 0.1);
    color: #6366f1;
}

.left-popup-alert-message {
    flex: 1;
    font-size: 14px;
    line-height: 1.5;
    color: #000000;
    font-weight: 500;
}

.left-popup-alert-close {
    position: absolute;
    top: 8px;
    right: 8px;
    background: none;
    border: none;
    color: #999;
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.left-popup-alert-close:hover {
    background: rgba(0, 0, 0, 0.05);
    color: #666;
}

/* Progress Bar */
.left-popup-alert-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 0 0 12px 12px;
    overflow: hidden;
}

.left-popup-alert-progress-bar {
    height: 100%;
    width: 100%;
    transform: translateX(-100%);
    transition: transform linear;
}

.left-popup-alert.alert-success .left-popup-alert-progress-bar {
    background: #10b981;
}

.left-popup-alert.alert-danger .left-popup-alert-progress-bar {
    background: #ef4444;
}

.left-popup-alert.alert-warning .left-popup-alert-progress-bar {
    background: #f59e0b;
}

.left-popup-alert.alert-info .left-popup-alert-progress-bar {
    background: #3b82f6;
}

.left-popup-alert.alert-primary .left-popup-alert-progress-bar {
    background: #6366f1;
}

/* Hover Effects */
.left-popup-alert:hover {
    transform: translateX(5px) scale(1.02);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.left-popup-alert:hover .left-popup-alert-progress-bar {
    animation-play-state: paused;
}

/* Additional Mobile Responsive Design */
@media (max-width: 768px) {
    .left-popup-alert {
        padding: 16px;
        margin-bottom: 12px;
    }

    .left-popup-alert-content {
        gap: 12px;
    }

    .left-popup-alert-message {
        font-size: 13px;
    }
}

/* Animation Keyframes */
@keyframes slideInLeft {
    from {
        transform: translateX(-120%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutLeft {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(-120%);
        opacity: 0;
    }
}

@keyframes progressAnimation {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Stacking Animation */
.left-popup-alert:nth-child(1) { animation-delay: 0ms; }
.left-popup-alert:nth-child(2) { animation-delay: 100ms; }
.left-popup-alert:nth-child(3) { animation-delay: 200ms; }
.left-popup-alert:nth-child(4) { animation-delay: 300ms; }
.left-popup-alert:nth-child(5) { animation-delay: 400ms; }

/* Dark Mode Support - Keep light background with black text for readability */
@media (prefers-color-scheme: dark) {
    .left-popup-alert {
        background: #ffffff;
        color: #000000;
        border: 1px solid rgba(0, 0, 0, 0.08);
    }

    .left-popup-alert-message {
        color: #000000;
    }

    .left-popup-alert-close {
        color: #666666;
    }

    .left-popup-alert-close:hover {
        background: rgba(0, 0, 0, 0.05);
        color: #333333;
    }
}
