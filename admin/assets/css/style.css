/* Custom styles for KFT Fitness Admin Dashboard */

/* Sidebar - Material Design Blue */
#sidebar-wrapper {
    min-height: 100vh;
    width: 250px;
    margin-left: -250px;
    transition: margin 0.25s ease-out;
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
    color: #fff;
}

#sidebar-wrapper .sidebar-heading {
    padding: 0.875rem 1.25rem;
    font-size: 1.2rem;
    color: #fff;
}

#wrapper.toggled #sidebar-wrapper {
    margin-left: 0;
}

#page-content-wrapper {
    min-width: 100vw;
    min-height: 100vh;
    background: #fff;
    color: #111;
}

.list-group-item-action:hover {
    background-color: #222 !important;
    color: #fff !important;
}

/* Active sidebar item */
.list-group-item-action.active {
    background-color: #000 !important;
    border-color: transparent !important;
    color: #fff !important;
}

/* Smooth navigation transitions */
.sidebar-menu-item {
    transition: all 0.2s ease;
    position: relative;
}

.sidebar-menu-item:hover {
    transform: translateX(2px);
}

.sidebar-menu-item.navigating {
    opacity: 0.7;
    transform: translateX(4px);
}

/* Prevent scroll jump on navigation */
html {
    scroll-behavior: smooth;
}

/* Maintain scroll position during page transitions */
body {
    overflow-anchor: auto;
}

/* Dashboard cards */
.dashboard-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    background: #fff;
    color: #111;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.12);
}

.card-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #111;
    color: #fff;
}

/* Tables */
.table-hover tbody tr:hover {
    background-color: #eee;
}

/* Charts */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Responsive adjustments */
@media (min-width: 768px) {
    #sidebar-wrapper {
        margin-left: 0;
    }

    #page-content-wrapper {
        min-width: 0;
        width: 100%;
    }

    #wrapper.toggled #sidebar-wrapper {
        margin-left: -250px;
    }
}

/* Custom theme colors */
.bg-primary-gradient, .bg-success-gradient, .bg-warning-gradient, .bg-danger-gradient {
    background: #111 !important;
    color: #fff !important;
}

/* Login page */
.login-container {
    max-width: 400px;
    margin: 100px auto;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.login-logo {
    text-align: center;
    margin-bottom: 30px;
}

/* Profile page */
.profile-header {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
}

/* Custom buttons */
.btn-rounded {
    border-radius: 50px;
    padding: 8px 20px;
}

/* Custom badges */
.badge-pill {
    border-radius: 50px;
    padding: 5px 10px;
}

/* Custom form controls */
.form-control:focus {
    border-color: #2193b0;
    box-shadow: 0 0 0 0.25rem rgba(33, 147, 176, 0.25);
}
