/**
 * Left-Side Popup Alert System
 * Modern, animated alert system that appears from the left side
 */

class LeftPopupAlerts {
    constructor() {
        this.container = null;
        this.alerts = new Map();
        this.alertCounter = 0;
        this.maxAlerts = 5;
        this.defaultDuration = 5000; // 5 seconds
        
        this.init();
    }
    
    init() {
        // Create container if it doesn't exist
        if (!document.querySelector('.left-popup-alerts-container')) {
            this.container = document.createElement('div');
            this.container.className = 'left-popup-alerts-container';
            document.body.appendChild(this.container);
        } else {
            this.container = document.querySelector('.left-popup-alerts-container');
        }
    }
    
    /**
     * Show an alert
     * @param {string} message - Alert message
     * @param {string} type - Alert type (success, danger, warning, info, primary)
     * @param {number} duration - Duration in milliseconds (0 for persistent)
     * @param {boolean} dismissible - Whether the alert can be dismissed
     */
    show(message, type = 'info', duration = null, dismissible = true) {
        // Use default duration if not specified
        if (duration === null) {
            duration = this.defaultDuration;
        }
        
        // Remove oldest alert if we've reached the maximum
        if (this.alerts.size >= this.maxAlerts) {
            const oldestId = this.alerts.keys().next().value;
            this.hide(oldestId);
        }
        
        // Create alert element
        const alertId = `alert-${++this.alertCounter}`;
        const alertElement = this.createAlertElement(alertId, message, type, dismissible);
        
        // Add to container
        this.container.appendChild(alertElement);
        
        // Store alert reference
        this.alerts.set(alertId, {
            element: alertElement,
            type: type,
            message: message,
            duration: duration,
            dismissible: dismissible,
            timeoutId: null
        });
        
        // Trigger show animation
        setTimeout(() => {
            alertElement.classList.add('show');
        }, 10);
        
        // Set auto-hide timer if duration > 0
        if (duration > 0) {
            this.setAutoHide(alertId, duration);
        }
        
        return alertId;
    }
    
    /**
     * Create alert element
     */
    createAlertElement(alertId, message, type, dismissible) {
        const alertDiv = document.createElement('div');
        alertDiv.id = alertId;
        alertDiv.className = `left-popup-alert alert-${type}`;
        
        // Get icon based on type
        const icon = this.getIcon(type);
        
        // Create alert HTML
        alertDiv.innerHTML = `
            <div class="left-popup-alert-content">
                <div class="left-popup-alert-icon">
                    <i class="fas ${icon}"></i>
                </div>
                <div class="left-popup-alert-message">${message}</div>
            </div>
            ${dismissible ? '<button type="button" class="left-popup-alert-close" aria-label="Close"><i class="fas fa-times"></i></button>' : ''}
            <div class="left-popup-alert-progress">
                <div class="left-popup-alert-progress-bar"></div>
            </div>
        `;
        
        // Add close event listener
        if (dismissible) {
            const closeBtn = alertDiv.querySelector('.left-popup-alert-close');
            closeBtn.addEventListener('click', () => {
                this.hide(alertId);
            });
        }
        
        // Add hover events to pause/resume progress
        alertDiv.addEventListener('mouseenter', () => {
            this.pauseProgress(alertId);
        });
        
        alertDiv.addEventListener('mouseleave', () => {
            this.resumeProgress(alertId);
        });
        
        return alertDiv;
    }
    
    /**
     * Get icon for alert type
     */
    getIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            danger: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle',
            primary: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }
    
    /**
     * Set auto-hide timer with progress bar
     */
    setAutoHide(alertId, duration) {
        const alert = this.alerts.get(alertId);
        if (!alert) return;
        
        const progressBar = alert.element.querySelector('.left-popup-alert-progress-bar');
        
        // Animate progress bar
        progressBar.style.transition = `transform ${duration}ms linear`;
        progressBar.style.transform = 'translateX(0)';
        
        // Set timeout to hide alert
        alert.timeoutId = setTimeout(() => {
            this.hide(alertId);
        }, duration);
    }
    
    /**
     * Pause progress animation
     */
    pauseProgress(alertId) {
        const alert = this.alerts.get(alertId);
        if (!alert || !alert.timeoutId) return;
        
        const progressBar = alert.element.querySelector('.left-popup-alert-progress-bar');
        const computedStyle = window.getComputedStyle(progressBar);
        const currentTransform = computedStyle.transform;
        
        // Pause the animation
        progressBar.style.transition = 'none';
        progressBar.style.transform = currentTransform;
        
        // Clear timeout
        clearTimeout(alert.timeoutId);
        alert.timeoutId = null;
    }
    
    /**
     * Resume progress animation
     */
    resumeProgress(alertId) {
        const alert = this.alerts.get(alertId);
        if (!alert || alert.duration <= 0) return;
        
        const progressBar = alert.element.querySelector('.left-popup-alert-progress-bar');
        const currentTransform = progressBar.style.transform;
        
        // Calculate remaining time based on current progress
        const matrix = new DOMMatrix(currentTransform);
        const currentProgress = (matrix.m41 + progressBar.offsetWidth) / progressBar.offsetWidth;
        const remainingTime = alert.duration * (1 - currentProgress);
        
        if (remainingTime > 0) {
            // Resume animation
            progressBar.style.transition = `transform ${remainingTime}ms linear`;
            progressBar.style.transform = 'translateX(0)';
            
            // Set new timeout
            alert.timeoutId = setTimeout(() => {
                this.hide(alertId);
            }, remainingTime);
        }
    }
    
    /**
     * Hide an alert
     */
    hide(alertId) {
        const alert = this.alerts.get(alertId);
        if (!alert) return;
        
        // Clear timeout if exists
        if (alert.timeoutId) {
            clearTimeout(alert.timeoutId);
        }
        
        // Add hide animation
        alert.element.classList.add('hide');
        alert.element.classList.remove('show');
        
        // Remove from DOM after animation
        setTimeout(() => {
            if (alert.element.parentNode) {
                alert.element.parentNode.removeChild(alert.element);
            }
            this.alerts.delete(alertId);
        }, 400);
    }
    
    /**
     * Hide all alerts
     */
    hideAll() {
        const alertIds = Array.from(this.alerts.keys());
        alertIds.forEach(id => this.hide(id));
    }
    
    /**
     * Show success alert
     */
    success(message, duration = null) {
        return this.show(message, 'success', duration);
    }
    
    /**
     * Show error alert
     */
    error(message, duration = null) {
        return this.show(message, 'danger', duration);
    }
    
    /**
     * Show warning alert
     */
    warning(message, duration = null) {
        return this.show(message, 'warning', duration);
    }
    
    /**
     * Show info alert
     */
    info(message, duration = null) {
        return this.show(message, 'info', duration);
    }
    
    /**
     * Show primary alert
     */
    primary(message, duration = null) {
        return this.show(message, 'primary', duration);
    }
}

// Initialize global instance
window.leftPopupAlerts = new LeftPopupAlerts();

// Convenience functions for global access
window.showAlert = (message, type = 'info', duration = null) => {
    return window.leftPopupAlerts.show(message, type, duration);
};

window.showSuccess = (message, duration = null) => {
    return window.leftPopupAlerts.success(message, duration);
};

window.showError = (message, duration = null) => {
    return window.leftPopupAlerts.error(message, duration);
};

window.showWarning = (message, duration = null) => {
    return window.leftPopupAlerts.warning(message, duration);
};

window.showInfo = (message, duration = null) => {
    return window.leftPopupAlerts.info(message, duration);
};

// Auto-convert existing Bootstrap alerts to left popup alerts
document.addEventListener('DOMContentLoaded', function() {
    // Find existing Bootstrap alerts and convert them
    const existingAlerts = document.querySelectorAll('.alert:not(.left-popup-alert)');
    
    existingAlerts.forEach(alert => {
        // Extract message
        const message = alert.textContent.trim();
        
        // Determine type
        let type = 'info';
        if (alert.classList.contains('alert-success')) type = 'success';
        else if (alert.classList.contains('alert-danger')) type = 'danger';
        else if (alert.classList.contains('alert-warning')) type = 'warning';
        else if (alert.classList.contains('alert-primary')) type = 'primary';
        
        // Show as popup alert
        if (message) {
            window.leftPopupAlerts.show(message, type);
        }
        
        // Hide original alert
        alert.style.display = 'none';
    });
});
