/**
 * PWA Install Functionality
 * Handles the installation prompt and FAB button behavior
 */

class PWAInstaller {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.isStandalone = false;
        
        this.init();
    }
    
    init() {
        this.checkInstallationStatus();
        this.bindEvents();
        this.checkStandaloneMode();
    }
    
    checkInstallationStatus() {
        // Check if app is already installed
        if (window.matchMedia('(display-mode: standalone)').matches) {
            this.isStandalone = true;
            this.hideInstallButton();
            return;
        }
        
        // Check if running in PWA mode
        if (window.navigator.standalone === true) {
            this.isStandalone = true;
            this.hideInstallButton();
            return;
        }
        
        // Check if install prompt is available
        if ('serviceWorker' in navigator && 'PushManager' in window) {
            this.checkForInstallPrompt();
        }
    }
    
    checkStandaloneMode() {
        // Listen for display mode changes
        window.matchMedia('(display-mode: standalone)').addEventListener('change', (e) => {
            if (e.matches) {
                this.isStandalone = true;
                this.hideInstallButton();
            }
        });
    }
    
    checkForInstallPrompt() {
        // Show install button after a delay if not installed
        setTimeout(() => {
            if (!this.isStandalone && !this.deferredPrompt) {
                // Check if we should show the install button based on user behavior
                const visitCount = this.getVisitCount();
                if (visitCount >= 2) { // Show after 2 visits
                    this.showInstallButton();
                }
            }
        }, 3000); // Wait 3 seconds before showing
    }
    
    bindEvents() {
        // Listen for the beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA: beforeinstallprompt event fired');
            
            // Prevent the mini-infobar from appearing on mobile
            e.preventDefault();
            
            // Stash the event so it can be triggered later
            this.deferredPrompt = e;
            
            // Show the install button
            this.showInstallButton();
        });
        
        // Listen for the appinstalled event
        window.addEventListener('appinstalled', (e) => {
            console.log('PWA: App was installed');
            this.isInstalled = true;
            this.hideInstallButton();
            this.showInstallSuccessMessage();
        });
        
        // Bind click events
        this.bindClickEvents();
    }
    
    bindClickEvents() {
        // Install button click
        const installButton = document.getElementById('pwa-install-button');
        if (installButton) {
            installButton.addEventListener('click', () => {
                this.showInstallModal();
            });
        }
        
        // Modal events
        const modal = document.getElementById('pwa-install-modal');
        const confirmButton = document.getElementById('pwa-install-confirm');
        const cancelButton = document.getElementById('pwa-install-cancel');
        const closeButton = document.getElementById('pwa-modal-close');
        
        if (confirmButton) {
            confirmButton.addEventListener('click', () => {
                this.installApp();
            });
        }
        
        if (cancelButton) {
            cancelButton.addEventListener('click', () => {
                this.hideInstallModal();
            });
        }
        
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                this.hideInstallModal();
            });
        }
        
        // Close modal on backdrop click
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideInstallModal();
                }
            });
        }
        
        // Close modal on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideInstallModal();
            }
        });
    }
    
    showInstallButton() {
        const fab = document.getElementById('pwa-install-fab');
        if (fab && !this.isStandalone) {
            fab.style.display = 'block';
            
            // Track that we showed the install prompt
            this.trackInstallPromptShown();
        }
    }
    
    hideInstallButton() {
        const fab = document.getElementById('pwa-install-fab');
        if (fab) {
            fab.style.display = 'none';
        }
    }
    
    showInstallModal() {
        const modal = document.getElementById('pwa-install-modal');
        if (modal) {
            modal.style.display = 'flex';
            document.body.classList.add('pwa-modal-open');
            
            // Focus the confirm button for accessibility
            const confirmButton = document.getElementById('pwa-install-confirm');
            if (confirmButton) {
                setTimeout(() => confirmButton.focus(), 100);
            }
        }
    }
    
    hideInstallModal() {
        const modal = document.getElementById('pwa-install-modal');
        if (modal) {
            modal.style.display = 'none';
            document.body.classList.remove('pwa-modal-open');
        }
    }
    
    async installApp() {
        if (!this.deferredPrompt) {
            console.log('PWA: No deferred prompt available');
            this.showFallbackInstructions();
            return;
        }
        
        try {
            // Show the install prompt
            this.deferredPrompt.prompt();
            
            // Wait for the user to respond to the prompt
            const { outcome } = await this.deferredPrompt.userChoice;
            
            console.log(`PWA: User response to the install prompt: ${outcome}`);
            
            if (outcome === 'accepted') {
                console.log('PWA: User accepted the install prompt');
                this.trackInstallAccepted();
            } else {
                console.log('PWA: User dismissed the install prompt');
                this.trackInstallDismissed();
            }
            
            // Clear the deferred prompt
            this.deferredPrompt = null;
            
            // Hide the modal
            this.hideInstallModal();
            
        } catch (error) {
            console.error('PWA: Error during installation:', error);
            this.showFallbackInstructions();
        }
    }
    
    showFallbackInstructions() {
        // Show manual installation instructions for different browsers
        const userAgent = navigator.userAgent.toLowerCase();
        let instructions = '';
        
        if (userAgent.includes('chrome')) {
            instructions = 'To install this app:\n1. Click the menu (⋮) in the top right\n2. Select "Install KFT Admin"\n3. Click "Install" in the dialog';
        } else if (userAgent.includes('firefox')) {
            instructions = 'To install this app:\n1. Click the menu (☰) in the top right\n2. Select "Install this site as an app"\n3. Click "Install"';
        } else if (userAgent.includes('safari')) {
            instructions = 'To add this app to your home screen:\n1. Tap the Share button (□↗)\n2. Select "Add to Home Screen"\n3. Tap "Add"';
        } else {
            instructions = 'To install this app, look for an "Install" or "Add to Home Screen" option in your browser menu.';
        }
        
        alert(instructions);
        this.hideInstallModal();
    }
    
    showInstallSuccessMessage() {
        // Show a success message when the app is installed
        const message = document.createElement('div');
        message.className = 'pwa-install-success';
        message.innerHTML = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <strong>Success!</strong> KFT Admin app has been installed successfully.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // Insert at the top of the page content
        const container = document.querySelector('.container-fluid');
        if (container) {
            container.insertBefore(message, container.firstChild);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (message.parentNode) {
                    message.parentNode.removeChild(message);
                }
            }, 5000);
        }
    }
    
    // Analytics and tracking methods
    getVisitCount() {
        const count = localStorage.getItem('pwa-visit-count') || '0';
        const newCount = parseInt(count) + 1;
        localStorage.setItem('pwa-visit-count', newCount.toString());
        return newCount;
    }
    
    trackInstallPromptShown() {
        const timestamp = new Date().toISOString();
        localStorage.setItem('pwa-prompt-shown', timestamp);
        
        // Send analytics event if available
        if (typeof gtag !== 'undefined') {
            gtag('event', 'pwa_install_prompt_shown', {
                event_category: 'PWA',
                event_label: 'Install Prompt Shown'
            });
        }
    }
    
    trackInstallAccepted() {
        const timestamp = new Date().toISOString();
        localStorage.setItem('pwa-install-accepted', timestamp);
        
        // Send analytics event if available
        if (typeof gtag !== 'undefined') {
            gtag('event', 'pwa_install_accepted', {
                event_category: 'PWA',
                event_label: 'Install Accepted'
            });
        }
    }
    
    trackInstallDismissed() {
        const timestamp = new Date().toISOString();
        localStorage.setItem('pwa-install-dismissed', timestamp);
        
        // Send analytics event if available
        if (typeof gtag !== 'undefined') {
            gtag('event', 'pwa_install_dismissed', {
                event_category: 'PWA',
                event_label: 'Install Dismissed'
            });
        }
    }
}

// Initialize PWA installer when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PWAInstaller();
});

// Export for potential use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PWAInstaller;
}
