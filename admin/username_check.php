<?php
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';

// Initialize auth
$auth = new Auth();

// Check if user is logged in and has permission
if (!$auth->isLoggedIn() || (!$auth->hasRole('admin') && !$auth->hasRole('super_admin') && !$auth->hasRole('staff'))) {
    http_response_code(403);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get username from POST data
$username = isset($_POST['username']) ? trim($_POST['username']) : '';

if (empty($username)) {
    echo json_encode(['exists' => false]);
    exit;
}

try {
    $db = new Database();

    // Validate username format using centralized function
    $formatValidation = Utilities::validateUsername($username);
    if (!$formatValidation['valid']) {
        echo json_encode(['exists' => false, 'error' => $formatValidation['error']]);
        exit;
    }

    // Check if username exists
    $exists = $db->usernameExists($username);

    echo json_encode(['exists' => $exists]);
} catch (Exception $e) {
    error_log("Username check error: " . $e->getMessage());
    echo json_encode(['exists' => false, 'error' => 'Database error']);
}
?>
