<?php
/**
 * Setup script for secure login tokens
 * Run this once to create the necessary database table
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';

// Check if admin is logged in
$auth = new Auth();
if (!$auth->isLoggedIn() || !$auth->hasRole('admin')) {
    die('Access denied. Admin login required.');
}

$db = new Database();
$conn = $db->getConnection();

try {
    // Create secure_login_tokens table
    $createTableSQL = "
    CREATE TABLE IF NOT EXISTS secure_login_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token VARCHAR(128) NOT NULL UNIQUE,
        generated_by_admin INT NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        used_at TIMESTAMP NULL DEFAULT NULL,
        is_used BOOLEAN DEFAULT FALSE,
        ip_address VARCHAR(45) DEFAULT NULL,
        user_agent TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        -- Foreign key constraints
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (generated_by_admin) REFERENCES admin_users(id) ON DELETE CASCADE,
        
        -- Indexes for performance
        INDEX idx_token (token),
        INDEX idx_user_id (user_id),
        INDEX idx_expires_at (expires_at),
        INDEX idx_is_used (is_used),
        INDEX idx_created_at (created_at),
        INDEX idx_user_token (user_id, token),
        INDEX idx_active_tokens (user_id, is_used, expires_at)
    )";
    
    $conn->query($createTableSQL);
    echo "✅ secure_login_tokens table created successfully.<br>";
    
    // Create cleanup procedure
    $createProcedureSQL = "
    DROP PROCEDURE IF EXISTS CleanupExpiredLoginTokens;
    CREATE PROCEDURE CleanupExpiredLoginTokens()
    BEGIN
        DELETE FROM secure_login_tokens 
        WHERE (expires_at < NOW() OR is_used = TRUE) 
        AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR);
        
        SELECT ROW_COUNT() as cleaned_tokens;
    END";
    
    $conn->multi_query($createProcedureSQL);
    
    // Clear any remaining results
    do {
        if ($result = $conn->store_result()) {
            $result->free();
        }
    } while ($conn->next_result());
    
    echo "✅ CleanupExpiredLoginTokens procedure created successfully.<br>";
    
    // Create view for active tokens
    $createViewSQL = "
    CREATE OR REPLACE VIEW active_secure_login_tokens AS
    SELECT 
        slt.*,
        u.username,
        u.name as user_name,
        au.username as admin_username,
        au.name as admin_name
    FROM secure_login_tokens slt
    JOIN users u ON slt.user_id = u.id
    JOIN admin_users au ON slt.generated_by_admin = au.id
    WHERE slt.is_used = FALSE 
    AND slt.expires_at > NOW()";
    
    $conn->query($createViewSQL);
    echo "✅ active_secure_login_tokens view created successfully.<br>";
    
    echo "<br><strong>Setup completed successfully!</strong><br>";
    echo "You can now use the secure login token feature in user profiles.<br>";
    echo "<br><a href='users.php'>← Back to Users</a>";
    
} catch (Exception $e) {
    echo "❌ Error during setup: " . $e->getMessage() . "<br>";
    echo "Please check your database configuration and try again.<br>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Secure Login Tokens Setup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Secure Login Tokens Setup</h1>
    <p>This script sets up the database components needed for secure login tokens.</p>
</body>
</html>
