-- Secure Login Tokens Table
-- This table stores single-use secure login tokens for instant user access
-- Each token expires after one use or after a time limit for security

CREATE TABLE IF NOT EXISTS secure_login_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(128) NOT NULL UNIQUE,
    generated_by_admin INT NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL DEFAULT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (generated_by_admin) REFERENCES admin_users(id) ON DELETE CASCADE,
    
    -- Indexes for performance
    INDEX idx_token (token),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_used (is_used),
    INDEX idx_created_at (created_at),
    INDEX idx_user_token (user_id, token),
    INDEX idx_active_tokens (user_id, is_used, expires_at)
);

-- Add admin action logging for secure login token generation
-- This will be logged in the existing admin_action_logs table
-- Action types: 'secure_login_token_generated', 'secure_login_token_used'

-- Create a stored procedure to clean up expired tokens
DELIMITER //
CREATE PROCEDURE CleanupExpiredLoginTokens()
BEGIN
    -- Delete tokens that are expired or used and older than 24 hours
    DELETE FROM secure_login_tokens 
    WHERE (expires_at < NOW() OR is_used = TRUE) 
    AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR);
    
    SELECT ROW_COUNT() as cleaned_tokens;
END //
DELIMITER ;

-- Create a view for active (unused and not expired) tokens
CREATE OR REPLACE VIEW active_secure_login_tokens AS
SELECT 
    slt.*,
    u.username,
    u.name as user_name,
    au.username as admin_username,
    au.name as admin_name
FROM secure_login_tokens slt
JOIN users u ON slt.user_id = u.id
JOIN admin_users au ON slt.generated_by_admin = au.id
WHERE slt.is_used = FALSE 
AND slt.expires_at > NOW();
