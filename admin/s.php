<?php
/**
 * Short URL Redirect Handler
 * Handles redirection from short URLs to their original destinations
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/url_shortener.php';

// Get the short code from the URL
$shortCode = $_GET['c'] ?? '';

if (empty($shortCode)) {
    http_response_code(404);
    die('Short URL not found');
}

$db = new Database();
$conn = $db->getConnection();
$urlShortener = new UrlShortener($conn);

// Resolve the short URL
$result = $urlShortener->resolveShortUrl($shortCode);

if (!$result['success']) {
    http_response_code(404);
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Link Not Found - KFT Fitness</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: 'Inter', sans-serif;
            }
            .error-container {
                background: white;
                border-radius: 20px;
                padding: 3rem;
                text-align: center;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                max-width: 500px;
                width: 90%;
            }
            .error-icon {
                font-size: 4rem;
                color: #dc3545;
                margin-bottom: 1rem;
            }
            .error-title {
                font-size: 1.5rem;
                font-weight: 600;
                color: #333;
                margin-bottom: 1rem;
            }
            .error-message {
                color: #666;
                margin-bottom: 2rem;
                line-height: 1.6;
            }
            .btn-home {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                padding: 12px 30px;
                border-radius: 25px;
                color: white;
                text-decoration: none;
                font-weight: 500;
                transition: transform 0.3s ease;
            }
            .btn-home:hover {
                transform: translateY(-2px);
                color: white;
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-icon">
                <i class="fas fa-link-slash"></i>
            </div>
            <h1 class="error-title">Link Not Found</h1>
            <p class="error-message">
                <?php echo htmlspecialchars($result['error']); ?><br>
                The link you're looking for may have expired or been removed.
            </p>
            <a href="../" class="btn-home">
                <i class="fas fa-home me-2"></i>Go to Homepage
            </a>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Log the access
$ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
$userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;

$logQuery = "INSERT INTO short_url_logs (short_code, action, ip_address, user_agent, created_at) VALUES (?, 'accessed', ?, ?, NOW())";
$stmt = $conn->prepare($logQuery);
$stmt->bind_param("sss", $shortCode, $ipAddress, $userAgent);
$stmt->execute();

// Redirect to the long URL
header("Location: " . $result['long_url']);
exit;
?>
