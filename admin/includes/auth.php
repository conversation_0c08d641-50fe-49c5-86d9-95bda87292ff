<?php
require_once 'database.php';

class Auth {
    private $db;

    public function __construct() {
        $this->db = new Database();

        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_name(SESSION_NAME);
            session_start();
        }
    }

    // Check if user is logged in
    public function isLoggedIn() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }

    // Login user
    public function login($username, $password) {
        // Removed DEV_MODE bypass for production

        $conn = $this->db->getConnection();

        // First try admin_users table - check by username or email
        $stmt = $conn->prepare("SELECT id, username, email, password, role, name FROM admin_users WHERE username = ? OR email = ? LIMIT 1");
        $stmt->bind_param("ss", $username, $username);
        $stmt->execute();
        $result = $stmt->get_result();

        // Log the query result
        error_log("Admin users query for '$username': found " . $result->num_rows . " results");

        if ($result->num_rows === 1) {
            $user = $result->fetch_assoc();

            // Verify password
            if (password_verify($password, $user['password'])) {
                // Log successful password verification
                error_log("Password verification successful for user: {$user['username']} (ID: {$user['id']}, Role: {$user['role']})");

                // Set session variables
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['name'] = $user['name'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['is_admin'] = ($user['role'] === 'admin' || $user['role'] === 'super_admin') ? 1 : 0;
                $_SESSION['is_staff'] = ($user['role'] === 'staff') ? 1 : 0;
                $_SESSION['last_activity'] = time();

                // Log session variables
                error_log("Session variables set: user_id={$_SESSION['user_id']}, username={$_SESSION['username']}, role={$_SESSION['role']}, is_admin={$_SESSION['is_admin']}, is_staff={$_SESSION['is_staff']}");

                // Update last login time for admin users
                $updateStmt = $conn->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = ?");
                $updateStmt->bind_param("i", $user['id']);
                $updateStmt->execute();

                return true;
            } else {
                // Log failed password verification
                error_log("Password verification failed for user: {$user['username']} (ID: {$user['id']}, Role: {$user['role']})");
            }
        }

        // If not found in admin_users, try regular users table
        $stmt = $conn->prepare("SELECT id, username, password, is_premium FROM users WHERE username = ? AND is_active = 1 LIMIT 1");
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 1) {
            $user = $result->fetch_assoc();

            // Verify password
            if (password_verify($password, $user['password'])) {
                // Set session variables
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['is_premium'] ? 'premium_user' : 'user';
                $_SESSION['last_activity'] = time();
                $_SESSION['user_type'] = 'regular';

                // Update last login time for regular users
                $updateStmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                $updateStmt->bind_param("i", $user['id']);
                $updateStmt->execute();

                return true;
            }
        }

        return false;
    }

    // Logout user
    public function logout() {
        // Unset all session variables
        $_SESSION = array();

        // Delete the session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }

        // Destroy the session
        session_destroy();
    }

    // Register new admin user
    public function register($username, $password, $email, $role = 'editor', $name = null) {
        $conn = $this->db->getConnection();

        // Hash password
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        // Use username as name if name is not provided
        if (empty($name)) {
            $name = ucfirst($username);
        }

        // Prepare statement
        $stmt = $conn->prepare("INSERT INTO admin_users (username, password, email, name, role, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())");
        $stmt->bind_param("sssss", $username, $hashedPassword, $email, $name, $role);

        return $stmt->execute();
    }

    // Check if user has required role
    public function hasRole($requiredRole) {
        if (!$this->isLoggedIn()) {
            return false;
        }

        // Check if user has the required role
        if ($_SESSION['role'] === $requiredRole) {
            return true;
        }

        // Admin and super_admin roles have access to everything
        if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super_admin') {
            return true;
        }

        // Premium users have access to premium content
        if ($_SESSION['role'] === 'premium_user' && $requiredRole === 'premium') {
            return true;
        }

        return false;
    }

    // Check if user has specific permission
    public function hasPermission($permissionSlug) {
        if (!$this->isLoggedIn()) {
            return false;
        }

        // Admin and super_admin roles have all permissions
        if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super_admin') {
            return true;
        }

        // For staff members, check the permission in the database
        if ($_SESSION['role'] === 'staff') {
            $conn = $this->db->getConnection();

            // First, check if permissions_locked column exists
            $checkColumnQuery = "SHOW COLUMNS FROM admin_users LIKE 'permissions_locked'";
            $checkColumnResult = $conn->query($checkColumnQuery);
            $permissionsLockedExists = ($checkColumnResult && $checkColumnResult->num_rows > 0);

            if ($permissionsLockedExists) {
                // Check if staff permissions are locked (override by super admin)
                $lockQuery = "SELECT permissions_locked FROM admin_users WHERE id = ?";
                $lockStmt = $conn->prepare($lockQuery);
                $userId = $_SESSION['user_id'];
                $lockStmt->bind_param("i", $userId);
                $lockStmt->execute();
                $lockResult = $lockStmt->get_result();

                if ($lockResult && $lockResult->num_rows > 0) {
                    $lockRow = $lockResult->fetch_assoc();
                    if ($lockRow['permissions_locked']) {
                        // If permissions are locked, deny all write permissions
                        // Allow only read-only permissions
                        $readOnlyPermissions = ['view_users', 'view_courses', 'view_reports'];
                        if (!in_array($permissionSlug, $readOnlyPermissions)) {
                            return false;
                        }
                    }
                }
            }

            // If not locked or is a read-only permission, check if the staff has the permission
            $query = "SELECT COUNT(*) as has_permission
                      FROM admin_user_permissions up
                      JOIN admin_permissions p ON up.permission_id = p.id
                      WHERE up.admin_user_id = ? AND p.slug = ?";

            $stmt = $conn->prepare($query);
            $stmt->bind_param("is", $userId, $permissionSlug);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result && $result->num_rows > 0) {
                $row = $result->fetch_assoc();
                return $row['has_permission'] > 0;
            }
        }

        return false;
    }

    /**
     * Check if current user has permission to access a page
     *
     * @param string $requiredPermission The permission slug required to access the page
     * @param bool $redirectOnFailure Whether to redirect to unauthorized page on failure
     * @return bool Whether the user has permission
     */
    public function checkPagePermission($requiredPermission, $redirectOnFailure = true) {
        // If no permission is required, allow access
        if (empty($requiredPermission)) {
            return true;
        }

        // Check if user has the required permission
        $hasPermission = $this->hasPermission($requiredPermission);

        // If user doesn't have permission and redirect is enabled, redirect to unauthorized page
        if (!$hasPermission && $redirectOnFailure) {
            Utilities::setFlashMessage('error', 'You do not have permission to access this page.');
            Utilities::redirect('unauthorized.php');
            exit;
        }

        return $hasPermission;
    }

    /**
     * Get all permissions for the current user as an associative array
     *
     * @return array Associative array of permission slugs => true
     */
    public function getPermissionsArray() {
        $permissions = [];

        // Admin and super_admin roles have all permissions
        if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super_admin') {
            $conn = $this->db->getConnection();
            $query = "SELECT slug FROM admin_permissions";
            $result = $conn->query($query);

            if ($result && $result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    $permissions[$row['slug']] = true;
                }
            }

            return $permissions;
        }

        // For staff members, get assigned permissions
        if ($_SESSION['role'] === 'staff') {
            $conn = $this->db->getConnection();

            $query = "SELECT p.slug
                      FROM admin_user_permissions up
                      JOIN admin_permissions p ON up.permission_id = p.id
                      WHERE up.admin_user_id = ?";

            $stmt = $conn->prepare($query);
            $userId = $_SESSION['user_id'];
            $stmt->bind_param("i", $userId);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result && $result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    $permissions[$row['slug']] = true;
                }
            }
        }

        return $permissions;
    }

    // Get all permissions for current user
    public function getUserPermissions() {
        if (!$this->isLoggedIn()) {
            return [];
        }

        // Admin and super_admin roles have all permissions
        if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super_admin') {
            $conn = $this->db->getConnection();
            $query = "SELECT id, name, slug, description FROM admin_permissions";
            $result = $conn->query($query);

            $permissions = [];
            if ($result && $result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    $permissions[] = $row;
                }
            }

            return $permissions;
        }

        // For staff members, get assigned permissions
        if ($_SESSION['role'] === 'staff') {
            $conn = $this->db->getConnection();

            $query = "SELECT p.id, p.name, p.slug, p.description
                      FROM admin_user_permissions up
                      JOIN admin_permissions p ON up.permission_id = p.id
                      WHERE up.admin_user_id = ?";

            $stmt = $conn->prepare($query);
            $userId = $_SESSION['user_id'];
            $stmt->bind_param("i", $userId);
            $stmt->execute();
            $result = $stmt->get_result();

            $permissions = [];
            if ($result && $result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    $permissions[] = $row;
                }
            }

            return $permissions;
        }

        return [];
    }

    // Get current user ID
    public function getUserId() {
        return $_SESSION['user_id'] ?? null;
    }

    // Get current username
    public function getUsername() {
        return $_SESSION['username'] ?? null;
    }

    // Get current user role
    public function getUserRole() {
        return $_SESSION['role'] ?? null;
    }

    // Check if current user is a regular user (not admin)
    public function isRegularUser() {
        return isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'regular';
    }

    // Check session timeout
    public function checkSessionTimeout() {
        if ($this->isLoggedIn()) {
            $currentTime = time();
            $lastActivity = $_SESSION['last_activity'] ?? 0;

            if (($currentTime - $lastActivity) > SESSION_LIFETIME) {
                $this->logout();
                return false;
            }

            // Update last activity time
            $_SESSION['last_activity'] = $currentTime;
        }

        return true;
    }

    // Authenticate API token
    public function authenticateToken() {
        // Get authorization header
        $headers = getallheaders();
        $authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';

        // Check if token is provided
        if (empty($authHeader) || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            // For development mode, allow a test user
            if (defined('DEV_MODE') && DEV_MODE === true) {
                return 1; // Return user ID 1 for testing
            }
            return false;
        }

        $token = $matches[1];
        $conn = $this->db->getConnection();

        // Check if token exists and is valid
        $query = "SELECT t.*, u.id as user_id, u.username, u.name, u.is_premium, u.is_active
                  FROM api_tokens t
                  JOIN users u ON t.user_id = u.id
                  WHERE t.token = ? AND t.expires_at > NOW()";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("s", $token);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            // For development mode, allow a test user
            if (defined('DEV_MODE') && DEV_MODE === true) {
                return 1; // Return user ID 1 for testing
            }
            return false;
        }

        $tokenData = $result->fetch_assoc();

        // Check if user is active
        if (!$tokenData['is_active']) {
            return false;
        }

        return $tokenData['user_id'];
    }

    // Validate API token and return user ID
    public function validateToken($token) {
        if (empty($token)) {
            // For development mode, allow a test user
            if (defined('DEV_MODE') && DEV_MODE === true) {
                error_log("DEV_MODE is enabled, returning user ID 1 for empty token");
                return 1; // Return user ID 1 for testing
            }
            error_log("Token is empty, authentication failed");
            return false;
        }

        // First try JWT validation using the improved JWT library
        try {
            require_once __DIR__ . '/jwt.php';
            $secret = defined('APP_SECRET') ? APP_SECRET : 'your-secret-key';

            error_log("=== JWT VALIDATION ATTEMPT ===");
            error_log("Token (first 50 chars): " . substr($token, 0, 50) . "...");
            error_log("Secret (first 10 chars): " . substr($secret, 0, 10) . "...");
            error_log("Secret defined: " . (defined('APP_SECRET') ? 'YES' : 'NO'));
            error_log("Full secret: " . $secret);

            $payload = validate_jwt($token, $secret);

            if ($payload !== false) {
                $userId = $payload['user_id'];
                error_log("✅ JWT validation successful, checking user status for ID: " . $userId);

                // Verify user exists and is active
                $conn = $this->db->getConnection();
                $query = "SELECT id, is_active FROM users WHERE id = ? LIMIT 1";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("i", $userId);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($result->num_rows > 0) {
                    $user = $result->fetch_assoc();
                    if ($user['is_active']) {
                        error_log("✅ JWT token validated successfully for user ID: " . $userId);
                        return $userId;
                    } else {
                        error_log("❌ User is not active: " . $userId);
                        return false;
                    }
                } else {
                    error_log("❌ User not found in database: " . $userId);
                    return false;
                }
            } else {
                error_log("❌ JWT validation failed for token: " . substr($token, 0, 20) . "...");
            }
            error_log("=== END JWT VALIDATION ATTEMPT ===");
        } catch (Exception $e) {
            error_log("❌ JWT validation error: " . $e->getMessage());
        }

        // Fall back to database token validation
        $conn = $this->db->getConnection();

        // Check if token exists and is valid
        $query = "SELECT t.*, u.id as user_id, u.username, u.name, u.is_premium, u.is_active
                  FROM api_tokens t
                  JOIN users u ON t.user_id = u.id
                  WHERE t.token = ? AND t.expires_at > NOW()";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("s", $token);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            // For development mode, allow a test user
            if (defined('DEV_MODE') && DEV_MODE === true) {
                error_log("DEV_MODE is enabled, returning user ID 1 for invalid token: " . substr($token, 0, 20) . "...");
                return 1; // Return user ID 1 for testing
            }
            error_log("Token not found or expired: " . substr($token, 0, 20) . "...");
            return false;
        }

        $tokenData = $result->fetch_assoc();

        // Check if user is active
        if (!$tokenData['is_active']) {
            error_log("User is not active: " . $tokenData['user_id']);
            return false;
        }

        error_log("Database token validated successfully for user ID: " . $tokenData['user_id']);
        return $tokenData['user_id'];
    }
}
