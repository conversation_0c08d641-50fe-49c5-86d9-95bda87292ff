<?php
require_once 'config.php';

class Database {
    private $conn;
    
    // Constructor - establishes database connection
    public function __construct() {
        try {
            $this->conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
            
            if ($this->conn->connect_error) {
                throw new Exception("Connection failed: " . $this->conn->connect_error);
            }
            
            $this->conn->set_charset("utf8mb4");
        } catch (Exception $e) {
            error_log("Database connection error: " . $e->getMessage());
            die("Database connection failed. Please try again later.");
        }
    }
    
    // Get database connection
    public function getConnection() {
        return $this->conn;
    }
    
    // Execute a query
    public function query($sql) {
        return $this->conn->query($sql);
    }
    
    // Execute a prepared statement
    public function prepare($sql) {
        return $this->conn->prepare($sql);
    }
    
    // Get the last inserted ID
    public function getLastId() {
        return $this->conn->insert_id;
    }
    
    // Escape string for security
    public function escapeString($string) {
        return $this->conn->real_escape_string($string);
    }
    
    // Close the database connection
    public function __destruct() {
        if ($this->conn) {
            $this->conn->close();
        }
    }
    
    // Get user by username
    public function getUserByUsername($username) {
        $query = "SELECT * FROM users WHERE username = ? LIMIT 1";
        $stmt = $this->conn->prepare($query);
        if (!$stmt) return null;

        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        $stmt->close();

        return $user ? $user : null;
    }

    // Get user by phone number (checks all common formats) - kept for backward compatibility
    public function getUserByPhone($phone) {
        require_once 'utilities.php';

        // Get all possible variations of the phone number
        $variations = Utilities::getPhoneNumberVariations($phone);

        if (empty($variations)) {
            return null;
        }

        // Build dynamic query with placeholders for all variations
        $placeholders = str_repeat('?,', count($variations) * 2);
        $placeholders = rtrim($placeholders, ',');

        $query = "SELECT * FROM users WHERE ";
        $conditions = [];
        $params = [];

        foreach ($variations as $variation) {
            $conditions[] = "phone = ? OR phone_number = ?";
            $params[] = $variation;
            $params[] = $variation;
        }

        $query .= implode(' OR ', $conditions) . " LIMIT 1";

        $stmt = $this->conn->prepare($query);
        if (!$stmt) return null;

        // Create type string (all strings)
        $types = str_repeat('s', count($params));
        $stmt->bind_param($types, ...$params);

        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        $stmt->close();

        return $user ? $user : null;
    }

    // Check if username exists
    public function usernameExists($username, $excludeUserId = null) {
        $query = "SELECT id FROM users WHERE username = ?";
        $params = [$username];
        $types = "s";

        if ($excludeUserId) {
            $query .= " AND id != ?";
            $params[] = $excludeUserId;
            $types .= "i";
        }

        $stmt = $this->conn->prepare($query);
        if (!$stmt) return false;

        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        $exists = $result->num_rows > 0;
        $stmt->close();

        return $exists;
    }
    
    // Update user's device_id
    public function updateUserDeviceId($userId, $deviceId) {
        $stmt = $this->conn->prepare("UPDATE users SET device_id = ? WHERE id = ?");
        if (!$stmt) return false;
        $stmt->bind_param("si", $deviceId, $userId);
        $result = $stmt->execute();
        $stmt->close();
        return $result;
    }

    // Update user's last_login timestamp
    public function updateUserLastLogin($userId) {
        $stmt = $this->conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        if (!$stmt) return false;
        $stmt->bind_param("i", $userId);
        $result = $stmt->execute();
        $stmt->close();
        return $result;
    }
}
