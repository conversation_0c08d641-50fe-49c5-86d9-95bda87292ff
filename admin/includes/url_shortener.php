<?php
/**
 * URL Shortener Service
 * Creates short URLs for secure login links
 */

class UrlShortener {
    private $conn;
    private $baseUrl;
    
    public function __construct($connection) {
        $this->conn = $connection;
        $this->baseUrl = $this->getBaseUrl();
    }
    
    /**
     * Create a short URL for a given long URL
     */
    public function createShortUrl($longUrl, $userId = null, $expiresAt = null) {
        // Generate a unique short code
        $shortCode = $this->generateShortCode();
        
        // Ensure uniqueness
        while ($this->shortCodeExists($shortCode)) {
            $shortCode = $this->generateShortCode();
        }
        
        // Insert into database
        $query = "INSERT INTO short_urls (short_code, long_url, user_id, expires_at, created_at) VALUES (?, ?, ?, ?, NOW())";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("ssis", $shortCode, $longUrl, $userId, $expiresAt);
        
        if ($stmt->execute()) {
            $shortUrl = $this->baseUrl . '/s/' . $shortCode;
            
            // Log the creation
            $this->logShortUrlCreation($shortCode, $longUrl, $userId);
            
            return [
                'success' => true,
                'short_code' => $shortCode,
                'short_url' => $shortUrl,
                'long_url' => $longUrl
            ];
        }
        
        return ['success' => false, 'error' => 'Failed to create short URL'];
    }
    
    /**
     * Resolve a short code to its long URL
     */
    public function resolveShortUrl($shortCode) {
        $query = "SELECT long_url, user_id, expires_at, is_active FROM short_urls WHERE short_code = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("s", $shortCode);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            return ['success' => false, 'error' => 'Short URL not found'];
        }
        
        $row = $result->fetch_assoc();
        
        // Check if URL is active
        if (!$row['is_active']) {
            return ['success' => false, 'error' => 'Short URL has been deactivated'];
        }
        
        // Check if URL has expired
        if ($row['expires_at'] && strtotime($row['expires_at']) < time()) {
            return ['success' => false, 'error' => 'Short URL has expired'];
        }
        
        // Update click count
        $this->incrementClickCount($shortCode);
        
        return [
            'success' => true,
            'long_url' => $row['long_url'],
            'user_id' => $row['user_id']
        ];
    }
    
    /**
     * Generate a random short code
     */
    private function generateShortCode($length = 6) {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $shortCode = '';
        
        for ($i = 0; $i < $length; $i++) {
            $shortCode .= $characters[random_int(0, strlen($characters) - 1)];
        }
        
        return $shortCode;
    }
    
    /**
     * Check if short code already exists
     */
    private function shortCodeExists($shortCode) {
        $query = "SELECT id FROM short_urls WHERE short_code = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("s", $shortCode);
        $stmt->execute();
        $result = $stmt->get_result();
        
        return $result->num_rows > 0;
    }
    
    /**
     * Increment click count for analytics
     */
    private function incrementClickCount($shortCode) {
        $query = "UPDATE short_urls SET click_count = click_count + 1, last_accessed = NOW() WHERE short_code = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("s", $shortCode);
        $stmt->execute();
    }
    
    /**
     * Get base URL for the application
     */
    private function getBaseUrl() {
        $protocol = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $path = dirname($_SERVER['REQUEST_URI']);
        
        // Remove /admin from path if present
        $path = str_replace('/admin', '', $path);
        
        return $protocol . '://' . $host . $path;
    }
    
    /**
     * Log short URL creation for analytics
     */
    private function logShortUrlCreation($shortCode, $longUrl, $userId) {
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        
        $query = "INSERT INTO short_url_logs (short_code, action, ip_address, user_agent, created_at) VALUES (?, 'created', ?, ?, NOW())";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("sss", $shortCode, $ipAddress, $userAgent);
        $stmt->execute();
    }
    
    /**
     * Deactivate a short URL
     */
    public function deactivateShortUrl($shortCode) {
        $query = "UPDATE short_urls SET is_active = 0 WHERE short_code = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("s", $shortCode);
        
        return $stmt->execute();
    }
    
    /**
     * Get analytics for a short URL
     */
    public function getAnalytics($shortCode) {
        $query = "SELECT short_code, long_url, click_count, created_at, last_accessed FROM short_urls WHERE short_code = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("s", $shortCode);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            return ['success' => false, 'error' => 'Short URL not found'];
        }
        
        return [
            'success' => true,
            'data' => $result->fetch_assoc()
        ];
    }
    
    /**
     * Clean up expired URLs
     */
    public function cleanupExpiredUrls() {
        $query = "UPDATE short_urls SET is_active = 0 WHERE expires_at < NOW() AND is_active = 1";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt->affected_rows;
    }
}
