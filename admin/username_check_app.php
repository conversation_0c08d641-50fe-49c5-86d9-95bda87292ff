<?php
// Enable CORS for Flutter app - comprehensive headers
header('Access-Control-Allow-Origin: http://localhost:8080');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Accept, Origin, Authorization, X-Requested-With, sec-ch-ua, sec-ch-ua-mobile, sec-ch-ua-platform, User-Agent');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Max-Age: 86400'); // 24 hours
header('Content-Type: application/json; charset=utf-8');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    error_log("CORS preflight request received for username check");
    http_response_code(200);
    exit;
}

require_once 'includes/config.php';
require_once 'includes/database.php';

// Enable error logging for debugging
error_log("=== USERNAME CHECK APP DEBUG ===");
error_log("Request method: " . $_SERVER['REQUEST_METHOD']);
error_log("POST data: " . print_r($_POST, true));
error_log("Headers: " . print_r(getallheaders(), true));

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    error_log("Method not allowed: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get username from POST data
$username = isset($_POST['username']) ? trim($_POST['username']) : '';
$deviceId = isset($_POST['device_id']) ? trim($_POST['device_id']) : '';

error_log("Username received: '$username'");
error_log("Device ID received: '$deviceId'");

if (empty($username)) {
    error_log("Empty username provided");
    echo json_encode(['exists' => false, 'error' => 'Username is required']);
    exit;
}

// Validate username format using centralized function
$formatValidation = Utilities::validateUsername($username);
if (!$formatValidation['valid']) {
    error_log("Invalid username format: '$username' - " . $formatValidation['error']);
    echo json_encode(['exists' => false, 'error' => $formatValidation['error']]);
    exit;
}

try {
    $db = new Database();
    error_log("Database connection established");

    // Check if username exists
    $user = $db->getUserByUsername($username);
    error_log("User lookup result: " . ($user ? "Found user ID " . $user['id'] : "User not found"));

    if (!$user) {
        error_log("Username '$username' does not exist");
        echo json_encode(['exists' => false]);
        exit;
    }

    // Check device registration if device_id is provided
    if (!empty($deviceId) && !empty($user['device_id']) && $user['device_id'] !== $deviceId) {
        error_log("Device already registered. User device: " . $user['device_id'] . ", Request device: $deviceId");
        echo json_encode(['error' => 'DEVICE_ALREADY_REGISTERED']);
        exit;
    }

    error_log("Username check successful - user exists and device is valid");
    $response = ['exists' => true, 'user_id' => $user_id, 'timestamp' => date('Y-m-d H:i:s')];
    error_log("Sending response: " . json_encode($response));
    echo json_encode($response);
} catch (Exception $e) {
    error_log("Username check error: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    echo json_encode(['exists' => false, 'error' => 'Database error: ' . $e->getMessage()]);
}
?>
