<?php
/**
 * Cleanup script for expired secure login tokens
 * This can be run manually or set up as a cron job
 */

require_once 'includes/config.php';
require_once 'includes/database.php';

// Allow running from command line or web
$isCLI = php_sapi_name() === 'cli';

if (!$isCLI) {
    // If running from web, require admin authentication
    require_once 'includes/auth.php';
    $auth = new Auth();
    if (!$auth->isLoggedIn() || !$auth->hasRole('admin')) {
        die('Access denied. Admin login required.');
    }
}

$db = new Database();
$conn = $db->getConnection();

try {
    // Clean up expired and used tokens older than 24 hours
    $cleanupQuery = "
        DELETE FROM secure_login_tokens 
        WHERE (expires_at < NOW() OR is_used = TRUE) 
        AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ";
    
    $result = $conn->query($cleanupQuery);
    $cleanedCount = $conn->affected_rows;
    
    // Get current statistics
    $statsQuery = "
        SELECT 
            COUNT(*) as total_tokens,
            SUM(CASE WHEN is_used = FALSE AND expires_at > NOW() THEN 1 ELSE 0 END) as active_tokens,
            SUM(CASE WHEN is_used = TRUE THEN 1 ELSE 0 END) as used_tokens,
            SUM(CASE WHEN expires_at <= NOW() THEN 1 ELSE 0 END) as expired_tokens
        FROM secure_login_tokens
    ";
    
    $statsResult = $conn->query($statsQuery);
    $stats = $statsResult->fetch_assoc();
    
    $message = "Secure Login Tokens Cleanup Report\n";
    $message .= "=====================================\n";
    $message .= "Cleaned up: {$cleanedCount} expired/used tokens\n";
    $message .= "Current statistics:\n";
    $message .= "- Total tokens: {$stats['total_tokens']}\n";
    $message .= "- Active tokens: {$stats['active_tokens']}\n";
    $message .= "- Used tokens: {$stats['used_tokens']}\n";
    $message .= "- Expired tokens: {$stats['expired_tokens']}\n";
    $message .= "Cleanup completed at: " . date('Y-m-d H:i:s') . "\n";
    
    if ($isCLI) {
        echo $message;
    } else {
        echo "<pre>" . htmlspecialchars($message) . "</pre>";
        echo "<br><a href='users.php'>← Back to Users</a>";
    }
    
    // Log the cleanup action
    if (!$isCLI) {
        $logQuery = "INSERT INTO admin_action_logs 
                     (admin_user_id, admin_username, action_type, action_details, ip_address) 
                     VALUES (?, ?, 'secure_login_tokens_cleanup', ?, ?)";
        
        $actionDetails = json_encode([
            'cleaned_count' => $cleanedCount,
            'total_tokens' => $stats['total_tokens'],
            'active_tokens' => $stats['active_tokens'],
            'cleanup_timestamp' => date('Y-m-d H:i:s')
        ]);
        
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
        $adminId = $auth->getUserId();
        $adminUsername = $auth->getUsername();
        
        $stmt = $conn->prepare($logQuery);
        $stmt->bind_param("issss", $adminId, $adminUsername, $actionDetails, $ipAddress);
        $stmt->execute();
    }
    
} catch (Exception $e) {
    $errorMessage = "Error during cleanup: " . $e->getMessage();
    if ($isCLI) {
        echo $errorMessage . "\n";
    } else {
        echo "<div style='color: red;'>" . htmlspecialchars($errorMessage) . "</div>";
    }
    exit(1);
}
?>

<?php if (!$isCLI): ?>
<!DOCTYPE html>
<html>
<head>
    <title>Secure Login Tokens Cleanup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Secure Login Tokens Cleanup</h1>
    <p>This script removes expired and used secure login tokens older than 24 hours.</p>
</body>
</html>
<?php endif; ?>
