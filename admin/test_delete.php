<?php
require_once 'includes/header.php';

// Simple test to check if delete action is working
if (isset($_GET['test_delete'])) {
    echo "<h2>Delete Test Results</h2>";
    echo "<p>Delete parameter: " . htmlspecialchars($_GET['test_delete']) . "</p>";
    echo "<p>Current user role: " . htmlspecialchars($auth->getUserRole()) . "</p>";
    echo "<p>Current user ID: " . htmlspecialchars($auth->getUserId()) . "</p>";
    echo "<p>Is admin: " . ($auth->hasRole('admin') ? 'Yes' : 'No') . "</p>";
    echo "<p>Is super_admin: " . ($auth->hasRole('super_admin') ? 'Yes' : 'No') . "</p>";
    echo "<p>Is staff: " . ($auth->hasRole('staff') ? 'Yes' : 'No') . "</p>";
    echo "<p><a href='users.php?delete=" . htmlspecialchars($_GET['test_delete']) . "'>Test Delete Link</a></p>";
    exit;
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Delete Test</title>
</head>
<body>
    <h1>Delete Functionality Test</h1>
    <p>This page helps debug the delete functionality.</p>
    
    <form method="GET">
        <label>User ID to test delete: <input type="number" name="test_delete" value="2"></label>
        <button type="submit">Test Delete</button>
    </form>
    
    <h2>Current Session Info</h2>
    <pre><?php print_r($_SESSION); ?></pre>
    
    <h2>Role Tests</h2>
    <p>hasRole('admin'): <?php echo $auth->hasRole('admin') ? 'true' : 'false'; ?></p>
    <p>hasRole('super_admin'): <?php echo $auth->hasRole('super_admin') ? 'true' : 'false'; ?></p>
    <p>hasRole('staff'): <?php echo $auth->hasRole('staff') ? 'true' : 'false'; ?></p>
    
    <p><a href="users.php">Back to Users</a></p>
</body>
</html> 