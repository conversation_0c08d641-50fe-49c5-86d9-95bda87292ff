<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Test - KFT Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#27ae60">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="KFT Admin">
    
    <!-- <PERSON><PERSON> Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- PWA Install CSS -->
    <link rel="stylesheet" href="assets/css/pwa-install.css">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Inter', sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .test-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-pass {
            background: #d1eddb;
            color: #155724;
        }
        
        .status-fail {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .feature-demo {
            margin-top: 20px;
        }
        
        .demo-button {
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <h1 class="h3 mb-4">
                <i class="fas fa-mobile-alt me-2 text-success"></i>
                PWA Test Dashboard
            </h1>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                This page tests the PWA functionality for KFT Admin. Open this page in a modern browser to test installation.
            </div>
        </div>
        
        <div class="test-card">
            <h2 class="h5 mb-3">PWA Requirements Check</h2>
            
            <div id="test-results">
                <div class="test-item">
                    <span>Service Worker Support</span>
                    <span id="sw-support" class="test-status">Checking...</span>
                </div>
                
                <div class="test-item">
                    <span>Manifest File</span>
                    <span id="manifest-check" class="test-status">Checking...</span>
                </div>
                
                <div class="test-item">
                    <span>HTTPS/Localhost</span>
                    <span id="https-check" class="test-status">Checking...</span>
                </div>
                
                <div class="test-item">
                    <span>Service Worker Registration</span>
                    <span id="sw-registration" class="test-status">Checking...</span>
                </div>
                
                <div class="test-item">
                    <span>Install Prompt Available</span>
                    <span id="install-prompt" class="test-status">Waiting...</span>
                </div>
                
                <div class="test-item">
                    <span>Standalone Mode</span>
                    <span id="standalone-mode" class="test-status">Checking...</span>
                </div>
            </div>
        </div>
        
        <div class="test-card">
            <h2 class="h5 mb-3">PWA Features Demo</h2>
            
            <div class="feature-demo">
                <button id="test-install" class="btn btn-primary demo-button">
                    <i class="fas fa-download me-2"></i>Test Install Prompt
                </button>
                
                <button id="test-notification" class="btn btn-info demo-button">
                    <i class="fas fa-bell me-2"></i>Test Notification
                </button>
                
                <button id="test-offline" class="btn btn-warning demo-button">
                    <i class="fas fa-wifi-slash me-2"></i>Test Offline Mode
                </button>
                
                <button id="clear-cache" class="btn btn-secondary demo-button">
                    <i class="fas fa-trash me-2"></i>Clear Cache
                </button>
            </div>
            
            <div id="demo-results" class="mt-3"></div>
        </div>
        
        <div class="test-card">
            <h2 class="h5 mb-3">Installation Instructions</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <h6>Chrome/Edge:</h6>
                    <ol class="small">
                        <li>Look for the install icon in the address bar</li>
                        <li>Or use the menu → "Install KFT Admin"</li>
                        <li>Click "Install" in the dialog</li>
                    </ol>
                </div>
                
                <div class="col-md-6">
                    <h6>Mobile Safari:</h6>
                    <ol class="small">
                        <li>Tap the Share button</li>
                        <li>Select "Add to Home Screen"</li>
                        <li>Tap "Add"</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
    
    <!-- PWA Install FAB Button -->
    <div id="pwa-install-fab" class="pwa-install-fab" style="display: none;">
        <button id="pwa-install-button" class="pwa-install-button" title="Install KFT Admin App">
            <i class="fas fa-download"></i>
            <span class="pwa-install-text">Install App</span>
        </button>
    </div>

    <!-- PWA Install Modal -->
    <div id="pwa-install-modal" class="pwa-modal" style="display: none;">
        <div class="pwa-modal-content">
            <div class="pwa-modal-header">
                <h3>Install KFT Admin App</h3>
                <button id="pwa-modal-close" class="pwa-modal-close">&times;</button>
            </div>
            <div class="pwa-modal-body">
                <div class="pwa-app-info">
                    <div class="pwa-app-icon" style="width: 64px; height: 64px; background: #27ae60; border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 24px;">KFT</div>
                    <div class="pwa-app-details">
                        <h4>KFT Admin Panel</h4>
                        <p>Install the KFT Admin app for quick access and offline functionality.</p>
                        <ul class="pwa-features">
                            <li><i class="fas fa-check"></i> Quick access from home screen</li>
                            <li><i class="fas fa-check"></i> Offline functionality</li>
                            <li><i class="fas fa-check"></i> Push notifications</li>
                            <li><i class="fas fa-check"></i> Native app experience</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="pwa-modal-footer">
                <button id="pwa-install-confirm" class="btn btn-primary">
                    <i class="fas fa-download me-2"></i>Install App
                </button>
                <button id="pwa-install-cancel" class="btn btn-secondary">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // PWA Test Functions
        class PWATest {
            constructor() {
                this.runTests();
                this.bindDemoEvents();
            }
            
            runTests() {
                this.testServiceWorkerSupport();
                this.testManifest();
                this.testHTTPS();
                this.testStandaloneMode();
                this.registerServiceWorker();
            }
            
            testServiceWorkerSupport() {
                const element = document.getElementById('sw-support');
                if ('serviceWorker' in navigator) {
                    this.setStatus(element, 'pass', 'Supported');
                } else {
                    this.setStatus(element, 'fail', 'Not Supported');
                }
            }
            
            testManifest() {
                const element = document.getElementById('manifest-check');
                fetch('manifest.json')
                    .then(response => {
                        if (response.ok) {
                            this.setStatus(element, 'pass', 'Found');
                        } else {
                            this.setStatus(element, 'fail', 'Not Found');
                        }
                    })
                    .catch(() => {
                        this.setStatus(element, 'fail', 'Error');
                    });
            }
            
            testHTTPS() {
                const element = document.getElementById('https-check');
                const isSecure = location.protocol === 'https:' || location.hostname === 'localhost';
                if (isSecure) {
                    this.setStatus(element, 'pass', 'Secure');
                } else {
                    this.setStatus(element, 'fail', 'Insecure');
                }
            }
            
            testStandaloneMode() {
                const element = document.getElementById('standalone-mode');
                const isStandalone = window.matchMedia('(display-mode: standalone)').matches || 
                                   window.navigator.standalone === true;
                if (isStandalone) {
                    this.setStatus(element, 'pass', 'Active');
                } else {
                    this.setStatus(element, 'warning', 'Browser Mode');
                }
            }
            
            registerServiceWorker() {
                const element = document.getElementById('sw-registration');
                if ('serviceWorker' in navigator) {
                    navigator.serviceWorker.register('/sw.js')
                        .then(registration => {
                            this.setStatus(element, 'pass', 'Registered');
                            console.log('SW registered:', registration);
                        })
                        .catch(error => {
                            this.setStatus(element, 'fail', 'Failed');
                            console.error('SW registration failed:', error);
                        });
                } else {
                    this.setStatus(element, 'fail', 'Not Supported');
                }
            }
            
            setStatus(element, type, text) {
                element.className = `test-status status-${type}`;
                element.textContent = text;
            }
            
            bindDemoEvents() {
                document.getElementById('test-install').addEventListener('click', () => {
                    this.showResult('Install prompt triggered (if available)');
                });
                
                document.getElementById('test-notification').addEventListener('click', () => {
                    this.testNotification();
                });
                
                document.getElementById('test-offline').addEventListener('click', () => {
                    window.location.href = 'offline.html';
                });
                
                document.getElementById('clear-cache').addEventListener('click', () => {
                    this.clearCache();
                });
            }
            
            testNotification() {
                if ('Notification' in window) {
                    if (Notification.permission === 'granted') {
                        new Notification('KFT Admin PWA', {
                            body: 'Test notification from PWA!',
                            icon: 'assets/img/pwa-icon-192x192.png'
                        });
                        this.showResult('Notification sent!');
                    } else if (Notification.permission !== 'denied') {
                        Notification.requestPermission().then(permission => {
                            if (permission === 'granted') {
                                new Notification('KFT Admin PWA', {
                                    body: 'Test notification from PWA!',
                                    icon: 'assets/img/pwa-icon-192x192.png'
                                });
                                this.showResult('Notification permission granted and sent!');
                            } else {
                                this.showResult('Notification permission denied');
                            }
                        });
                    } else {
                        this.showResult('Notifications are blocked');
                    }
                } else {
                    this.showResult('Notifications not supported');
                }
            }
            
            clearCache() {
                if ('caches' in window) {
                    caches.keys().then(cacheNames => {
                        return Promise.all(
                            cacheNames.map(cacheName => caches.delete(cacheName))
                        );
                    }).then(() => {
                        this.showResult('Cache cleared successfully');
                    });
                } else {
                    this.showResult('Cache API not supported');
                }
            }
            
            showResult(message) {
                const results = document.getElementById('demo-results');
                const alert = document.createElement('div');
                alert.className = 'alert alert-info alert-dismissible fade show mt-2';
                alert.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                results.appendChild(alert);
                
                // Auto-remove after 5 seconds
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 5000);
            }
        }
        
        // Listen for install prompt
        window.addEventListener('beforeinstallprompt', (e) => {
            const element = document.getElementById('install-prompt');
            element.className = 'test-status status-pass';
            element.textContent = 'Available';
        });
        
        // Initialize test when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new PWATest();
        });
    </script>
    
    <!-- PWA Install Script -->
    <script src="assets/js/pwa-install.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
