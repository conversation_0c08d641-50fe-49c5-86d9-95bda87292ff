<?php
require_once '../includes/config.php';

$settingsManager = Settings::getInstance();
if (!$settingsManager->get('enable_registration', 1)) {
    echo json_encode([
        'success' => false,
        'message' => 'Registration is currently disabled.'
    ]);
    exit;
}

$db = new Database();
$conn = $db->getConnection();

$data = getRequestData();
$name = sanitizeInput($data['name'] ?? '');
$phone = sanitizeInput($data['phone_number']);
$deviceId = sanitizeInput($data['device_id'] ?? '');

// Validate required fields
if (empty($phone)) {
    returnResponse([
        'success' => false,
        'message' => 'Phone number is required.'
    ]);
}

// Check if phone already exists in users table using improved matching
$existingUser = $db->getUserByPhone($phone);

if ($existingUser) {
    returnResponse([
        'success' => false,
        'message' => 'This phone number is already registered.'
    ]);
}

// Check if phone already exists in pending_users table
// Use improved phone number matching by checking variations
require_once '../includes/utilities.php';
$phoneVariations = Utilities::getPhoneNumberVariations($phone);

$pendingExists = false;
foreach ($phoneVariations as $variation) {
    $checkPendingQuery = "SELECT id FROM pending_users WHERE phone_number = ? AND status = 'pending'";
    $checkPendingStmt = $conn->prepare($checkPendingQuery);
    $checkPendingStmt->bind_param("s", $variation);
    $checkPendingStmt->execute();
    $pendingResult = $checkPendingStmt->get_result();

    if ($pendingResult->num_rows > 0) {
        $pendingExists = true;
        break;
    }
}

if ($pendingExists) {
    returnResponse([
        'success' => false,
        'message' => 'Registration request already exists for this phone number.'
    ]);
}

// Add device_id column if it doesn't exist
$checkDeviceColumnQuery = "SHOW COLUMNS FROM pending_users LIKE 'device_id'";
$checkDeviceColumnResult = $conn->query($checkDeviceColumnQuery);

if ($checkDeviceColumnResult->num_rows === 0) {
    $alterTableQuery = "ALTER TABLE pending_users ADD COLUMN device_id VARCHAR(255) NULL AFTER phone_number";
    $conn->query($alterTableQuery);
}

// Save as pending user with current timestamp
$query = "INSERT INTO pending_users (name, phone_number, device_id, created_at) VALUES (?, ?, ?, NOW())";
$stmt = $conn->prepare($query);
$stmt->bind_param("sss", $name, $phone, $deviceId);

if ($stmt->execute()) {
    returnResponse([
        'success' => true,
        'message' => 'Registration request sent successfully. Admin will review your request.'
    ]);
} else {
    returnResponse([
        'success' => false,
        'message' => 'Failed to submit registration request. Please try again.'
    ]);
}