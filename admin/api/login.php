<?php
require_once 'config.php';
require_once '../includes/utilities.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    returnError('Method not allowed', 405);
}

// Get request data
$data = getRequestData();

// Log the request data in development mode
if (defined('DEV_MODE') && DEV_MODE === true) {
    error_log('Login request data: ' . print_r($data, true));
}

// Check if this is a verification request or initial login
$isVerification = isset($data['verification_code']) && !empty($data['verification_code']);

// For initial login, we need a username
if (!$isVerification && empty($data['username'])) {
    returnError('Username is required');
}

// For verification, we need both username and verification code
if ($isVerification && (empty($data['username']) || empty($data['verification_code']))) {
    returnError('Username and verification code are required');
}

// Require PIN for login
if (!$isVerification && (empty($data['pin']) || !preg_match('/^\d{4}$/', $data['pin']))) {
    returnError('A valid 4-digit PIN is required');
}

// Sanitize inputs
$username = sanitizeInput($data['username']);
$verificationCode = $isVerification ? sanitizeInput($data['verification_code']) : null;
$deviceId = isset($data['device_id']) ? sanitizeInput($data['device_id']) : null;
$pin = $isVerification ? null : sanitizeInput($data['pin']);

// Connect to database
$conn = getDbConnection();

// Use the improved Database class for username lookup
require_once '../includes/database.php';
$db = new Database();
$user = $db->getUserByUsername($username);
if (!$user) {
    // Not registered
    returnResponse([
        'success' => false,
        'error' => 'not_registered',
        'message' => 'This username is not registered. Please contact admin.'
    ]);
}

// Check if user is active
if (!$user['is_active']) {
    returnResponse([
        'success' => false,
        'error' => 'inactive',
        'message' => 'Your account is inactive. Please contact admin.'
    ]);
}

// After finding the user by username, check PIN and expiry
if (!$isVerification && $user) {
    error_log('DEBUG: Login attempt for user_id=' . $user['id'] . ', username=' . $username . ', entered PIN=' . $pin);
    $stmt = $conn->prepare('SELECT pin, pin_expires_at, pin_used FROM users WHERE id = ?');
    $stmt->bind_param('i', $user['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $userPinRow = $result->fetch_assoc();
    error_log('DEBUG: DB PIN=' . ($userPinRow['pin'] ?? 'NULL') . ', DB pin_expires_at=' . ($userPinRow['pin_expires_at'] ?? 'NULL') . ', DB pin_used=' . ($userPinRow['pin_used'] ?? 'NULL'));
    if (!$userPinRow || $userPinRow['pin'] !== $pin) {
        error_log('DEBUG: PIN mismatch or not found.');
        returnResponse([
            'success' => false,
            'error' => 'invalid_pin',
            'message' => 'Incorrect PIN. Please check and try again.'
        ]);
    }
    if (isset($userPinRow['pin_used']) && $userPinRow['pin_used'] == 1) {
        returnResponse([
            'success' => false,
            'error' => 'pin_already_used',
            'message' => 'This PIN has already been used. Please contact admin for a new PIN.'
        ]);
    }
    if (empty($userPinRow['pin_expires_at']) || strtotime($userPinRow['pin_expires_at']) < time()) {
        error_log('DEBUG: PIN expired or missing expiry.');
        returnResponse([
            'success' => false,
            'error' => 'pin_expired',
            'message' => 'Your PIN has expired. Please contact admin for a new PIN.'
        ]);
    }
}

// Enhanced single-session device management
if (empty($user['device_id'])) {
    // First login: save device ID
    $updateDeviceStmt = $conn->prepare("UPDATE users SET device_id = ?, last_login = NOW() WHERE id = ?");
    $updateDeviceStmt->bind_param("si", $deviceId, $user['id']);
    $updateDeviceStmt->execute();
    $updateDeviceStmt->close();

    $user['device_id'] = $deviceId;
    $forcedLogout = false;

    // Log first login
    error_log("First login: User {$user['id']} ({$user['name']}) - Device ID: {$deviceId}");

} else {
    // Always allow login, ignore device mismatch or forced logout
    $updateLoginStmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $updateLoginStmt->bind_param("i", $user['id']);
    $updateLoginStmt->execute();
    $updateLoginStmt->close();

    $forcedLogout = false;
}

// Successful login
// Generate and store token in database
$tokenData = generateToken($user['id']);
$token = $tokenData['token'];

// Generate JWT token using the improved JWT library
require_once '../includes/jwt.php';

// Determine token expiration based on request
$extendedSession = isset($input['extended_session']) && $input['extended_session'] === true;
$rememberMe = isset($input['remember_me']) && $input['remember_me'] === true;

// Set token expiration
if ($extendedSession || $rememberMe) {
    $tokenExpiry = time() + (300 * 24 * 60 * 60); // 300 days for persistent login
} else {
    $tokenExpiry = time() + (7 * 24 * 60 * 60); // 7 days for regular login
}

$jwtPayload = [
    'user_id' => $user['id'],
    'name' => $user['name'],
    'iat' => time(),
    'exp' => $tokenExpiry,
    'extended' => $extendedSession || $rememberMe
];

// Create JWT token using the improved library
$jwtToken = generate_jwt($jwtPayload, APP_SECRET);

// Enhanced logging in development mode
if (defined('DEV_MODE') && DEV_MODE === true) {
    error_log('=== JWT TOKEN GENERATION ===');
    error_log('User ID: ' . $user['id']);
    error_log('User Name: ' . $user['name']);
    error_log('Current time: ' . time() . ' (' . date('Y-m-d H:i:s') . ')');
    error_log('Expiry time: ' . ($jwtPayload['exp']) . ' (' . date('Y-m-d H:i:s', $jwtPayload['exp']) . ')');
    error_log('Time until expiry: ' . ($jwtPayload['exp'] - time()) . ' seconds');
    error_log('APP_SECRET (first 10 chars): ' . substr(APP_SECRET, 0, 10) . '...');
    error_log('Generated database token: ' . $token);
    error_log('Generated JWT token (first 50 chars): ' . substr($jwtToken, 0, 50) . '...');
    error_log('Token expires at: ' . $tokenData['expires_at']);
    error_log('=== END JWT TOKEN GENERATION ===');
}

// Generate refresh token for extended sessions
$refreshToken = null;
if ($extendedSession || $rememberMe) {
    $refreshTokenPayload = [
        'user_id' => $user['id'],
        'type' => 'refresh',
        'iat' => time(),
        'exp' => time() + (365 * 24 * 60 * 60) // 1 year for refresh token
    ];
    $refreshToken = generate_jwt($refreshTokenPayload, APP_SECRET . '_refresh');
}

// Mark PIN as used if single-use
if (!$isVerification && isset($userPinRow['pin_used']) && $userPinRow['pin_used'] == 0) {
    $conn->query("UPDATE users SET pin_used = 1 WHERE id = " . intval($user['id']));
}

returnResponse([
    'success' => true,
    'token' => $jwtToken,
    'refresh_token' => $refreshToken,
    'expires_at' => date('Y-m-d H:i:s', $tokenExpiry),
    'extended_session' => $extendedSession || $rememberMe,
    'forced_logout' => $forcedLogout,
    'session_info' => [
        'device_id' => $user['device_id'],
        'login_time' => date('Y-m-d H:i:s'),
        'session_type' => $forcedLogout ? 'new_device' : 'same_device',
        'previous_device_logout' => $forcedLogout,
    ],
    'user' => [
        'id' => $user['id'],
        'name' => $user['name'],
        'username' => $user['username'],
        'email' => $user['email'],
        'phone_number' => $user['phone_number'],
    ]
]);
