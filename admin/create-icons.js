const fs = require('fs');
const { createCanvas } = require('canvas');

const sizes = [72, 96, 128, 144, 152, 192, 384, 512];

sizes.forEach(size => {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');
    
    // Background
    ctx.fillStyle = '#27ae60';
    ctx.fillRect(0, 0, size, size);
    
    // Text
    ctx.fillStyle = 'white';
    ctx.font = `bold ${size * 0.2}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('KFT', size/2, size/2 - size*0.05);
    
    ctx.font = `${size * 0.1}px Arial`;
    ctx.fillText('ADMIN', size/2, size/2 + size*0.15);
    
    // Save
    const buffer = canvas.toBuffer('image/png');
    fs.writeFileSync(`assets/img/pwa-icon-${size}x${size}.png`, buffer);
    console.log(`Created pwa-icon-${size}x${size}.png`);
});

console.log('All PWA icons created successfully!');
