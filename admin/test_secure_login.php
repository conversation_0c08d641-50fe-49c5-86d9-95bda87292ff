<?php
/**
 * Test script for secure login token functionality
 * This helps verify that the system is working correctly
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';

// Check if admin is logged in
$auth = new Auth();
if (!$auth->isLoggedIn() || !$auth->hasRole('admin')) {
    die('Access denied. Admin login required.');
}

$db = new Database();
$conn = $db->getConnection();

echo "<h1>Secure Login Token System Test</h1>";

// Test 1: Check if table exists
echo "<h2>Test 1: Database Table Check</h2>";
try {
    $result = $conn->query("SHOW TABLES LIKE 'secure_login_tokens'");
    if ($result->num_rows > 0) {
        echo "✅ secure_login_tokens table exists<br>";
        
        // Check table structure
        $result = $conn->query("DESCRIBE secure_login_tokens");
        echo "<details><summary>Table structure:</summary><pre>";
        while ($row = $result->fetch_assoc()) {
            echo "{$row['Field']} - {$row['Type']} - {$row['Null']} - {$row['Key']}\n";
        }
        echo "</pre></details>";
    } else {
        echo "❌ secure_login_tokens table does not exist. Run setup_secure_login_tokens.php first.<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking table: " . $e->getMessage() . "<br>";
}

// Test 2: Check if API endpoint exists
echo "<h2>Test 2: API Endpoint Check</h2>";
if (file_exists('api/secure_login_token.php')) {
    echo "✅ API endpoint exists<br>";
} else {
    echo "❌ API endpoint missing<br>";
}

// Test 3: Test token generation (if we have a test user)
echo "<h2>Test 3: Token Generation Test</h2>";
try {
    // Find a test user (first active user)
    $userResult = $conn->query("SELECT id, username, name FROM users WHERE is_active = 1 LIMIT 1");
    if ($userResult->num_rows > 0) {
        $testUser = $userResult->fetch_assoc();
        echo "Using test user: {$testUser['name']} (ID: {$testUser['id']})<br>";
        
        // Generate a test token
        $token = bin2hex(random_bytes(64));
        $expiresAt = date('Y-m-d H:i:s', time() + (24 * 60 * 60));
        $adminId = $auth->getUserId();
        
        $insertQuery = "INSERT INTO secure_login_tokens (user_id, token, generated_by_admin, expires_at) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($insertQuery);
        $stmt->bind_param("isis", $testUser['id'], $token, $adminId, $expiresAt);
        
        if ($stmt->execute()) {
            echo "✅ Test token generated successfully<br>";
            echo "Token ID: " . $conn->insert_id . "<br>";
            echo "Expires: " . $expiresAt . "<br>";
            
            // Generate test URL
            $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
            $testUrl = $baseUrl . dirname($_SERVER['REQUEST_URI']) . "/login.php?secure_token=" . urlencode($token);
            echo "<strong>Test URL:</strong><br>";
            echo "<input type='text' value='" . htmlspecialchars($testUrl) . "' style='width: 100%; margin: 5px 0;' readonly><br>";
            echo "<small>⚠️ This is a real token - use it only for testing!</small><br>";
            
            // Clean up test token
            $cleanupQuery = "DELETE FROM secure_login_tokens WHERE token = ?";
            $stmt = $conn->prepare($cleanupQuery);
            $stmt->bind_param("s", $token);
            $stmt->execute();
            echo "🧹 Test token cleaned up<br>";
            
        } else {
            echo "❌ Failed to generate test token<br>";
        }
    } else {
        echo "❌ No active users found for testing<br>";
    }
} catch (Exception $e) {
    echo "❌ Error in token generation test: " . $e->getMessage() . "<br>";
}

// Test 4: Check current token statistics
echo "<h2>Test 4: Current Statistics</h2>";
try {
    $statsQuery = "
        SELECT 
            COUNT(*) as total_tokens,
            SUM(CASE WHEN is_used = FALSE AND expires_at > NOW() THEN 1 ELSE 0 END) as active_tokens,
            SUM(CASE WHEN is_used = TRUE THEN 1 ELSE 0 END) as used_tokens,
            SUM(CASE WHEN expires_at <= NOW() THEN 1 ELSE 0 END) as expired_tokens
        FROM secure_login_tokens
    ";
    
    $result = $conn->query($statsQuery);
    $stats = $result->fetch_assoc();
    
    echo "Total tokens: {$stats['total_tokens']}<br>";
    echo "Active tokens: {$stats['active_tokens']}<br>";
    echo "Used tokens: {$stats['used_tokens']}<br>";
    echo "Expired tokens: {$stats['expired_tokens']}<br>";
    
} catch (Exception $e) {
    echo "❌ Error getting statistics: " . $e->getMessage() . "<br>";
}

// Test 5: Check if cleanup procedure exists
echo "<h2>Test 5: Cleanup Procedure Check</h2>";
try {
    $result = $conn->query("SHOW PROCEDURE STATUS WHERE Name = 'CleanupExpiredLoginTokens'");
    if ($result->num_rows > 0) {
        echo "✅ CleanupExpiredLoginTokens procedure exists<br>";
    } else {
        echo "❌ CleanupExpiredLoginTokens procedure missing<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking procedure: " . $e->getMessage() . "<br>";
}

echo "<br><hr>";
echo "<h2>Next Steps</h2>";
echo "<ul>";
echo "<li>If all tests pass, the secure login token system is ready to use</li>";
echo "<li>Go to any user profile to generate secure login links</li>";
echo "<li>Set up a cron job to run cleanup_expired_tokens.php periodically</li>";
echo "<li>Monitor the admin_action_logs table for security auditing</li>";
echo "</ul>";

echo "<br><a href='users.php'>← Back to Users</a> | ";
echo "<a href='setup_secure_login_tokens.php'>Run Setup</a> | ";
echo "<a href='cleanup_expired_tokens.php'>Run Cleanup</a>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Secure Login Token Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        details { margin: 10px 0; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        input[readonly] { background: #f8f9fa; }
    </style>
</head>
<body>
</body>
</html>
