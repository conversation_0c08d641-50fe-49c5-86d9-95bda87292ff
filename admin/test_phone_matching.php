<?php
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

echo "<h2>Phone Number Matching Test</h2>\n";

// Test phone number normalization
echo "<h3>1. Phone Number Normalization Tests</h3>\n";
$testNumbers = [
    '+919876543210',
    '919876543210', 
    '9876543210',
    '******-567-8900',
    '12345678900',
    '1234567890',
    '+44 20 7946 0958',
    '447946095800'
];

foreach ($testNumbers as $number) {
    $normalized = Utilities::normalizePhoneNumber($number);
    echo "Input: $number → Normalized: $normalized<br>\n";
}

// Test phone number variations
echo "<h3>2. Phone Number Variations Tests</h3>\n";
$testCases = [
    '9876543210',
    '+919876543210',
    '919876543210',
    '+12345678900',
    '12345678900'
];

foreach ($testCases as $number) {
    $variations = Utilities::getPhoneNumberVariations($number);
    echo "Input: $number<br>\n";
    echo "Variations: " . implode(', ', $variations) . "<br><br>\n";
}

// Test database lookup (if you have test data)
echo "<h3>3. Database Lookup Test</h3>\n";
echo "Testing database lookup with different phone number formats...<br>\n";

$db = new Database();

// Test with a sample phone number (replace with actual test data)
$testPhoneNumbers = [
    '9876543210',
    '+919876543210', 
    '919876543210'
];

foreach ($testPhoneNumbers as $testPhone) {
    echo "Testing lookup for: $testPhone<br>\n";
    $user = $db->getUserByPhone($testPhone);
    if ($user) {
        echo "✓ Found user: {$user['name']} (ID: {$user['id']}, Phone: {$user['phone_number']})<br>\n";
    } else {
        echo "✗ No user found<br>\n";
    }
    echo "<br>\n";
}

echo "<h3>4. Test Summary</h3>\n";
echo "✓ Phone number normalization function created<br>\n";
echo "✓ Phone number variations function created<br>\n";
echo "✓ Database getUserByPhone method updated<br>\n";
echo "✓ API login.php updated to use improved matching<br>\n";
echo "✓ Main login.php already uses Database class<br>\n";
echo "✓ Registration API updated<br>\n";
echo "✓ User forms updated for better UX<br>\n";
echo "✓ JavaScript validation made more flexible<br>\n";

echo "<p><strong>The phone number authentication system has been improved to:</strong></p>\n";
echo "<ul>\n";
echo "<li>Accept phone numbers with or without country codes</li>\n";
echo "<li>Handle various international formats</li>\n";
echo "<li>Provide flexible matching during login</li>\n";
echo "<li>Improve user experience with clearer form instructions</li>\n";
echo "<li>Use efficient database queries instead of loading all users</li>\n";
echo "</ul>\n";
?>
