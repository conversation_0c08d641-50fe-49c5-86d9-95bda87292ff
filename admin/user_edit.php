<?php
require_once 'includes/header.php';
require_once 'includes/audit_logger.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if user has permission to access this page
if (!$auth->hasRole('admin') && !$auth->hasRole('super_admin') && !$auth->hasPermission('manage_users')) {
    Utilities::setFlashMessage('error', 'You do not have permission to edit users.');
    Utilities::redirect('index.php');
}

// Get current admin role and ID
$currentAdminRole = $auth->getUserRole();
$currentAdminId = $auth->getUserId();
$isSuperAdmin = $auth->hasRole('super_admin');
$isAdmin = $auth->hasRole('admin') || $isSuperAdmin;

// Check if user ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    Utilities::setFlashMessage('danger', 'Invalid user ID.');
    Utilities::redirect('users.php');
}

$userId = (int)$_GET['id'];

// Get user data
$userQuery = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($userQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    Utilities::setFlashMessage('danger', 'User not found.');
    Utilities::redirect('users.php');
}

$user = $result->fetch_assoc();

// Handle device reassignment
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reassign_device']) && $_POST['reassign_device'] == '1') {
    // Re-check user existence
    $stmt = $conn->prepare("SELECT id FROM users WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows === 0) {
        Utilities::setFlashMessage('danger', 'User not found.');
        Utilities::redirect('users.php');
    }
    $resetQuery = "UPDATE users SET device_id = NULL WHERE id = ?";
    $resetStmt = $conn->prepare($resetQuery);
    $resetStmt->bind_param("i", $userId);
    if ($resetStmt->execute()) {
        // Invalidate all API tokens for this user (force logout on Flutter app)
        $delTokenStmt = $conn->prepare("DELETE FROM api_tokens WHERE user_id = ?");
        $delTokenStmt->bind_param("i", $userId);
        $delTokenStmt->execute();
        $delTokenStmt->close();
        // If the affected user is the current session user, log them out
        if ($userId == ($_SESSION['user_id'] ?? null)) {
            // Double-check device_id is now NULL in DB
            $checkStmt = $conn->prepare("SELECT device_id FROM users WHERE id = ?");
            $checkStmt->bind_param("i", $userId);
            $checkStmt->execute();
            $result = $checkStmt->get_result();
            $row = $result->fetch_assoc();
            if (empty($row['device_id'])) {
                session_unset();
                session_destroy();
                header("Location: login.php?device_removed=1");
                exit;
            }
        }
        Utilities::setFlashMessage('success', 'Device ID has been reassigned. The user must log in again on a new device.');
    } else {
        Utilities::setFlashMessage('danger', 'Failed to reassign device ID.');
    }
    Utilities::redirect('user_edit.php?id=' . $userId);
}

// Handle course expiry date update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['edit_expiry']) && $_POST['edit_expiry'] == '1') {
    // Re-check user existence
    $stmt = $conn->prepare("SELECT id FROM users WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows === 0) {
        Utilities::setFlashMessage('danger', 'User not found.');
        Utilities::redirect('users.php');
    }
    $enrollmentId = (int)($_POST['enrollment_id'] ?? 0);
    $newExpiry = $_POST['new_expiry'] ?? '';
    if ($enrollmentId && $newExpiry) {
        $updateExpiryQuery = "UPDATE user_course_enrollments SET end_date = ? WHERE id = ? AND user_id = ?";
        $stmt = $conn->prepare($updateExpiryQuery);
        $stmt->bind_param("sii", $newExpiry, $enrollmentId, $userId);
        if ($stmt->execute()) {
            Utilities::setFlashMessage('success', 'Course expiry date updated successfully.');
        } else {
            Utilities::setFlashMessage('danger', 'Failed to update course expiry date.');
        }
    } else {
        Utilities::setFlashMessage('danger', 'Invalid request for expiry update.');
    }
    Utilities::redirect('user_edit.php?id=' . $userId . '#course' . $enrollmentId);
}

// Process main form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['reassign_device']) && !isset($_POST['edit_expiry'])) {
    // Re-check user existence
    $stmt = $conn->prepare("SELECT id FROM users WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows === 0) {
        Utilities::setFlashMessage('danger', 'User not found.');
        Utilities::redirect('users.php');
    }
    // Sanitize inputs
    $name = Utilities::sanitizeInput($_POST['name'] ?? '');
    $username = Utilities::sanitizeInput($_POST['username'] ?? '');
    $email = Utilities::sanitizeInput($_POST['email'] ?? '');
    // Convert empty email to NULL
    $email = empty($email) ? null : $email;
    $phone = Utilities::sanitizeInput($_POST['phone'] ?? '');
    // Convert empty phone to NULL
    $phone = empty($phone) ? null : $phone;
    $age = isset($_POST['age']) && !empty($_POST['age']) ? (int)$_POST['age'] : null;
    $height = isset($_POST['height']) && !empty($_POST['height']) ? (float)$_POST['height'] : null;
    $weight = isset($_POST['weight']) && !empty($_POST['weight']) ? (float)$_POST['weight'] : null;
    $isPremium = isset($_POST['is_premium']) ? 1 : 0;
    $isActive = isset($_POST['is_active']) ? 1 : 0;
    $assignedStaffId = isset($_POST['assigned_staff_id']) && !empty($_POST['assigned_staff_id']) ? (int)$_POST['assigned_staff_id'] : null;

    // Validate inputs
    $errors = [];

    if (empty($name)) {
        $errors[] = 'Name is required.';
    }

    // Username validation using centralized function
    $usernameValidation = Utilities::validateUsernameComplete($db, $username, $userId);
    if (!$usernameValidation['valid']) {
        $errors[] = $usernameValidation['error'];
    }

    if (!empty($email) && !Utilities::validateEmail($email)) {
        $errors[] = 'Please enter a valid email address.';
    }

    // Check if email already exists (if provided and changed)
    if (!empty($email) && $email !== $user['email']) {
        $checkQuery = "SELECT id FROM users WHERE email = ? AND id != ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("si", $email, $userId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $errors[] = 'Email already exists.';
        }
    }

    if (empty($errors)) {
        // Check if phone column exists in users table
        $checkPhoneColumnQuery = "SHOW COLUMNS FROM users LIKE 'phone'";
        $checkPhoneColumnResult = $conn->query($checkPhoneColumnQuery);

        if ($checkPhoneColumnResult->num_rows === 0) {
            // Add phone column if it doesn't exist
            $alterTableQuery = "ALTER TABLE users ADD COLUMN phone VARCHAR(20) NULL";
            $conn->query($alterTableQuery);
        }

        // Get gender and fitness goal from form
        $gender = isset($_POST['gender']) ? Utilities::sanitizeInput($_POST['gender']) : null;
        $fitnessGoal = isset($_POST['fitness_goal']) ? Utilities::sanitizeInput($_POST['fitness_goal']) : null;

        // Update user
        $updateQuery = "UPDATE users SET name = ?, username = ?, email = ?, phone = ?, phone_number = ?, age = ?, height = ?, weight = ?, is_premium = ?, is_active = ?, gender = ?, fitness_goal = ?, assigned_staff_id = ? WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("sssssiidiissii", $name, $username, $email, $phone, $phone, $age, $height, $weight, $isPremium, $isActive, $gender, $fitnessGoal, $assignedStaffId, $userId);

        if ($stmt->execute()) {
            // Log success for debugging
            error_log("User update successful for user ID: $userId");

            // Log the activity in audit logs
            $details = "Updated user profile: " . $name;
            if ($isStaff || $isAdmin) {
                logStaffActivity($conn, $currentAdminId, 'user_edit', $userId, $details);
            }

            // Session access functionality removed

            Utilities::setFlashMessage('success', 'User has been updated successfully.');
            Utilities::redirect('users.php');
        } else {
            // Log error for debugging
            error_log("User update failed for user ID: $userId. Error: " . $conn->error);
            error_log("SQL Query: $updateQuery");
            error_log("Params: name=$name, email=$email, phone=$phone, age=$age, height=$height, weight=$weight, isPremium=$isPremium, isActive=$isActive, gender=$gender, fitnessGoal=$fitnessGoal, userId=$userId");

            Utilities::setFlashMessage('danger', 'Failed to update user: ' . $conn->error);
        }
    } else {
        // Display errors
        foreach ($errors as $error) {
            Utilities::setFlashMessage('danger', $error);
        }
    }
}

// Define fitness goals
$fitnessGoals = [
    'weight_loss' => 'Weight Loss',
    'muscle_gain' => 'Muscle Gain',
    'endurance' => 'Endurance',
    'flexibility' => 'Flexibility',
    'general_fitness' => 'General Fitness',
    'strength' => 'Strength Training',
    'rehabilitation' => 'Rehabilitation'
];

// Session access functionality removed

// Get all staff members for the dropdown
$staffQuery = "SELECT id, name, username FROM admin_users WHERE role = 'staff' ORDER BY name";
$staffResult = $conn->query($staffQuery);
$staffMembers = [];
if ($staffResult && $staffResult->num_rows > 0) {
    while ($staff = $staffResult->fetch_assoc()) {
        $staffMembers[] = $staff;
    }
}

// Get user's enrolled courses
$enrolledCoursesQuery = "SELECT e.id as enrollment_id, e.start_date, e.end_date, e.status,
                        c.id as course_id, c.title, c.category, c.duration_weeks, c.price
                        FROM user_course_enrollments e
                        JOIN courses c ON e.course_id = c.id
                        WHERE e.user_id = ?
                        ORDER BY e.start_date DESC";
$enrolledCoursesStmt = $conn->prepare($enrolledCoursesQuery);
$enrolledCoursesStmt->bind_param("i", $userId);
$enrolledCoursesStmt->execute();
$enrolledCoursesResult = $enrolledCoursesStmt->get_result();
$enrolledCourses = [];
while ($course = $enrolledCoursesResult->fetch_assoc()) {
    $enrolledCourses[] = $course;
}

// Get available courses (not enrolled)
$availableCoursesQuery = "SELECT c.id, c.title, c.category, c.duration_weeks, c.price
                         FROM courses c
                         WHERE c.is_active = 1
                         AND c.id NOT IN (
                             SELECT course_id FROM user_course_enrollments WHERE user_id = ?
                         )
                         ORDER BY c.title";
$availableCoursesStmt = $conn->prepare($availableCoursesQuery);
$availableCoursesStmt->bind_param("i", $userId);
$availableCoursesStmt->execute();
$availableCoursesResult = $availableCoursesStmt->get_result();
$availableCourses = [];
while ($course = $availableCoursesResult->fetch_assoc()) {
    $availableCourses[] = $course;
}
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Edit User</h1>
    <a href="users.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> Back to Users
    </a>
</div>

<div class="card border-0 shadow-sm">
    <div class="card-body">
        <?php Utilities::displayFlashMessages(); ?>

        <!-- User Profile Image -->
        <div class="text-center mb-4">
            <?php
            $profileImageUrl = $user['profile_image_url'] ?? '';

            if (!empty($profileImageUrl)) {
                // Check if the image URL is a relative path
                if (!preg_match('/^https?:\/\//', $profileImageUrl)) {
                    // Convert relative path to absolute URL
                    $baseUrl = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
                    $baseUrl .= $_SERVER['HTTP_HOST'];

                    // Remove leading slash if present
                    if (substr($profileImageUrl, 0, 1) === '/') {
                        $profileImageUrl = substr($profileImageUrl, 1);
                    }

                    $profileImageUrl = "$baseUrl/$profileImageUrl";
                }

                // Add cache busting parameter
                $profileImageUrl .= (strpos($profileImageUrl, '?') !== false ? '&' : '?') . 't=' . time();
            ?>
                <div class="user-avatar-large mx-auto">
                    <img src="<?php echo htmlspecialchars($profileImageUrl); ?>" alt="<?php echo htmlspecialchars($user['name']); ?>" style="width:100%; height:100%; object-fit:cover; border-radius:50%;">
                </div>
            <?php } else { ?>
                <div class="user-avatar-large mx-auto" style="background-color: <?php echo Utilities::getColorFromName($user['name']); ?>;">
                    <?php echo strtoupper(substr($user['name'], 0, 1)); ?>
                </div>
            <?php } ?>
            <h5 class="mt-3"><?php echo htmlspecialchars($user['name']); ?></h5>
            <p class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></p>
        </div>

        <form method="post" action="user_edit.php?id=<?php echo $userId; ?>" id="mainUserForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($user['name']); ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="username" name="username"
                               value="<?php echo htmlspecialchars($user['username']); ?>"
                               required pattern="[a-zA-Z0-9_]{3,20}"
                               title="Username must be 3-20 characters long and contain only letters, numbers, and underscores">
                        <div class="form-text">Username will be used for app login (no spaces allowed)</div>
                        <div id="username-error" class="text-danger" style="display: none;"></div>
                        <div id="username-success" class="text-success" style="display: none;"></div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars((string)($user['phone'] ?? $user['phone_number'] ?? '')); ?>" placeholder="+1234567890 or 1234567890" inputmode="numeric">
                        <div class="form-text">Enter phone number with or without country code. System accepts any format.</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="age" class="form-label">Age</label>
                        <input type="number" class="form-control" id="age" name="age" min="1" max="120" value="<?php echo $user['age'] ?? ''; ?>">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="height" class="form-label">Height (cm)</label>
                        <input type="number" class="form-control" id="height" name="height" step="0.01" min="0" value="<?php echo $user['height'] ?? ''; ?>">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="weight" class="form-label">Weight (kg)</label>
                        <input type="number" class="form-control" id="weight" name="weight" step="0.01" min="0" value="<?php echo $user['weight'] ?? ''; ?>">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="gender" class="form-label">Gender</label>
                        <select class="form-select" id="gender" name="gender">
                            <option value="">Select Gender</option>
                            <option value="male" <?php if (($user['gender'] ?? '') === 'male') echo 'selected'; ?>>Male</option>
                            <option value="female" <?php if (($user['gender'] ?? '') === 'female') echo 'selected'; ?>>Female</option>
                            <option value="other" <?php if (($user['gender'] ?? '') === 'other') echo 'selected'; ?>>Other</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="fitness_goal" class="form-label">Fitness Goal</label>
                        <select class="form-select" id="fitness_goal" name="fitness_goal">
                            <option value="">Select Fitness Goal</option>
                            <?php foreach ($fitnessGoals as $value => $label): ?>
                                <option value="<?php echo $value; ?>" <?php if (($user['fitness_goal'] ?? '') === $value) echo 'selected'; ?>><?php echo $label; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <?php if ($isSuperAdmin && !empty($staffMembers)): ?>
                    <div class="mb-3">
                        <label for="assigned_staff_id" class="form-label">Assigned Staff Member</label>
                        <select class="form-select" id="assigned_staff_id" name="assigned_staff_id">
                            <option value="">No Staff Assigned</option>
                            <?php foreach ($staffMembers as $staff): ?>
                                <option value="<?php echo $staff['id']; ?>" <?php echo ($user['assigned_staff_id'] == $staff['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($staff['name'] . ' (' . $staff['username'] . ')'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">Assign this user to a staff member for management.</div>
                    </div>
                    <?php elseif ($currentAdminRole === 'staff'): ?>
                        <?php if ($user['assigned_staff_id'] == $currentAdminId): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-user-check me-2"></i> This user is assigned to you.
                            </div>
                        <?php elseif (!empty($user['assigned_staff_id'])): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i> This user is assigned to another staff member.
                            </div>
                        <?php else: ?>
                            <div class="alert alert-secondary">
                                <i class="fas fa-info-circle me-2"></i> This user is not assigned to any staff member.
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <!-- Session Access section removed -->
                </div>
            </div>
            <div class="mt-4">
                <button type="submit" class="btn btn-primary" id="saveChangesBtn" onclick="document.getElementById('mainUserForm').submit(); return false;">
                    <i class="fas fa-save me-2"></i> Save Changes
                </button>
                <a href="users.php" class="btn btn-outline-secondary ms-2">Cancel</a>
            </div>
        </form>
    </div>
</div>

<!-- Course Management Section -->
<div class="card border-0 shadow-sm mt-4">
    <div class="card-header bg-white py-3">
        <h5 class="mb-0">Course Management</h5>
        <p class="text-muted mb-0 small">Manage courses assigned to this user</p>
    </div>
    <div class="card-body">
        <!-- Enrolled Courses -->
        <div class="mb-4">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="fw-bold mb-0">Enrolled Courses</h6>
                <span class="badge bg-primary rounded-pill"><?php echo count($enrolledCourses); ?></span>
            </div>
            <?php if (empty($enrolledCourses)): ?>
                <div class="alert alert-light border text-center py-4">
                    <i class="fas fa-book-open fa-2x text-muted mb-3"></i>
                    <p class="text-muted mb-0">This user is not enrolled in any courses yet.</p>
                </div>
            <?php else: ?>
                <div class="row g-2">
                    <?php foreach ($enrolledCourses as $course): ?>
                        <div class="col-12 col-md-6 col-lg-4">
                            <div class="card enrolled-course-card-minimal h-100 border-0">
                                <div class="card-body p-3 d-flex flex-column justify-content-between">
                                    <div>
                                        <div class="mb-2">
                                            <span class="fw-bold text-dark" style="font-size:1.08rem;"><i class="fas fa-graduation-cap me-2 text-success"></i><?php echo htmlspecialchars((string)($course['title'] ?? '')); ?></span>
                                        </div>
                                        <div class="mb-2 small text-muted">
                                            <?php echo htmlspecialchars((string)($course['category'] ?? '')); ?> &bull; <?php echo $course['duration_weeks']; ?> weeks &bull; ₹<?php echo number_format($course['price'], 2); ?>
                                        </div>
                                        <div class="mb-2 small">
                                            <span class="text-muted">Enrolled:</span> <?php echo date('M d, Y', strtotime($course['start_date'])); ?>
                                        </div>
                                        <div class="mb-2 small">
                                            <span class="text-muted">Expires:</span> 
                                            <?php $daysLeft = (new DateTime($course['end_date']))->diff(new DateTime())->format('%r%a'); ?>
                                            <span class="badge bg-<?php echo ($daysLeft < 0 ? 'danger' : 'success'); ?>">
                                                <?php echo date('M d, Y', strtotime($course['end_date'])); ?>
                                                <?php if ($daysLeft < 0): ?>
                                                    <span class="ms-1">Expired</span>
                                                <?php else: ?>
                                                    <span class="ms-1"><?php echo $daysLeft; ?>d left</span>
                                                <?php endif; ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="d-flex gap-2 mt-2 align-items-center">
                                        <form action="course_unenroll.php" method="post" class="d-inline" onsubmit="return confirm('Are you sure you want to remove this course from the user?');">
                                            <input type="hidden" name="enrollment_id" value="<?php echo $course['enrollment_id']; ?>">
                                            <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                            <button type="submit" class="btn btn-sm btn-outline-danger px-2" data-bs-toggle="tooltip" title="Remove Course">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </form>
                                        <a href="video_analytics.php?user_id=<?php echo $userId; ?>&course_id=<?php echo $course['course_id']; ?>" class="btn btn-sm btn-outline-primary px-2" data-bs-toggle="tooltip" title="View Analytics">
                                            <i class="fas fa-chart-line"></i>
                                        </a>
                                        <form method="post" action="user_edit.php?id=<?php echo $userId; ?>#course<?php echo $course['enrollment_id']; ?>" class="d-inline-block">
                                            <input type="hidden" name="edit_expiry" value="1">
                                            <input type="hidden" name="enrollment_id" value="<?php echo $course['enrollment_id']; ?>">
                                            <input type="date" class="form-control form-control-sm d-inline-block w-auto" id="new_expiry_<?php echo $course['enrollment_id']; ?>" name="new_expiry" value="<?php echo date('Y-m-d', strtotime($course['end_date'])); ?>" min="<?php echo date('Y-m-d', strtotime($course['start_date'])); ?>" style="max-width:120px;">
                                            <button type="submit" class="btn btn-sm btn-outline-primary px-2" data-bs-toggle="tooltip" title="Save Expiry"><i class="fas fa-save"></i></button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <style>
                .enrolled-course-card-minimal {
                    border-radius: 10px;
                    background: #f9fbfa;
                    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.04);
                    transition: box-shadow 0.2s;
                }
                .enrolled-course-card-minimal:hover {
                    box-shadow: 0 4px 18px rgba(39, 174, 96, 0.10);
                }
                .enrolled-course-card-minimal .badge {
                    font-size: 0.92rem;
                    font-weight: 500;
                }
                </style>
            <?php endif; ?>
        </div>

        <!-- Add New Course -->
        <div class="mt-4">
            <h6 class="fw-bold mb-3">Add New Course</h6>
            <?php if (empty($availableCourses)): ?>
                <div class="alert alert-light border text-center py-3">
                    <p class="text-muted mb-0">No more courses available to assign to this user.</p>
                </div>
            <?php else: ?>
                <form action="course_enroll.php" method="post" class="row g-3" id="addCourseForm">
                    <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                    <div class="col-md-6">
                        <label for="course_id" class="form-label">Select Course</label>
                        <select class="form-select" id="course_id" name="course_id" required>
                            <option value="">-- Select a course --</option>
                            <?php foreach (
                                $availableCourses as $course): ?>
                                <option value="<?php echo $course['id']; ?>" data-price="<?php echo $course['price']; ?>" data-duration="<?php echo $course['duration_weeks']; ?>">
                                    <?php echo htmlspecialchars((string)($course['title'] ?? '')); ?>
                                    (<?php echo htmlspecialchars((string)($course['category'] ?? '')); ?>) -
                                    <?php echo $course['duration_weeks']; ?> weeks -
                                    ₹<?php echo number_format($course['price'], 2); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">Start Date</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                    <div class="col-md-3">
                        <label for="expiry_date" class="form-label">Expiry Date</label>
                        <input type="date" class="form-control" id="expiry_date" name="expiry_date" required>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus-circle me-2"></i> Enroll in Course
                        </button>
                    </div>
                </form>
                <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const courseSelect = document.getElementById('course_id');
                    const expiryInput = document.getElementById('expiry_date');
                    const startInput = document.getElementById('start_date');

                    courseSelect.addEventListener('change', function() {
                        const selected = this.options[this.selectedIndex];
                        if (selected.value) {
                            // Set default expiry date based on duration
                            const duration = parseInt(selected.getAttribute('data-duration'));
                            if (!isNaN(duration)) {
                                const startDate = new Date(startInput.value);
                                if (!isNaN(startDate.getTime())) {
                                    const expiryDate = new Date(startDate);
                                    expiryDate.setDate(expiryDate.getDate() + duration * 7);
                                    expiryInput.value = expiryDate.toISOString().slice(0,10);
                                }
                            }
                        } else {
                            expiryInput.value = '';
                        }
                    });

                    startInput.addEventListener('change', function() {
                        const selected = courseSelect.options[courseSelect.selectedIndex];
                        const duration = parseInt(selected.getAttribute('data-duration'));
                        if (!isNaN(duration)) {
                            const startDate = new Date(startInput.value);
                            if (!isNaN(startDate.getTime())) {
                                const expiryDate = new Date(startDate);
                                expiryDate.setDate(expiryDate.getDate() + duration * 7);
                                expiryInput.value = expiryDate.toISOString().slice(0,10);
                            }
                        }
                    });
                });
                </script>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>

<!-- Hidden form for device reassignment -->
<form id="reassignDeviceForm" method="post" action="user_edit.php?id=<?php echo $userId; ?>" style="display: none;">
    <input type="hidden" name="reassign_device" value="1">
</form>

<script>
// JavaScript to handle the reassign device button click
document.addEventListener('DOMContentLoaded', function() {
    // Handle reassign device button
    const reassignDeviceBtn = document.getElementById('reassignDeviceBtn');
    const reassignDeviceForm = document.getElementById('reassignDeviceForm');

    if (reassignDeviceBtn && reassignDeviceForm) {
        reassignDeviceBtn.addEventListener('click', function() {
            if (confirm('Reassigning device will require the user to log in again on a new device. Continue?')) {
                reassignDeviceForm.submit();
            }
        });
    }

    // Real-time username validation
    var usernameInput = document.getElementById('username');
    var usernameError = document.getElementById('username-error');
    var usernameSuccess = document.getElementById('username-success');
    var usernameTimeout;
    var originalUsername = usernameInput.value; // Store original value

    usernameInput.addEventListener('input', function() {
        var username = usernameInput.value.trim();

        // Clear previous timeout
        clearTimeout(usernameTimeout);

        // Hide previous messages
        usernameError.style.display = 'none';
        usernameSuccess.style.display = 'none';
        usernameInput.classList.remove('is-invalid', 'is-valid');

        if (username.length === 0) {
            return;
        }

        // If username hasn't changed, mark as valid
        if (username === originalUsername) {
            usernameSuccess.style.display = '';
            usernameSuccess.innerText = 'Current username';
            usernameInput.classList.add('is-valid');
            return;
        }

        // Check for spaces immediately
        if (/\s/.test(username)) {
            usernameError.style.display = '';
            usernameError.innerText = 'Username cannot contain spaces';
            usernameInput.classList.add('is-invalid');
            return;
        }

        // Check format
        if (!/^[a-zA-Z0-9_]{3,20}$/.test(username)) {
            usernameError.style.display = '';
            usernameError.innerText = 'Username must be 3-20 characters long and contain only letters, numbers, and underscores';
            usernameInput.classList.add('is-invalid');
            return;
        }

        // Check availability after a delay
        usernameTimeout = setTimeout(function() {
            var xhr = new XMLHttpRequest();
            xhr.open('POST', 'username_check.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    try {
                        var res = JSON.parse(xhr.responseText);
                        if (res.exists) {
                            usernameError.style.display = '';
                            usernameError.innerText = 'Username already exists. Please choose a different username.';
                            usernameInput.classList.add('is-invalid');
                        } else {
                            usernameSuccess.style.display = '';
                            usernameSuccess.innerText = 'Username is available';
                            usernameInput.classList.add('is-valid');
                        }
                    } catch (e) {
                        console.error('Error parsing username check response:', e);
                    }
                }
            };
            xhr.send('username=' + encodeURIComponent(username));
        }, 500); // 500ms delay
    });

    // Handle main form submission
    const mainForm = document.querySelector('form[action^="user_edit.php"]');
    const saveButton = document.querySelector('button[type="submit"].btn-primary');

    if (mainForm && saveButton) {
        console.log('Main form and save button found');

        // Add click event to the save button
        saveButton.addEventListener('click', function(e) {
            console.log('Save button clicked');
        });

        // Add submit event to the form
        mainForm.addEventListener('submit', function(e) {
            console.log('Form submitted');
        });
    } else {
        console.error('Main form or save button not found');
        if (!mainForm) console.error('Main form not found');
        if (!saveButton) console.error('Save button not found');
    }
});
</script>
