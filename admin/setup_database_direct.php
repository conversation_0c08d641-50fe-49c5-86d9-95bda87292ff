<?php
/**
 * Direct database setup script for secure login tokens
 * This can be run from command line or browser
 */

// Allow running from command line
if (php_sapi_name() === 'cli') {
    // CLI mode - set up basic environment
    $_SERVER['HTTP_HOST'] = 'localhost:9001';
    $_SERVER['REQUEST_URI'] = '/admin/setup_database_direct.php';
    $_SERVER['HTTPS'] = 'off';
}

require_once 'includes/config.php';
require_once 'includes/database.php';

echo "Setting up Secure Login Tokens Database...\n";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "✅ Database connection established\n";
    
    // Create secure_login_tokens table
    $createTableSQL = "
    CREATE TABLE IF NOT EXISTS secure_login_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token VARCHAR(128) NOT NULL UNIQUE,
        generated_by_admin INT NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        used_at TIMESTAMP NULL DEFAULT NULL,
        is_used BOOLEAN DEFAULT FALSE,
        ip_address VARCHAR(45) DEFAULT NULL,
        user_agent TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_token (token),
        INDEX idx_user_id (user_id),
        INDEX idx_expires_at (expires_at),
        INDEX idx_is_used (is_used),
        INDEX idx_created_at (created_at),
        INDEX idx_user_token (user_id, token),
        INDEX idx_active_tokens (user_id, is_used, expires_at)
    )";
    
    if ($conn->query($createTableSQL)) {
        echo "✅ secure_login_tokens table created successfully\n";
    } else {
        echo "❌ Error creating table: " . $conn->error . "\n";
        exit(1);
    }
    
    // Create cleanup procedure
    $createProcedureSQL = "
    DROP PROCEDURE IF EXISTS CleanupExpiredLoginTokens;
    CREATE PROCEDURE CleanupExpiredLoginTokens()
    BEGIN
        DELETE FROM secure_login_tokens 
        WHERE (expires_at < NOW() OR is_used = TRUE) 
        AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR);
        
        SELECT ROW_COUNT() as cleaned_tokens;
    END";
    
    if ($conn->multi_query($createProcedureSQL)) {
        // Clear any remaining results
        do {
            if ($result = $conn->store_result()) {
                $result->free();
            }
        } while ($conn->next_result());
        
        echo "✅ CleanupExpiredLoginTokens procedure created successfully\n";
    } else {
        echo "❌ Error creating procedure: " . $conn->error . "\n";
    }
    
    // Create view for active tokens
    $createViewSQL = "
    CREATE OR REPLACE VIEW active_secure_login_tokens AS
    SELECT 
        slt.*,
        u.username,
        u.name as user_name,
        au.username as admin_username,
        au.name as admin_name
    FROM secure_login_tokens slt
    JOIN users u ON slt.user_id = u.id
    JOIN admin_users au ON slt.generated_by_admin = au.id
    WHERE slt.is_used = FALSE 
    AND slt.expires_at > NOW()";
    
    if ($conn->query($createViewSQL)) {
        echo "✅ active_secure_login_tokens view created successfully\n";
    } else {
        echo "❌ Error creating view: " . $conn->error . "\n";
    }
    
    // Test the table
    $testQuery = "SELECT COUNT(*) as count FROM secure_login_tokens";
    $result = $conn->query($testQuery);
    if ($result) {
        $row = $result->fetch_assoc();
        echo "✅ Table test successful - Current token count: " . $row['count'] . "\n";
    }
    
    echo "\n🎉 Setup completed successfully!\n";
    echo "The secure login token system is now ready to use.\n";
    
    if (php_sapi_name() !== 'cli') {
        echo "<br><a href='user_view.php?id=27'>← Back to User Profile</a>";
    }
    
} catch (Exception $e) {
    echo "❌ Error during setup: " . $e->getMessage() . "\n";
    exit(1);
}
?>
