<?php
/**
 * Test URL Shortener Functionality
 * This script tests the URL shortening system
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/url_shortener.php';

// Check if admin is logged in
$auth = new Auth();
if (!$auth->isLoggedIn() || !$auth->hasRole(['admin', 'super_admin'])) {
    die('Unauthorized access. Please login as admin.');
}

$db = new Database();
$conn = $db->getConnection();
$urlShortener = new UrlShortener($conn);

echo "<h2>URL Shortener Test</h2>";

// Test URL creation
$testUrl = "https://example.com/very/long/url/that/needs/to/be/shortened?param1=value1&param2=value2";
$result = $urlShortener->createShortUrl($testUrl, 1, date('Y-m-d H:i:s', time() + 3600));

if ($result['success']) {
    echo "<h3>✅ Short URL Created Successfully</h3>";
    echo "<p><strong>Original URL:</strong> " . htmlspecialchars($testUrl) . "</p>";
    echo "<p><strong>Short Code:</strong> " . htmlspecialchars($result['short_code']) . "</p>";
    echo "<p><strong>Short URL:</strong> <a href='" . htmlspecialchars($result['short_url']) . "' target='_blank'>" . htmlspecialchars($result['short_url']) . "</a></p>";
    
    // Test URL resolution
    echo "<h3>Testing URL Resolution</h3>";
    $resolveResult = $urlShortener->resolveShortUrl($result['short_code']);
    
    if ($resolveResult['success']) {
        echo "<p>✅ URL resolved successfully</p>";
        echo "<p><strong>Resolved URL:</strong> " . htmlspecialchars($resolveResult['long_url']) . "</p>";
    } else {
        echo "<p>❌ Failed to resolve URL: " . htmlspecialchars($resolveResult['error']) . "</p>";
    }
    
    // Test analytics
    echo "<h3>Analytics</h3>";
    $analytics = $urlShortener->getAnalytics($result['short_code']);
    if ($analytics['success']) {
        echo "<p><strong>Click Count:</strong> " . $analytics['data']['click_count'] . "</p>";
        echo "<p><strong>Created:</strong> " . $analytics['data']['created_at'] . "</p>";
        echo "<p><strong>Last Accessed:</strong> " . ($analytics['data']['last_accessed'] ?? 'Never') . "</p>";
    }
    
} else {
    echo "<h3>❌ Failed to Create Short URL</h3>";
    echo "<p>Error: " . htmlspecialchars($result['error']) . "</p>";
}

// Test secure login token generation
echo "<hr><h3>Testing Secure Login Token with Short URL</h3>";

// Simulate secure login token generation
$userId = 1;
$token = bin2hex(random_bytes(64));
$expiresAt = date('Y-m-d H:i:s', time() + (24 * 60 * 60));

$baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
$longUrl = $baseUrl . dirname($_SERVER['REQUEST_URI']) . "/login.php?secure_token=" . urlencode($token);

$shortResult = $urlShortener->createShortUrl($longUrl, $userId, $expiresAt);

if ($shortResult['success']) {
    echo "<p>✅ Secure login short URL created</p>";
    echo "<p><strong>Long URL:</strong> " . htmlspecialchars($longUrl) . "</p>";
    echo "<p><strong>Short URL:</strong> <a href='" . htmlspecialchars($shortResult['short_url']) . "' target='_blank'>" . htmlspecialchars($shortResult['short_url']) . "</a></p>";
    echo "<p><strong>Length Reduction:</strong> " . strlen($longUrl) . " → " . strlen($shortResult['short_url']) . " characters (" . round((1 - strlen($shortResult['short_url']) / strlen($longUrl)) * 100, 1) . "% shorter)</p>";
} else {
    echo "<p>❌ Failed to create secure login short URL</p>";
}

echo "<hr>";
echo "<p><a href='user_view.php'>← Back to User Management</a></p>";

$conn->close();
?>
