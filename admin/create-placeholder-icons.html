<!DOCTYPE html>
<html>
<head>
    <title>Create Placeholder PWA Icons</title>
</head>
<body>
    <h1>Creating Placeholder PWA Icons...</h1>
    <div id="status"></div>
    
    <script>
        const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
        let completed = 0;
        
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = size;
            canvas.height = size;
            
            // Background
            ctx.fillStyle = '#27ae60';
            ctx.fillRect(0, 0, size, size);
            
            // Round corners
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, size * 0.15);
            ctx.fill();
            ctx.globalCompositeOperation = 'source-over';
            
            // Text
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.2}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('KFT', size/2, size/2 - size*0.05);
            
            ctx.font = `${size * 0.1}px Arial`;
            ctx.fillText('ADMIN', size/2, size/2 + size*0.15);
            
            // Download
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `pwa-icon-${size}x${size}.png`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                completed++;
                document.getElementById('status').innerHTML = `Created ${completed}/${sizes.length} icons`;
                
                if (completed === sizes.length) {
                    document.getElementById('status').innerHTML += '<br><strong>All icons created! Please move them to assets/img/ folder.</strong>';
                }
            });
        }
        
        // Create all icons
        sizes.forEach(size => createIcon(size));
    </script>
</body>
</html>
