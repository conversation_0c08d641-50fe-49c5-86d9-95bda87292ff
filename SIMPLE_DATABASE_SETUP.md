# 🗄️ Simple Database Setup for KFT Fitness

## ✅ **Current Status**
- ✅ **PHP Server**: Running on http://localhost:9001
- ✅ **Admin Panel**: Available at http://localhost:9001/admin/
- ✅ **Flutter App**: Starting on http://localhost:8080
- ✅ **HTTP Support**: Enabled (no HTTPS required)

---

## 🚀 **Quick Database Setup**

### **Option 1: Manual MySQL Setup (Recommended)**

#### **1. Connect to MySQL**
```bash
# Connect with your MySQL credentials
mysql -u root -p
# Or if you have a different user:
mysql -u your_username -p
```

#### **2. Create Database**
```sql
-- Create the database
CREATE DATABASE myclo4dz_new_kftdb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the database
USE myclo4dz_new_kftdb;

-- Import the SQL file
SOURCE admin/myclo4dz_new_kftdb (1).sql;

-- Verify tables were created
SHOW TABLES;

-- Check users table
SELECT COUNT(*) FROM users;
```

#### **3. Update PHP Configuration**
Edit `admin/includes/config.php` with your MySQL credentials:
```php
<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'myclo4dz_new_kftdb');
define('DB_USER', 'your_mysql_username');
define('DB_PASS', 'your_mysql_password');
?>
```

---

### **Option 2: Using phpMyAdmin (If Available)**

1. **Open phpMyAdmin** in browser
2. **Create new database**: `myclo4dz_new_kftdb`
3. **Import SQL file**: Select `admin/myclo4dz_new_kftdb (1).sql`
4. **Wait for import** to complete
5. **Verify tables** were created

---

### **Option 3: Command Line Import**

```bash
# Create database and import in one command
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS myclo4dz_new_kftdb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# Import the SQL file
mysql -u root -p myclo4dz_new_kftdb < "admin/myclo4dz_new_kftdb (1).sql"

# Verify import
mysql -u root -p -e "USE myclo4dz_new_kftdb; SHOW TABLES; SELECT COUNT(*) FROM users;"
```

---

## 🔧 **Testing the Setup**

### **1. Test Admin Panel**
- Open: http://localhost:9001/admin/
- Should show login page
- Check if database connection works

### **2. Test API Endpoints**
```bash
# Test basic API
curl http://localhost:9001/admin/api/ping.php

# Test username check
curl -X POST http://localhost:9001/admin/username_check_app.php \
  -d "username=testuser&device_id=test123"
```

### **3. Test Flutter App**
- Open: http://localhost:8080 (when Flutter finishes loading)
- Try username login
- Should connect to PHP server on port 9001

---

## 🎯 **Current Running Services**

### **✅ PHP Server (Terminal 21)**
- **Status**: Running
- **URL**: http://localhost:9001
- **Admin Panel**: http://localhost:9001/admin/
- **API**: http://localhost:9001/admin/api/

### **✅ Flutter App (Terminal 23)**
- **Status**: Starting up
- **URL**: http://localhost:8080 (when ready)
- **Mode**: Debug mode with hot reload

---

## 🔍 **Troubleshooting**

### **Database Connection Issues:**
1. **Check MySQL is running**:
   ```bash
   # macOS
   brew services list | grep mysql
   
   # Linux
   sudo systemctl status mysql
   ```

2. **Test MySQL connection**:
   ```bash
   mysql -u root -p -e "SELECT 1;"
   ```

3. **Check database exists**:
   ```bash
   mysql -u root -p -e "SHOW DATABASES LIKE 'myclo4dz_new_kftdb';"
   ```

### **PHP Server Issues:**
1. **Check if port 9001 is free**:
   ```bash
   lsof -i :9001
   ```

2. **Restart PHP server**:
   ```bash
   # Kill current server (Ctrl+C in terminal 21)
   # Then restart:
   ./run_php_server.sh
   ```

### **Flutter App Issues:**
1. **Check Flutter doctor**:
   ```bash
   flutter doctor
   ```

2. **Clean and rebuild**:
   ```bash
   flutter clean
   flutter pub get
   flutter run -d chrome
   ```

---

## 🎉 **Success Indicators**

### **✅ Everything Working When:**
- Admin panel loads at http://localhost:9001/admin/
- Database connection successful in admin panel
- Flutter app loads at http://localhost:8080
- Username login works without errors
- API endpoints respond correctly

---

## 📝 **Next Steps**

1. **Set up database** using one of the options above
2. **Test admin panel** login
3. **Wait for Flutter app** to finish loading
4. **Test username login** in Flutter app
5. **Verify HTTP communication** between Flutter and PHP

---

**🎯 The servers are running! Just need to set up the database and you'll be ready to test the username login system with HTTP support.**
