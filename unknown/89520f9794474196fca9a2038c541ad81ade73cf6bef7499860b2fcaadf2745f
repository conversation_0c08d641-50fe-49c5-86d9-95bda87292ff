<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3D5AFE;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E88E5;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dumbbellGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E3F2FD;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="256" cy="256" r="240" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Dumbbell icon -->
  <g transform="translate(256, 256)">
    <!-- Left weight -->
    <rect x="-120" y="-25" width="40" height="50" rx="8" fill="url(#dumbbellGradient)" stroke="#FFFFFF" stroke-width="3"/>
    
    <!-- Right weight -->
    <rect x="80" y="-25" width="40" height="50" rx="8" fill="url(#dumbbellGradient)" stroke="#FFFFFF" stroke-width="3"/>
    
    <!-- Center bar -->
    <rect x="-80" y="-8" width="160" height="16" rx="8" fill="url(#dumbbellGradient)" stroke="#FFFFFF" stroke-width="3"/>
    
    <!-- Grip texture on center bar -->
    <rect x="-70" y="-4" width="140" height="8" rx="4" fill="#E3F2FD"/>
    
    <!-- Small accent circles on weights -->
    <circle cx="-100" cy="0" r="8" fill="#3D5AFE" opacity="0.8"/>
    <circle cx="100" cy="0" r="8" fill="#3D5AFE" opacity="0.8"/>
  </g>
  
  <!-- KFT text (subtle) -->
  <text x="256" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#FFFFFF" opacity="0.9">KFT</text>
</svg> 