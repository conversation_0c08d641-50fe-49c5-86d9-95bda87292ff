-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 05, 2025 at 01:04 PM
-- Server version: 5.7.23-23
-- PHP Version: 8.1.32

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `myclo4dz_new_kftdb`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin_action_logs`
--

CREATE TABLE `admin_action_logs` (
  `id` int(11) NOT NULL,
  `admin_user_id` int(11) NOT NULL,
  `admin_username` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `action_type` enum('device_revoke','user_deactivate','user_activate','token_invalidate','session_terminate') COLLATE utf8_unicode_ci NOT NULL,
  `target_user_id` int(11) NOT NULL,
  `target_username` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `target_device_id` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `action_details` json DEFAULT NULL,
  `reason` text COLLATE utf8_unicode_ci,
  `ip_address` varchar(45) COLLATE utf8_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `admin_permissions`
--

CREATE TABLE `admin_permissions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_permissions`
--

INSERT INTO `admin_permissions` (`id`, `name`, `slug`, `description`, `created_at`, `updated_at`) VALUES
(1, 'Manage Users', 'manage_users', 'Create, edit, and delete users', '2025-05-11 17:59:19', '2025-05-11 17:59:19'),
(2, 'Manage Courses', 'manage_courses', 'Create, edit, and delete courses', '2025-05-11 17:59:19', '2025-05-11 17:59:19'),
(3, 'Manage Workouts', 'manage_workouts', 'Create, edit, and delete workouts', '2025-05-11 17:59:19', '2025-05-11 17:59:19'),
(4, 'Manage Quotes', 'manage_quotes', 'Create, edit, and delete motivational quotes', '2025-05-11 17:59:19', '2025-05-11 17:59:19'),
(5, 'View Reports', 'view_reports', 'View system reports and analytics', '2025-05-11 17:59:19', '2025-05-11 17:59:19'),
(6, 'Manage Settings', 'manage_settings', 'Modify system settings', '2025-05-11 17:59:19', '2025-05-11 17:59:19'),
(7, 'Manage Staff', 'manage_staff', 'Create, edit, and delete staff accounts', '2025-05-11 17:59:19', '2025-05-11 17:59:19'),
(9, 'Manage Food Database', 'manage_food_database', 'Add, edit, and delete food items in the calorie tracker database', '2025-05-11 17:59:19', '2025-05-11 17:59:19'),
(10, 'Assign Users', 'assign_users', 'Assign users to staff members', '2025-05-11 17:59:19', '2025-05-11 17:59:19');

-- --------------------------------------------------------

--
-- Table structure for table `admin_users`
--

CREATE TABLE `admin_users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `username` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` enum('admin','editor','viewer','super_admin','staff') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'staff',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `last_login` timestamp NULL DEFAULT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `phone` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `parent_admin_id` bigint(20) UNSIGNED DEFAULT NULL,
  `permissions_locked` tinyint(1) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_users`
--

INSERT INTO `admin_users` (`id`, `username`, `password`, `email`, `name`, `role`, `is_active`, `last_login`, `remember_token`, `created_at`, `updated_at`, `phone`, `parent_admin_id`, `permissions_locked`) VALUES
(1, 'admin', '$2y$12$w21cilpio7DT12RjIbAOkOVHvlOnlIKAvo0.tpO/YmhoIW7DMU7X2', '<EMAIL>', 'Administrator', 'super_admin', 1, '2025-05-21 03:57:25', NULL, '2025-05-09 06:54:42', '2025-05-09 07:40:41', '+919876543210', NULL, 0),
(2, 'editor', '$2y$12$gwgclGe6oCeQjQz23ZpiWe3NumHro8Es0MQMlC0qav.fogJO5.ScG', '<EMAIL>', 'Editor User', 'editor', 1, NULL, NULL, '2025-05-09 06:54:42', '2025-05-09 06:54:42', NULL, NULL, 0),
(3, 'viewer', '$2y$12$isAz1GdUkKNXmsCPyYgW1ulPZnpzSK0SQ8wV.WQv1klsrYqmxnNUO', '<EMAIL>', 'Viewer User', 'viewer', 1, NULL, NULL, '2025-05-09 06:54:43', '2025-05-09 06:54:43', NULL, NULL, 0),
(4, 'staff1', '$2y$12$Gmgz9tpyZZQumy3xhn2dguhicndSiiQEZYisNBHMnMbb5n2UA9wSi', '<EMAIL>', 'StaffOne', 'staff', 1, '2025-05-12 04:29:52', NULL, NULL, NULL, '1234567890', 1, 0),
(5, 'STAFF2', '$2y$12$l7lgcfSwlU6x4MkYkP4IfePF8n.L9/x6XK3y3wux/eOZAVsgsnWCm', '<EMAIL>', 'staff2', 'staff', 1, '2025-05-13 17:24:38', NULL, NULL, NULL, '987654321', 1, 0),
(6, 'staff3', '$2y$12$Pk/stfD.DsxlKRosQGGHLecFBWNs78kh4viEJ5Xj.7EKMErpqSpVa', '<EMAIL>', 'staff3', 'staff', 1, '2025-05-12 08:45:32', NULL, NULL, NULL, '987654321', 1, 0),
(7, 'staffsdf', '$2y$12$UnwyQPS4x.96HeppQQ4CiOdp98MCke659I29v/u4JpxNyEYepPSMC', '<EMAIL>', 'staff01', 'staff', 1, '2025-05-12 06:40:16', NULL, NULL, NULL, '9876543212', 1, 0),
(8, 'dfsfds', '$2y$12$VROKkuTkUqHLa02Yk3uTF.ovcyPneZtYmjLqzrbAgU1.Ek4zKZ39K', '<EMAIL>', 'abc', 'staff', 0, NULL, NULL, NULL, NULL, '1234567890', 1, 0);

-- --------------------------------------------------------

--
-- Table structure for table `admin_user_permissions`
--

CREATE TABLE `admin_user_permissions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `admin_user_id` bigint(20) UNSIGNED NOT NULL,
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_user_permissions`
--

INSERT INTO `admin_user_permissions` (`id`, `admin_user_id`, `permission_id`, `created_at`) VALUES
(1, 1, 10, '2025-05-11 17:59:19'),
(3, 1, 2, '2025-05-11 17:59:19'),
(4, 1, 9, '2025-05-11 17:59:19'),
(5, 1, 4, '2025-05-11 17:59:19'),
(6, 1, 6, '2025-05-11 17:59:19'),
(7, 1, 7, '2025-05-11 17:59:19'),
(8, 1, 1, '2025-05-11 17:59:19'),
(9, 1, 3, '2025-05-11 17:59:19'),
(10, 1, 5, '2025-05-11 17:59:19'),
(11, 4, 10, '2025-05-11 18:54:56'),
(12, 4, 9, '2025-05-11 18:54:56'),
(13, 4, 1, '2025-05-11 18:54:56'),
(14, 4, 3, '2025-05-11 18:54:56'),
(24, 6, 10, '2025-05-12 05:36:05'),
(25, 6, 7, '2025-05-12 05:36:05'),
(26, 6, 1, '2025-05-12 05:36:05'),
(60, 7, 1, '2025-05-12 14:39:09'),
(61, 7, 3, '2025-05-12 14:39:09'),
(62, 7, 5, '2025-05-12 14:39:09'),
(69, 8, 1, '2025-05-20 16:56:09'),
(70, 8, 3, '2025-05-20 16:56:09'),
(71, 8, 5, '2025-05-20 16:56:09'),
(72, 5, 10, '2025-05-29 03:28:00'),
(73, 5, 1, '2025-05-29 03:28:00'),
(74, 5, 2, '2025-05-29 03:28:00'),
(75, 5, 9, '2025-05-29 03:28:00');

-- --------------------------------------------------------

--
-- Table structure for table `api_tokens`
--

CREATE TABLE `api_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `device_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `device_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `last_used` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_revoked` tinyint(1) DEFAULT '0',
  `revoked_by_admin` int(11) DEFAULT NULL,
  `revoked_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `api_tokens`
--

INSERT INTO `api_tokens` (`id`, `user_id`, `token`, `device_name`, `device_id`, `expires_at`, `last_used_at`, `created_at`, `updated_at`, `last_used`, `is_revoked`, `revoked_by_admin`, `revoked_at`) VALUES
(171, 24, '4f63f89b8b6b51231b1396acabbf2e5a9a4ab30b108db704dc2b75c5c5f960b0', NULL, NULL, '2025-07-13 11:28:15', NULL, NULL, NULL, '2025-06-13 16:58:15', 0, NULL, NULL),
(335, 45, '93b6e7d1709a30ce64c0516c188c1ada0f8b7a0700573961b057ea85d1164cb7', NULL, NULL, '2025-07-21 10:53:35', NULL, NULL, NULL, '2025-06-21 16:23:35', 0, NULL, NULL),
(365, 35, '4b81bc3c2a333bade5b3aa2503db2e47d528eb9e73e8d7c935bf1bae160e0b48', NULL, NULL, '2025-07-28 02:21:30', NULL, NULL, NULL, '2025-06-28 07:51:30', 0, NULL, NULL),
(366, 38, 'dd4e107d23b561d6fa103ded68b5caf9a2954c7ae46443c1309bb4f6c32441ec', NULL, NULL, '2025-07-28 02:27:33', NULL, NULL, NULL, '2025-06-28 07:57:33', 0, NULL, NULL),
(367, 36, '8e8a16536386083de838b143aac13f5a785bb8d1468a0d052dfe44b3d579a6cf', NULL, NULL, '2025-07-28 02:27:38', NULL, NULL, NULL, '2025-06-28 07:57:38', 0, NULL, NULL),
(368, 41, '127fe7a3b1adb15fd2f007ad35d6ad2d382caa522aa0e26ebaca06b26ce66072', NULL, NULL, '2025-07-28 02:28:54', NULL, NULL, NULL, '2025-06-28 07:58:54', 0, NULL, NULL),
(369, 37, '191d9e0b1006d76fc6accb16ab40ead8069e5cc34f7de797709e0d9c757e4b4a', NULL, NULL, '2025-07-28 02:34:33', NULL, NULL, NULL, '2025-06-28 08:04:33', 0, NULL, NULL),
(370, 40, '5ffd10f22560a9c48e07e548b0170b4c334de3c5bb5e3480b7dfe3fdfa88a1f9', NULL, NULL, '2025-07-28 02:53:18', NULL, NULL, NULL, '2025-06-28 08:23:18', 0, NULL, NULL),
(371, 39, '71877971d7c18c2ffb6eded7fa4def12eb8d25c6bc4a68252acd959d391eb91a', NULL, NULL, '2025-07-28 02:57:09', NULL, NULL, NULL, '2025-06-28 08:27:09', 0, NULL, NULL),
(373, 33, 'f958ef00606889505a8e9de4cd2d465c9153ffdebbbf35417175fbe121a06d3d', NULL, NULL, '2025-07-28 12:26:41', NULL, NULL, NULL, '2025-06-28 17:56:41', 0, NULL, NULL),
(374, 25, 'fe0d643110541dce72b0bae0657cb9e7fcac75a2ac2c2ffac28da1672c0ff0c5', NULL, NULL, '2025-07-28 14:49:02', NULL, NULL, NULL, '2025-06-28 20:19:02', 0, NULL, NULL),
(375, 29, '18afb4e774163502a4d3f58a6fdcc00924f12f2a3c706f315970d9025c453de8', NULL, NULL, '2025-07-28 15:42:53', NULL, NULL, NULL, '2025-06-28 21:12:53', 0, NULL, NULL),
(378, 43, 'c84188f9217fa5306a435b2644a51e7cc8ef7990d009fbcf8524a9773f8d181d', NULL, NULL, '2025-07-29 22:22:21', NULL, NULL, NULL, '2025-06-30 03:52:21', 0, NULL, NULL),
(379, 44, '9ea29cdb7555a2f229aa3ae57a03e6f1659ffd4033e8729a0ae71b7887e230e9', NULL, NULL, '2025-07-30 09:12:01', NULL, NULL, NULL, '2025-06-30 14:42:01', 0, NULL, NULL),
(380, 30, 'fb890d784d12a8f46a5038390b98c768ca6c1d5a2b1570a9176b029de32f7ccf', NULL, NULL, '2025-07-31 00:16:13', NULL, NULL, NULL, '2025-07-01 05:46:13', 0, NULL, NULL),
(381, 34, '4a7c81a62c6fd876d5242fc284f9fe350b32f006e86e443566918d33f5f1647e', NULL, NULL, '2025-07-31 02:05:07', NULL, NULL, NULL, '2025-07-01 07:35:07', 0, NULL, NULL),
(383, 32, '5cede88f552d0a480cca87694b27350e6170805b1563362e26cd61b75c963772', NULL, NULL, '2025-08-02 04:17:20', NULL, NULL, NULL, '2025-07-03 09:47:20', 0, NULL, NULL),
(400, 27, '51970b4e883e8aad097ef782da91cf881d601e489891ab2f59d1005cbe0e02e7', NULL, NULL, '2025-08-04 01:24:28', NULL, NULL, NULL, '2025-07-05 06:54:28', 0, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `audit_logs`
--

CREATE TABLE `audit_logs` (
  `id` int(10) UNSIGNED NOT NULL,
  `staff_id` bigint(20) UNSIGNED NOT NULL,
  `action_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `affected_user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `details` text COLLATE utf8mb4_unicode_ci,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `audit_logs`
--

INSERT INTO `audit_logs` (`id`, `staff_id`, `action_type`, `timestamp`, `affected_user_id`, `details`, `ip_address`, `user_agent`) VALUES
(1, 1, 'user_edit', '2025-05-12 12:43:31', NULL, '0', '***********', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(2, 1, 'user_edit', '2025-05-12 12:43:46', NULL, '0', '***********', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(3, 1, 'user_edit', '2025-05-12 19:23:18', NULL, '0', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(4, 1, 'user_edit', '2025-05-12 19:24:44', NULL, '0', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(5, 1, 'user_edit', '2025-05-12 19:25:11', NULL, '0', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(6, 1, 'user_edit', '2025-05-12 19:25:45', NULL, '0', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(7, 1, 'user_edit', '2025-05-13 12:06:40', NULL, '0', '***********', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(8, 1, 'user_edit', '2025-05-13 13:11:22', NULL, '0', '***********', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(9, 1, 'user_edit', '2025-05-13 15:06:08', NULL, '0', '***********', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(10, 1, 'user_edit', '2025-06-10 19:54:35', NULL, '0', '157.51.226.107', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'),
(11, 1, 'user_edit', '2025-06-14 10:53:09', NULL, '0', '103.153.105.64', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'),
(12, 1, 'course_enroll', '2025-06-14 23:59:44', 27, '0', '157.51.213.235', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'),
(13, 1, 'user_edit', '2025-06-15 14:27:15', 27, '0', '157.51.226.179', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'),
(14, 1, 'user_edit', '2025-06-17 17:19:21', 27, '0', '157.51.226.99', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Mobile Safari/537.36'),
(15, 1, 'course_enroll', '2025-06-19 11:01:27', 43, '0', '103.153.105.77', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'),
(16, 1, 'user_edit', '2025-06-19 15:08:03', 45, '0', '157.51.204.7', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'),
(17, 1, 'course_enroll', '2025-06-19 15:46:55', 45, '0', '157.51.204.7', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'),
(18, 1, 'user_edit', '2025-06-19 15:58:50', 45, '0', '157.51.204.7', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'),
(19, 1, 'user_edit', '2025-06-19 16:06:59', 45, '0', '157.51.204.7', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'),
(20, 1, 'course_enroll', '2025-06-19 16:18:25', 45, '0', '157.51.204.7', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');

-- --------------------------------------------------------

--
-- Table structure for table `bmi_records`
--

CREATE TABLE `bmi_records` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `height` double NOT NULL,
  `weight` double NOT NULL,
  `bmi` double NOT NULL,
  `recorded_at` datetime NOT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `bmi_records`
--

INSERT INTO `bmi_records` (`id`, `user_id`, `height`, `weight`, `bmi`, `recorded_at`, `notes`, `created_at`, `updated_at`) VALUES
(26, 25, 152, 52, 22.506925207756233, '2025-05-28 17:26:07', NULL, NULL, NULL),
(31, 25, 152, 52, 22.506925207756233, '2025-05-28 21:26:26', NULL, NULL, NULL),
(32, 25, 152, 52, 22.506925207756233, '2025-05-28 21:31:33', NULL, NULL, NULL),
(33, 25, 152, 52, 22.506925207756233, '2025-05-28 21:33:47', NULL, NULL, NULL),
(40, 25, 152, 52, 22.506925207756233, '2025-05-29 20:04:15', NULL, NULL, NULL),
(56, 25, 152, 52, 22.506925207756233, '2025-06-02 12:17:21', NULL, NULL, NULL),
(91, 25, 152, 52, 22.506925207756233, '2025-06-05 22:58:45', NULL, NULL, NULL),
(118, 25, 152, 52, 22.506925207756233, '2025-06-13 22:01:14', NULL, NULL, NULL),
(119, 24, 188, 76, 21.50294250792214, '2025-06-13 22:28:18', NULL, NULL, NULL),
(120, 29, 157, 57, 23.124670372023203, '2025-06-14 10:24:01', NULL, NULL, NULL),
(121, 30, 163, 44, 16.560653393052053, '2025-06-14 10:36:16', NULL, NULL, NULL),
(123, 32, 154, 44, 18.55287569573284, '2025-06-14 10:52:16', NULL, NULL, NULL),
(124, 30, 163, 44, 16.560653393052053, '2025-06-14 10:54:25', NULL, NULL, NULL),
(125, 30, 160, 44, 17.187499999999996, '2025-06-14 10:54:27', NULL, NULL, NULL),
(126, 30, 160, 44, 17.187499999999996, '2025-06-14 10:54:31', NULL, NULL, NULL),
(127, 30, 160, 44, 17.187499999999996, '2025-06-14 10:55:01', NULL, NULL, NULL),
(128, 30, 160, 44, 17.187499999999996, '2025-06-14 10:55:01', NULL, NULL, NULL),
(129, 30, 161, 44, 16.97465375564214, '2025-06-14 10:55:01', NULL, NULL, NULL),
(130, 30, 160, 44, 17.187499999999996, '2025-06-14 10:55:01', NULL, NULL, NULL),
(131, 30, 160, 44, 17.187499999999996, '2025-06-14 10:55:01', NULL, NULL, NULL),
(132, 30, 160, 44, 17.187499999999996, '2025-06-14 10:55:01', NULL, NULL, NULL),
(133, 30, 160, 44, 17.187499999999996, '2025-06-14 10:55:01', NULL, NULL, NULL),
(134, 30, 160, 44, 17.187499999999996, '2025-06-14 10:55:01', NULL, NULL, NULL),
(135, 29, 157, 57, 23.124670372023203, '2025-06-14 10:57:15', NULL, NULL, NULL),
(136, 29, 157, 57, 23.124670372023203, '2025-06-14 10:57:16', NULL, NULL, NULL),
(137, 29, 157, 57, 23.124670372023203, '2025-06-14 10:57:18', NULL, NULL, NULL),
(138, 29, 157, 57, 23.124670372023203, '2025-06-14 10:57:19', NULL, NULL, NULL),
(139, 29, 157, 57, 23.124670372023203, '2025-06-14 10:57:20', NULL, NULL, NULL),
(140, 29, 157, 57, 23.124670372023203, '2025-06-14 10:57:22', NULL, NULL, NULL),
(141, 33, 163, 43, 16.184274906846326, '2025-06-14 10:58:28', NULL, NULL, NULL),
(142, 34, 150, 47, 20.88888888888889, '2025-06-14 11:01:45', NULL, NULL, NULL),
(143, 35, 151, 40, 17.543090215341433, '2025-06-14 11:05:06', NULL, NULL, NULL),
(144, 36, 150, 49, 21.77777777777778, '2025-06-14 11:07:32', NULL, NULL, NULL),
(145, 37, 160, 50, 19.531249999999996, '2025-06-14 11:19:31', NULL, NULL, NULL),
(146, 30, 160, 44, 17.187499999999996, '2025-06-14 11:33:45', NULL, NULL, NULL),
(147, 30, 160, 44, 17.187499999999996, '2025-06-14 11:34:14', NULL, NULL, NULL),
(148, 30, 161, 44, 16.97465375564214, '2025-06-14 11:34:14', NULL, NULL, NULL),
(149, 30, 161, 44, 16.97465375564214, '2025-06-14 11:34:15', NULL, NULL, NULL),
(150, 30, 161, 44, 16.97465375564214, '2025-06-14 11:34:16', NULL, NULL, NULL),
(151, 30, 161, 44, 16.97465375564214, '2025-06-14 11:34:17', NULL, NULL, NULL),
(152, 30, 161, 44, 16.97465375564214, '2025-06-14 11:34:17', NULL, NULL, NULL),
(153, 30, 161, 44, 16.97465375564214, '2025-06-14 11:34:18', NULL, NULL, NULL),
(154, 30, 161, 44, 16.97465375564214, '2025-06-14 11:34:18', NULL, NULL, NULL),
(155, 30, 161, 44, 16.97465375564214, '2025-06-14 11:34:19', NULL, NULL, NULL),
(156, 30, 161, 44, 16.97465375564214, '2025-06-14 11:34:20', NULL, NULL, NULL),
(157, 30, 161, 44, 16.97465375564214, '2025-06-14 11:34:21', NULL, NULL, NULL),
(158, 30, 161, 44, 16.97465375564214, '2025-06-14 11:34:22', NULL, NULL, NULL),
(159, 32, 154, 44, 18.55287569573284, '2025-06-14 11:34:56', NULL, NULL, NULL),
(160, 32, 153, 44, 18.79618949976505, '2025-06-14 11:34:57', NULL, NULL, NULL),
(161, 32, 153, 44, 18.79618949976505, '2025-06-14 11:34:57', NULL, NULL, NULL),
(162, 32, 153, 44, 18.79618949976505, '2025-06-14 11:34:58', NULL, NULL, NULL),
(163, 32, 153, 44, 18.79618949976505, '2025-06-14 11:34:59', NULL, NULL, NULL),
(164, 32, 153, 44, 18.79618949976505, '2025-06-14 11:34:59', NULL, NULL, NULL),
(165, 32, 153, 44, 18.79618949976505, '2025-06-14 11:34:59', NULL, NULL, NULL),
(166, 32, 153, 44, 18.79618949976505, '2025-06-14 11:35:00', NULL, NULL, NULL),
(167, 32, 153, 44, 18.79618949976505, '2025-06-14 11:35:00', NULL, NULL, NULL),
(168, 32, 153, 44, 18.79618949976505, '2025-06-14 11:35:00', NULL, NULL, NULL),
(169, 32, 153, 44, 18.79618949976505, '2025-06-14 11:35:00', NULL, NULL, NULL),
(170, 32, 153, 44, 18.79618949976505, '2025-06-14 11:35:01', NULL, NULL, NULL),
(171, 32, 153, 44, 18.79618949976505, '2025-06-14 11:35:01', NULL, NULL, NULL),
(172, 32, 153, 44, 18.79618949976505, '2025-06-14 11:35:01', NULL, NULL, NULL),
(173, 32, 153, 44, 18.79618949976505, '2025-06-14 11:35:02', NULL, NULL, NULL),
(174, 32, 153, 44, 18.79618949976505, '2025-06-14 11:35:02', NULL, NULL, NULL),
(175, 32, 153, 44, 18.79618949976505, '2025-06-14 11:35:03', NULL, NULL, NULL),
(176, 32, 153, 44, 18.79618949976505, '2025-06-14 11:35:03', NULL, NULL, NULL),
(177, 32, 153, 44, 18.79618949976505, '2025-06-14 11:35:20', NULL, NULL, NULL),
(178, 32, 154, 44, 18.55287569573284, '2025-06-14 11:35:21', NULL, NULL, NULL),
(179, 32, 154, 44, 18.55287569573284, '2025-06-14 11:35:22', NULL, NULL, NULL),
(180, 32, 154, 44, 18.55287569573284, '2025-06-14 11:35:23', NULL, NULL, NULL),
(181, 32, 154, 44, 18.55287569573284, '2025-06-14 11:35:24', NULL, NULL, NULL),
(182, 32, 154, 44, 18.55287569573284, '2025-06-14 11:35:24', NULL, NULL, NULL),
(183, 32, 154, 44, 18.55287569573284, '2025-06-14 11:35:25', NULL, NULL, NULL),
(184, 32, 154, 44, 18.55287569573284, '2025-06-14 11:35:25', NULL, NULL, NULL),
(185, 32, 154, 44, 18.55287569573284, '2025-06-14 11:35:27', NULL, NULL, NULL),
(186, 38, 167, 61, 21.872422819032593, '2025-06-14 11:43:01', NULL, NULL, NULL),
(187, 32, 154, 44, 18.55287569573284, '2025-06-14 15:18:50', NULL, NULL, NULL),
(188, 40, 157, 50, 20.28479857195018, '2025-06-14 21:21:28', NULL, NULL, NULL),
(189, 39, 150, 42, 18.666666666666668, '2025-06-14 21:22:08', NULL, NULL, NULL),
(190, 41, 150, 50, 22.22222222222222, '2025-06-14 21:26:16', NULL, NULL, NULL),
(191, 33, 163, 43, 16.184274906846326, '2025-06-14 22:27:49', NULL, NULL, NULL),
(192, 25, 152, 52, 22.506925207756233, '2025-06-15 19:40:45', NULL, NULL, NULL),
(193, 35, 151, 40, 17.543090215341433, '2025-06-15 20:48:39', NULL, NULL, NULL),
(194, 38, 167, 61, 21.872422819032593, '2025-06-15 20:49:34', NULL, NULL, NULL),
(195, 35, 151, 40, 17.543090215341433, '2025-06-15 20:52:30', NULL, NULL, NULL),
(196, 37, 160, 50, 19.531249999999996, '2025-06-15 20:53:39', NULL, NULL, NULL),
(197, 36, 150, 49, 21.77777777777778, '2025-06-15 20:53:48', NULL, NULL, NULL),
(198, 41, 150, 50, 22.22222222222222, '2025-06-17 10:48:00', NULL, NULL, NULL),
(199, 39, 150, 42, 18.666666666666668, '2025-06-17 10:50:16', NULL, NULL, NULL),
(200, 35, 151, 40, 17.543090215341433, '2025-06-17 10:50:56', NULL, NULL, NULL),
(201, 34, 150, 47, 20.88888888888889, '2025-06-17 10:51:25', NULL, NULL, NULL),
(202, 40, 157, 50, 20.28479857195018, '2025-06-17 10:52:24', NULL, NULL, NULL),
(203, 36, 150, 49, 21.77777777777778, '2025-06-17 10:52:47', NULL, NULL, NULL),
(204, 29, 157, 57, 23.124670372023203, '2025-06-17 10:55:22', NULL, NULL, NULL),
(205, 33, 163, 43, 16.184274906846326, '2025-06-17 10:55:57', NULL, NULL, NULL),
(206, 30, 161, 44, 16.97465375564214, '2025-06-17 10:56:46', NULL, NULL, NULL),
(207, 37, 160, 50, 19.531249999999996, '2025-06-17 10:58:12', NULL, NULL, NULL),
(208, 25, 152, 52, 22.506925207756233, '2025-06-17 11:18:11', NULL, NULL, NULL),
(209, 25, 152, 52, 22.506925207756233, '2025-06-18 21:02:53', NULL, NULL, NULL),
(210, 30, 161, 44, 16.97465375564214, '2025-06-19 10:31:41', NULL, NULL, NULL),
(211, 43, 153, 55, 23.49523687470631, '2025-06-19 10:41:20', NULL, NULL, NULL),
(212, 43, 153, 55, 23.49523687470631, '2025-06-19 11:08:49', NULL, NULL, NULL),
(213, 40, 157, 50, 20.28479857195018, '2025-06-19 11:39:49', NULL, NULL, NULL),
(214, 25, 152, 52, 22.506925207756233, '2025-06-19 12:27:20', NULL, NULL, NULL),
(215, 34, 150, 47, 20.88888888888889, '2025-06-19 12:57:18', NULL, NULL, NULL),
(216, 44, 130, 30, 17.751479289940825, '2025-06-19 13:18:27', NULL, NULL, NULL),
(217, 44, 130, 30, 17.751479289940825, '2025-06-19 13:31:38', NULL, NULL, NULL),
(218, 43, 153, 55, 23.49523687470631, '2025-06-19 14:10:56', NULL, NULL, NULL),
(219, 27, 193, 122, 32.75255711562727, '2025-06-19 18:27:28', NULL, NULL, NULL),
(220, 27, 193, 75, 20.13476871862332, '2025-06-19 18:27:29', NULL, NULL, NULL),
(221, 27, 193, 122, 32.75255711562727, '2025-06-19 18:27:29', NULL, NULL, NULL),
(222, 27, 193, 75, 20.13476871862332, '2025-06-19 18:27:30', NULL, NULL, NULL),
(223, 27, 193, 75, 20.13476871862332, '2025-06-19 18:27:30', NULL, NULL, NULL),
(224, 27, 193, 75, 20.13476871862332, '2025-06-19 18:27:30', NULL, NULL, NULL),
(225, 27, 193, 75, 20.13476871862332, '2025-06-19 18:27:30', NULL, NULL, NULL),
(226, 27, 193, 75, 20.13476871862332, '2025-06-19 18:27:31', NULL, NULL, NULL),
(227, 27, 193, 75, 20.13476871862332, '2025-06-19 18:27:31', NULL, NULL, NULL),
(228, 27, 193, 75, 20.13476871862332, '2025-06-19 18:27:31', NULL, NULL, NULL),
(229, 27, 193, 75, 20.13476871862332, '2025-06-19 18:27:31', NULL, NULL, NULL),
(230, 27, 193, 75, 20.13476871862332, '2025-06-19 18:27:32', NULL, NULL, NULL),
(231, 27, 193, 75, 20.13476871862332, '2025-06-19 18:27:32', NULL, NULL, NULL),
(232, 27, 193, 75, 20.13476871862332, '2025-06-19 18:27:32', NULL, NULL, NULL),
(233, 27, 193, 75, 20.13476871862332, '2025-06-19 18:27:33', NULL, NULL, NULL),
(234, 27, 193, 75, 20.13476871862332, '2025-06-19 18:27:33', NULL, NULL, NULL),
(235, 27, 193, 75, 20.13476871862332, '2025-06-19 18:27:33', NULL, NULL, NULL),
(236, 25, 152, 52, 22.506925207756233, '2025-06-19 19:33:42', NULL, NULL, NULL),
(237, 25, 152, 52, 22.506925207756233, '2025-06-19 19:54:47', NULL, NULL, NULL),
(238, 25, 152, 52, 22.506925207756233, '2025-06-19 21:27:26', NULL, NULL, NULL),
(239, 27, 193, 75, 20.13476871862332, '2025-06-19 23:15:06', NULL, NULL, NULL),
(240, 27, 193, 75, 20.13476871862332, '2025-06-19 23:26:36', NULL, NULL, NULL),
(241, 25, 152, 52, 22.506925207756233, '2025-06-20 00:02:23', NULL, NULL, NULL),
(242, 27, 193, 75, 20.13476871862332, '2025-06-20 12:24:51', NULL, NULL, NULL),
(243, 25, 152, 52, 22.506925207756233, '2025-06-20 13:09:41', NULL, NULL, NULL),
(244, 38, 167, 61, 21.872422819032593, '2025-06-20 13:18:10', NULL, NULL, NULL),
(245, 27, 193, 75, 20.13476871862332, '2025-06-20 13:41:55', NULL, NULL, NULL),
(246, 27, 193, 75, 20.13476871862332, '2025-06-20 13:44:05', NULL, NULL, NULL),
(247, 27, 193, 75, 20.13476871862332, '2025-06-20 13:44:06', NULL, NULL, NULL),
(248, 27, 193, 75, 20.13476871862332, '2025-06-20 13:44:08', NULL, NULL, NULL),
(249, 27, 193, 75, 20.13476871862332, '2025-06-20 13:44:47', NULL, NULL, NULL),
(250, 27, 193, 75, 20.13476871862332, '2025-06-20 13:44:49', NULL, NULL, NULL),
(251, 27, 193, 75, 20.13476871862332, '2025-06-20 13:44:50', NULL, NULL, NULL),
(252, 25, 152, 52, 22.506925207756233, '2025-06-20 13:50:00', NULL, NULL, NULL),
(253, 27, 193, 75, 20.13476871862332, '2025-06-20 13:54:58', NULL, NULL, NULL),
(254, 37, 160, 50, 19.531249999999996, '2025-06-20 14:04:43', NULL, NULL, NULL),
(255, 30, 161, 44, 16.97465375564214, '2025-06-20 14:05:46', NULL, NULL, NULL),
(256, 38, 167, 61, 21.872422819032593, '2025-06-20 14:06:11', NULL, NULL, NULL),
(257, 39, 150, 42, 18.666666666666668, '2025-06-20 14:06:31', NULL, NULL, NULL),
(258, 27, 193, 75, 20.13476871862332, '2025-06-20 14:06:52', NULL, NULL, NULL),
(259, 27, 194, 75, 19.927728770326283, '2025-06-20 14:06:53', NULL, NULL, NULL),
(260, 27, 194, 75, 19.927728770326283, '2025-06-20 14:06:53', NULL, NULL, NULL),
(261, 27, 194, 75, 19.927728770326283, '2025-06-20 14:06:54', NULL, NULL, NULL),
(262, 27, 194, 75, 19.927728770326283, '2025-06-20 14:06:54', NULL, NULL, NULL),
(263, 27, 194, 75, 19.927728770326283, '2025-06-20 14:06:55', NULL, NULL, NULL),
(264, 27, 194, 75, 19.927728770326283, '2025-06-20 14:06:55', NULL, NULL, NULL),
(265, 27, 194, 75, 19.927728770326283, '2025-06-20 14:06:56', NULL, NULL, NULL),
(266, 27, 194, 75, 19.927728770326283, '2025-06-20 14:06:56', NULL, NULL, NULL),
(267, 27, 194, 75, 19.927728770326283, '2025-06-20 14:06:56', NULL, NULL, NULL),
(268, 27, 194, 75, 19.927728770326283, '2025-06-20 14:06:57', NULL, NULL, NULL),
(269, 27, 194, 75, 19.927728770326283, '2025-06-20 14:06:57', NULL, NULL, NULL),
(270, 27, 194, 75, 19.927728770326283, '2025-06-20 14:07:04', NULL, NULL, NULL),
(271, 27, 193, 75, 20.13476871862332, '2025-06-20 14:07:06', NULL, NULL, NULL),
(272, 27, 193, 75, 20.13476871862332, '2025-06-20 14:07:08', NULL, NULL, NULL),
(273, 33, 163, 43, 16.184274906846326, '2025-06-20 14:07:11', NULL, NULL, NULL),
(274, 36, 150, 49, 21.77777777777778, '2025-06-20 14:07:44', NULL, NULL, NULL),
(275, 32, 154, 44, 18.55287569573284, '2025-06-20 14:08:37', NULL, NULL, NULL),
(276, 34, 150, 47, 20.88888888888889, '2025-06-20 14:09:01', NULL, NULL, NULL),
(277, 29, 157, 57, 23.124670372023203, '2025-06-20 14:11:06', NULL, NULL, NULL),
(278, 32, 154, 44, 18.55287569573284, '2025-06-20 14:17:18', NULL, NULL, NULL),
(279, 27, 193, 75, 20.13476871862332, '2025-06-20 14:18:48', NULL, NULL, NULL),
(280, 35, 151, 40, 17.543090215341433, '2025-06-20 14:19:11', NULL, NULL, NULL),
(281, 40, 157, 50, 20.28479857195018, '2025-06-20 14:20:09', NULL, NULL, NULL),
(282, 43, 153, 55, 23.49523687470631, '2025-06-20 14:20:43', NULL, NULL, NULL),
(283, 27, 193, 75, 20.13476871862332, '2025-06-20 14:21:10', NULL, NULL, NULL),
(284, 27, 193, 75, 20.13476871862332, '2025-06-20 14:21:33', NULL, NULL, NULL),
(285, 27, 194, 75, 19.927728770326283, '2025-06-20 14:21:34', NULL, NULL, NULL),
(286, 27, 194, 75, 19.927728770326283, '2025-06-20 14:21:35', NULL, NULL, NULL),
(287, 27, 194, 75, 19.927728770326283, '2025-06-20 14:21:35', NULL, NULL, NULL),
(288, 27, 194, 75, 19.927728770326283, '2025-06-20 14:21:36', NULL, NULL, NULL),
(289, 27, 194, 75, 19.927728770326283, '2025-06-20 14:21:37', NULL, NULL, NULL),
(290, 36, 150, 49, 21.77777777777778, '2025-06-20 14:30:31', NULL, NULL, NULL),
(291, 33, 163, 43, 16.184274906846326, '2025-06-20 14:40:33', NULL, NULL, NULL),
(292, 25, 152, 52, 22.506925207756233, '2025-06-20 15:33:55', NULL, NULL, NULL),
(293, 27, 194, 75, 19.927728770326283, '2025-06-20 17:26:04', NULL, NULL, NULL),
(294, 27, 194, 75, 19.927728770326283, '2025-06-20 17:33:55', NULL, NULL, NULL),
(295, 27, 194, 75, 19.927728770326283, '2025-06-20 17:38:20', NULL, NULL, NULL),
(296, 27, 194, 75, 19.927728770326283, '2025-06-20 18:05:22', NULL, NULL, NULL),
(297, 27, 193, 75, 20.13476871862332, '2025-06-20 18:05:23', NULL, NULL, NULL),
(298, 27, 193, 75, 20.13476871862332, '2025-06-20 18:05:25', NULL, NULL, NULL),
(299, 25, 152, 52, 22.506925207756233, '2025-06-20 20:17:50', NULL, NULL, NULL),
(300, 25, 152, 52, 22.506925207756233, '2025-06-20 20:20:59', NULL, NULL, NULL),
(301, 44, 130, 30, 17.751479289940825, '2025-06-20 20:44:39', NULL, NULL, NULL),
(302, 27, 193, 75, 20.13476871862332, '2025-06-20 20:52:20', NULL, NULL, NULL),
(303, 39, 150, 42, 18.666666666666668, '2025-06-20 21:01:00', NULL, NULL, NULL),
(304, 27, 193, 75, 20.13476871862332, '2025-06-20 21:58:44', NULL, NULL, NULL),
(305, 41, 150, 50, 22.22222222222222, '2025-06-20 22:17:41', NULL, NULL, NULL),
(306, 27, 193, 75, 20.13476871862332, '2025-06-20 23:15:50', NULL, NULL, NULL),
(307, 27, 193, 75, 20.13476871862332, '2025-06-21 00:45:52', NULL, NULL, NULL),
(308, 41, 150, 50, 22.22222222222222, '2025-06-21 12:35:14', NULL, NULL, NULL),
(309, 39, 150, 42, 18.666666666666668, '2025-06-21 12:35:43', NULL, NULL, NULL),
(310, 35, 151, 40, 17.543090215341433, '2025-06-21 12:36:13', NULL, NULL, NULL),
(311, 38, 167, 61, 21.872422819032593, '2025-06-21 12:37:41', NULL, NULL, NULL),
(312, 30, 161, 44, 16.97465375564214, '2025-06-21 12:37:44', NULL, NULL, NULL),
(313, 40, 157, 50, 20.28479857195018, '2025-06-21 12:38:15', NULL, NULL, NULL),
(314, 37, 160, 50, 19.531249999999996, '2025-06-21 12:39:04', NULL, NULL, NULL),
(315, 36, 150, 49, 21.77777777777778, '2025-06-21 12:39:58', NULL, NULL, NULL),
(316, 33, 163, 43, 16.184274906846326, '2025-06-21 12:41:45', NULL, NULL, NULL),
(317, 32, 154, 44, 18.55287569573284, '2025-06-21 12:43:17', NULL, NULL, NULL),
(318, 29, 157, 57, 23.124670372023203, '2025-06-21 12:44:43', NULL, NULL, NULL),
(319, 44, 130, 30, 17.751479289940825, '2025-06-21 12:44:58', NULL, NULL, NULL),
(320, 43, 153, 55, 23.49523687470631, '2025-06-21 12:46:22', NULL, NULL, NULL),
(321, 34, 150, 47, 20.88888888888889, '2025-06-21 14:01:00', NULL, NULL, NULL),
(322, 27, 193, 75, 20.13476871862332, '2025-06-23 01:11:25', NULL, NULL, NULL),
(323, 27, 193, 75, 20.13476871862332, '2025-06-23 13:07:35', NULL, NULL, NULL),
(324, 32, 154, 44, 18.55287569573284, '2025-06-25 10:43:34', NULL, NULL, NULL),
(325, 30, 161, 44, 16.97465375564214, '2025-06-26 11:17:36', NULL, NULL, NULL),
(326, 25, 152, 52, 22.506925207756233, '2025-06-28 12:00:38', NULL, NULL, NULL),
(327, 35, 151, 40, 17.543090215341433, '2025-06-28 13:21:34', NULL, NULL, NULL),
(328, 38, 167, 61, 21.872422819032593, '2025-06-28 13:27:38', NULL, NULL, NULL),
(329, 36, 150, 49, 21.77777777777778, '2025-06-28 13:27:42', NULL, NULL, NULL),
(330, 41, 150, 50, 22.22222222222222, '2025-06-28 13:28:58', NULL, NULL, NULL),
(331, 37, 160, 50, 19.531249999999996, '2025-06-28 13:34:38', NULL, NULL, NULL),
(332, 40, 157, 50, 20.28479857195018, '2025-06-28 13:53:24', NULL, NULL, NULL),
(333, 39, 150, 42, 18.666666666666668, '2025-06-28 13:57:13', NULL, NULL, NULL),
(334, 27, 193, 75, 20.13476871862332, '2025-06-28 15:50:08', NULL, NULL, NULL),
(335, 33, 163, 43, 16.184274906846326, '2025-06-28 23:26:45', NULL, NULL, NULL),
(336, 25, 152, 52, 22.506925207756233, '2025-06-29 01:49:06', NULL, NULL, NULL),
(337, 29, 157, 57, 23.124670372023203, '2025-06-29 02:42:56', NULL, NULL, NULL),
(338, 27, 193, 75, 20.13476871862332, '2025-06-29 15:46:39', NULL, NULL, NULL),
(339, 43, 153, 55, 23.49523687470631, '2025-06-30 09:22:24', NULL, NULL, NULL),
(340, 44, 130, 30, 17.751479289940825, '2025-06-30 20:12:06', NULL, NULL, NULL),
(341, 30, 161, 44, 16.97465375564214, '2025-07-01 11:16:20', NULL, NULL, NULL),
(342, 33, 163, 43, 16.184274906846326, '2025-07-03 10:50:08', NULL, NULL, NULL),
(343, 33, 163, 43, 16.184274906846326, '2025-07-03 10:50:10', NULL, NULL, NULL),
(344, 33, 163, 43, 16.184274906846326, '2025-07-03 10:50:10', NULL, NULL, NULL),
(345, 33, 163, 43, 16.184274906846326, '2025-07-03 10:50:11', NULL, NULL, NULL),
(346, 33, 163, 43, 16.184274906846326, '2025-07-03 10:50:12', NULL, NULL, NULL),
(347, 33, 163, 43, 16.184274906846326, '2025-07-03 10:50:12', NULL, NULL, NULL),
(348, 33, 163, 43, 16.184274906846326, '2025-07-03 10:50:14', NULL, NULL, NULL),
(349, 33, 163, 43, 16.184274906846326, '2025-07-03 10:50:14', NULL, NULL, NULL),
(350, 33, 163, 43, 16.184274906846326, '2025-07-03 10:50:15', NULL, NULL, NULL),
(351, 33, 163, 43, 16.184274906846326, '2025-07-03 10:50:58', NULL, NULL, NULL),
(352, 33, 163, 43, 16.184274906846326, '2025-07-03 10:51:00', NULL, NULL, NULL),
(353, 33, 163, 43, 16.184274906846326, '2025-07-03 10:51:01', NULL, NULL, NULL),
(354, 32, 154, 44, 18.55287569573284, '2025-07-03 15:17:24', NULL, NULL, NULL),
(355, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:28', NULL, NULL, NULL),
(356, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:29', NULL, NULL, NULL),
(357, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:29', NULL, NULL, NULL),
(358, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:30', NULL, NULL, NULL),
(359, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:31', NULL, NULL, NULL),
(360, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:31', NULL, NULL, NULL),
(361, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:31', NULL, NULL, NULL),
(362, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:42', NULL, NULL, NULL),
(363, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:43', NULL, NULL, NULL),
(364, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:44', NULL, NULL, NULL),
(365, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:44', NULL, NULL, NULL),
(366, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:45', NULL, NULL, NULL),
(367, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:45', NULL, NULL, NULL),
(368, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:46', NULL, NULL, NULL),
(369, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:46', NULL, NULL, NULL),
(370, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:46', NULL, NULL, NULL),
(371, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:46', NULL, NULL, NULL),
(372, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:47', NULL, NULL, NULL),
(373, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:47', NULL, NULL, NULL),
(374, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:47', NULL, NULL, NULL),
(375, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:47', NULL, NULL, NULL),
(376, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:48', NULL, NULL, NULL),
(377, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:48', NULL, NULL, NULL),
(378, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:49', NULL, NULL, NULL),
(379, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:49', NULL, NULL, NULL),
(380, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:49', NULL, NULL, NULL),
(381, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:49', NULL, NULL, NULL),
(382, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:49', NULL, NULL, NULL),
(383, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:50', NULL, NULL, NULL),
(384, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:50', NULL, NULL, NULL),
(385, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:50', NULL, NULL, NULL),
(386, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:50', NULL, NULL, NULL),
(387, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:51', NULL, NULL, NULL),
(388, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:51', NULL, NULL, NULL),
(389, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:51', NULL, NULL, NULL),
(390, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:52', NULL, NULL, NULL),
(391, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:52', NULL, NULL, NULL),
(392, 44, 130, 30, 17.751479289940825, '2025-07-04 11:35:52', NULL, NULL, NULL),
(393, 27, 193, 75, 20.13476871862332, '2025-07-04 21:03:24', NULL, NULL, NULL),
(394, 27, 193, 75, 20.13476871862332, '2025-07-04 21:03:24', NULL, NULL, NULL),
(395, 27, 193, 75, 20.13476871862332, '2025-07-04 21:07:51', NULL, NULL, NULL),
(396, 27, 193, 75, 20.13476871862332, '2025-07-04 22:55:42', NULL, NULL, NULL),
(397, 27, 193, 75, 20.13476871862332, '2025-07-05 01:21:55', NULL, NULL, NULL),
(398, 27, 193, 75, 20.13476871862332, '2025-07-05 01:33:49', NULL, NULL, NULL),
(399, 27, 193, 75, 20.13476871862332, '2025-07-05 01:49:25', NULL, NULL, NULL),
(400, 27, 193, 75, 20.13476871862332, '2025-07-05 02:00:32', NULL, NULL, NULL),
(401, 27, 193, 75, 20.13476871862332, '2025-07-05 02:00:33', NULL, NULL, NULL),
(402, 27, 193, 75, 20.13476871862332, '2025-07-05 02:00:33', NULL, NULL, NULL),
(403, 27, 193, 75, 20.13476871862332, '2025-07-05 02:06:09', NULL, NULL, NULL),
(404, 27, 193, 75, 20.13476871862332, '2025-07-05 11:39:00', NULL, NULL, NULL),
(405, 27, 193, 75, 20.13476871862332, '2025-07-05 11:56:06', NULL, NULL, NULL),
(406, 27, 193, 75, 20.13476871862332, '2025-07-05 12:15:45', NULL, NULL, NULL),
(407, 27, 193, 75, 20.13476871862332, '2025-07-05 12:15:45', NULL, NULL, NULL),
(408, 27, 193, 75, 20.13476871862332, '2025-07-05 12:24:30', NULL, NULL, NULL),
(409, 27, 193, 75, 20.13476871862332, '2025-07-05 12:24:30', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `calorie_goals`
--

CREATE TABLE `calorie_goals` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `daily_calories` int(11) NOT NULL,
  `protein_goal` decimal(5,2) DEFAULT NULL,
  `carbs_goal` decimal(5,2) DEFAULT NULL,
  `fat_goal` decimal(5,2) DEFAULT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `calorie_logs`
--

CREATE TABLE `calorie_logs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `log_date` date NOT NULL,
  `meal_type` enum('breakfast','lunch','dinner','snack') COLLATE utf8mb4_unicode_ci NOT NULL,
  `food_item_id` bigint(20) UNSIGNED DEFAULT NULL,
  `food_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `calories` int(11) NOT NULL,
  `protein` decimal(5,2) DEFAULT NULL,
  `carbs` decimal(5,2) DEFAULT NULL,
  `fat` decimal(5,2) DEFAULT NULL,
  `quantity` decimal(5,2) NOT NULL DEFAULT '1.00',
  `serving_size` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `chats`
--

CREATE TABLE `chats` (
  `id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `admin_id` bigint(20) UNSIGNED DEFAULT '0',
  `last_message` text COLLATE utf8mb4_unicode_ci,
  `last_message_time` timestamp NULL DEFAULT NULL,
  `unread_user` int(11) DEFAULT '0',
  `unread_admin` int(11) DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `courses`
--

CREATE TABLE `courses` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `thumbnail_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `duration_weeks` int(11) NOT NULL DEFAULT '1',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `is_premium` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `category` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `discount_percentage` int(11) NOT NULL DEFAULT '0',
  `is_featured` tinyint(1) NOT NULL DEFAULT '0',
  `whatsapp_number` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `whatsapp_message_prefix` text COLLATE utf8mb4_unicode_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `courses`
--

INSERT INTO `courses` (`id`, `title`, `description`, `thumbnail_url`, `duration_weeks`, `price`, `is_premium`, `is_active`, `created_at`, `updated_at`, `category`, `discount_percentage`, `is_featured`, `whatsapp_number`, `whatsapp_message_prefix`) VALUES
(2, 'Weight Loss', '', 'https://www.spartan.com/cdn/shop/articles/at-home-workout-2_1200x.jpg?v=1595895885', 6, 1500.00, 1, 1, '2025-05-09 18:51:24', '2025-05-09 18:51:24', 'General', 0, 0, '', ''),
(3, 'Skinny Fat Loss', '', '', 6, 1500.00, 0, 1, '2025-05-09 18:51:24', '2025-05-09 18:51:24', 'General', 0, 0, '', ''),
(13, 'NO JUMPING CARDIO', '', '', 6, 1500.00, 0, 0, NULL, NULL, 'General', 0, 0, '', ''),
(14, 'Weight Gain', '', '', 6, 1500.00, 0, 0, NULL, NULL, 'General', 0, 0, '', '');

-- --------------------------------------------------------

--
-- Table structure for table `course_purchases`
--

CREATE TABLE `course_purchases` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `course_id` bigint(20) UNSIGNED NOT NULL,
  `purchase_date` datetime NOT NULL,
  `amount_paid` decimal(10,2) NOT NULL,
  `payment_method` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `transaction_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_purchases`
--

INSERT INTO `course_purchases` (`id`, `user_id`, `course_id`, `purchase_date`, `amount_paid`, `payment_method`, `transaction_id`, `status`) VALUES
(1, 27, 2, '2025-06-13 03:31:29', 49.00, 'upi', '121221222', 'completed'),
(2, 28, 2, '2025-06-13 11:33:12', 49.00, 'upi', '232132313123', 'completed'),
(3, 29, 2, '2025-06-14 10:23:17', 1499.00, 'upi', '76532235678', 'completed'),
(4, 30, 2, '2025-06-14 10:33:57', 1499.00, 'upi', '3456789', 'completed'),
(5, 32, 2, '2025-06-14 10:51:23', 1499.00, 'upi', '12345', 'completed'),
(6, 33, 2, '2025-06-14 10:57:26', 1499.00, 'upi', '3589000', 'completed'),
(7, 34, 2, '2025-06-14 11:00:55', 1499.00, 'upi', '12356', 'completed'),
(8, 35, 2, '2025-06-14 11:04:10', 1499.00, 'upi', '6545625', 'completed'),
(9, 36, 2, '2025-06-14 11:06:44', 1499.00, 'upi', '178039', 'completed'),
(10, 37, 2, '2025-06-14 11:18:39', 1499.00, 'upi', '12345', 'completed'),
(11, 38, 2, '2025-06-14 11:41:54', 1499.00, 'upi', '521667', 'completed'),
(12, 39, 2, '2025-06-14 20:54:07', 1499.00, 'upi', '2443', 'completed'),
(13, 40, 2, '2025-06-14 21:07:21', 1499.00, 'upi', '23456', 'completed'),
(14, 41, 2, '2025-06-14 21:18:40', 1500.00, 'upi', '8765', 'completed'),
(15, 43, 3, '2025-06-19 10:38:12', 0.00, '', '', 'completed'),
(16, 44, 2, '2025-06-19 13:14:57', 0.00, '', '', 'completed');

-- --------------------------------------------------------

--
-- Table structure for table `course_videos`
--

CREATE TABLE `course_videos` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `course_id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `video_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `thumbnail_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `duration_minutes` int(11) DEFAULT NULL,
  `week_number` int(11) NOT NULL DEFAULT '1',
  `sequence_number` int(11) NOT NULL DEFAULT '0',
  `video_provider` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `video_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `video_embed_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_videos`
--

INSERT INTO `course_videos` (`id`, `course_id`, `title`, `description`, `video_url`, `thumbnail_url`, `duration_minutes`, `week_number`, `sequence_number`, `video_provider`, `video_id`, `video_embed_url`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 2, 'EPISODE 1', '', 'https://vimeo.com/**********', 'https://shorturl.at/wZ5uG', 14, 1, 1, NULL, NULL, NULL, 1, NULL, NULL),
(2, 2, 'EPISODE 2', '', 'https://vimeo.com/**********?share=copy', 'https://shorturl.at/jcnpl', 25, 2, 2, NULL, NULL, NULL, 1, NULL, NULL),
(4, 2, 'EPISODE 3', 'sdfs', 'https://vimeo.com/**********', 'https://shorturl.at/IBoGT', 28, 3, 3, NULL, NULL, NULL, 1, NULL, NULL),
(7, 2, 'EPISODE 4', '', 'https://vimeo.com/**********', 'https://shorturl.at/BXKn5', 31, 4, 1, NULL, NULL, NULL, 1, NULL, NULL),
(8, 2, 'EPISODE 5', '', 'https://vimeo.com/**********?share=copy', '', 37, 5, 1, NULL, NULL, NULL, 1, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `device_sessions`
--

CREATE TABLE `device_sessions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `device_id` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `device_info` json DEFAULT NULL,
  `first_login` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_activity` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  `revoked_by_admin` int(11) DEFAULT NULL,
  `revoked_at` timestamp NULL DEFAULT NULL,
  `revocation_reason` text COLLATE utf8_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

--
-- Dumping data for table `device_sessions`
--

INSERT INTO `device_sessions` (`id`, `user_id`, `device_id`, `device_info`, `first_login`, `last_activity`, `is_active`, `revoked_by_admin`, `revoked_at`, `revocation_reason`, `created_at`, `updated_at`) VALUES
(1, 25, 'QP1A.190711.020', '{\"platform\": \"unknown\", \"first_seen\": \"2025-05-28 12:37:10.000000\"}', '2025-05-28 07:07:10', '2025-05-28 07:07:10', 1, NULL, NULL, NULL, '2025-06-05 16:39:18', '2025-06-05 16:39:18');

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `food_items`
--

CREATE TABLE `food_items` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `calories` int(11) NOT NULL,
  `protein` decimal(5,2) DEFAULT NULL,
  `carbs` decimal(5,2) DEFAULT NULL,
  `fat` decimal(5,2) DEFAULT NULL,
  `serving_size` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_verified` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `job_batches`
--

CREATE TABLE `job_batches` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `messages`
--

CREATE TABLE `messages` (
  `id` int(11) NOT NULL,
  `chat_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sender_id` bigint(20) UNSIGNED NOT NULL,
  `receiver_id` bigint(20) UNSIGNED NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_admin` tinyint(1) NOT NULL DEFAULT '0',
  `type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'text',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'sent',
  `sent_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '0001_01_01_000000_create_users_table', 1),
(2, '0001_01_01_000001_create_cache_table', 1),
(3, '0001_01_01_000002_create_jobs_table', 1),
(4, '2025_05_09_091152_create_users_table', 1),
(5, '2025_05_09_091206_create_admin_users_table', 1),
(6, '2025_05_09_091206_create_api_tokens_table', 1),
(7, '2025_05_09_091206_create_courses_table', 1),
(8, '2025_05_09_091206_create_pending_users_table', 1),
(9, '2025_05_09_091207_create_calorie_logs_table', 1),
(10, '2025_05_09_091207_create_course_videos_table', 1),
(11, '2025_05_09_091207_create_food_items_table', 1),
(12, '2025_05_09_091207_create_motivational_quotes_table', 1),
(13, '2025_05_09_091207_create_quote_settings_table', 1),
(14, '2025_05_09_091207_create_user_course_enrollments_table', 1),
(15, '2025_05_09_091207_create_user_food_items_table', 1),
(16, '2025_05_09_091207_create_user_quote_preferences_table', 1),
(17, '2025_05_09_091207_create_user_video_progress_table', 1),
(18, '2025_05_09_091208_create_bmi_records_table', 1),
(19, '2025_05_09_091208_create_calorie_goals_table', 1),
(20, '2025_05_09_091208_create_settings_table', 1),
(21, '2025_05_09_091208_create_streak_days_table', 1),
(22, '2025_05_09_091208_create_user_activity_log_table', 1),
(23, '2025_05_09_091208_create_workout_history_table', 1),
(24, '2025_05_09_100019_create_sessions_table', 1),
(25, '2025_05_09_101231_create_personal_access_tokens_table', 1),
(26, '2025_05_09_104919_add_missing_columns_to_courses_table', 1),
(27, '2025_05_09_111956_add_group_column_to_settings_table', 1);

-- --------------------------------------------------------

--
-- Table structure for table `motivational_quotes`
--

CREATE TABLE `motivational_quotes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `quote` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `author` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_ai_generated` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `target_id` int(11) DEFAULT NULL,
  `target_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_read` tinyint(1) DEFAULT '0',
  `read_at` timestamp NULL DEFAULT NULL,
  `recipient_id` int(11) DEFAULT NULL,
  `recipient_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'admin'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `type`, `message`, `target_id`, `target_type`, `created_at`, `is_read`, `read_at`, `recipient_id`, `recipient_type`) VALUES
(1, 'user', 'New user registration: John Doe', 1, 'user', '2025-05-12 00:54:41', 1, '2025-05-12 07:24:56', NULL, 'admin'),
(2, 'system', 'System update completed successfully', NULL, NULL, '2025-05-11 22:54:41', 1, NULL, NULL, 'admin'),
(3, 'course', 'New course \"Advanced Fitness\" has been created', 1, 'course', '2025-05-11 01:54:41', 1, '2025-05-12 07:24:56', NULL, 'admin');

-- --------------------------------------------------------

--
-- Table structure for table `notification_settings`
--

CREATE TABLE `notification_settings` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `email_notifications` tinyint(1) DEFAULT '1',
  `notification_types` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notification_settings`
--

INSERT INTO `notification_settings` (`id`, `admin_id`, `email_notifications`, `notification_types`, `created_at`, `updated_at`) VALUES
(1, 1, 1, '[\"user\",\"system\",\"course\"]', '2025-05-12 07:28:29', '2025-05-12 07:28:29');

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `pending_users`
--

CREATE TABLE `pending_users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `phone_number` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `device_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('pending','approved','not_interested') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `status_updated_at` timestamp NULL DEFAULT NULL,
  `status_updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `pending_users`
--

INSERT INTO `pending_users` (`id`, `name`, `phone_number`, `device_id`, `status`, `status_updated_at`, `status_updated_by`, `created_at`, `updated_at`) VALUES
(1, '', '9656444498', NULL, 'approved', '2025-05-09 21:26:41', NULL, NULL, NULL),
(2, '', '9565656562', NULL, 'approved', '2025-05-09 21:28:58', NULL, NULL, NULL),
(3, '', '65653256565', NULL, 'approved', '2025-05-09 21:38:40', NULL, NULL, NULL),
(4, '', '9494646464', NULL, 'not_interested', '2025-05-10 03:59:36', NULL, NULL, NULL),
(7, '', '9876543213', NULL, 'approved', '2025-05-12 06:30:06', NULL, NULL, NULL),
(8, '', '95599599865', NULL, 'not_interested', '2025-05-29 08:47:53', NULL, NULL, NULL),
(9, 'hshee', '9656666664', 'TP1A.220905.001', 'not_interested', '2025-05-29 08:47:53', NULL, '2025-05-28 02:25:59', NULL),
(10, 'Mahira', '8714151952', 'QP1A.190711.020', 'approved', '2025-05-28 06:31:45', NULL, '2025-05-28 06:31:02', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `pin_usage_history`
--

CREATE TABLE `pin_usage_history` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `pin` char(4) COLLATE utf8mb4_unicode_ci NOT NULL,
  `used_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `pin_usage_history`
--

INSERT INTO `pin_usage_history` (`id`, `user_id`, `pin`, `used_at`) VALUES
(1, 27, '2850', '2025-06-14 19:56:56');

-- --------------------------------------------------------

--
-- Table structure for table `program_enrollments`
--

CREATE TABLE `program_enrollments` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `program_id` int(11) NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `quote_settings`
--

CREATE TABLE `quote_settings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `setting_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_value` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `refresh_tokens`
--

CREATE TABLE `refresh_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `token` varchar(500) COLLATE utf8_unicode_ci NOT NULL,
  `device_id` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `device_fingerprint` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `expires_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_used_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `security_alerts`
--

CREATE TABLE `security_alerts` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `alert_type` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `details` json DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8_unicode_ci,
  `resolved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sessions`
--

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('7quEAx9eO7JmS2YVkeRdMX9983Fp3rjEaa8cUh1K', 1, '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoidjV5cFlaY1FPSUREc3M2WnVFa3YyUlo3OWNQQUsyTnlsUVprS2hTaiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly8xMjcuMC4wLjE6ODAwMC9hZG1pbiI7fXM6MzoidXJsIjthOjA6e31zOjUyOiJsb2dpbl9hZG1pbl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7fQ==', 1746796275),
('c8kAM6vPONCKd5Kt5OujD9tgz8luZ0HfLc7PdPAj', 1, '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiQzZ2Wkl1ZUd6WVpUWTFnOHVXcXFxb2h1UG1CV1J3UmRKdEdiNktvNCI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czozOiJ1cmwiO2E6MDp7fXM6NTI6ImxvZ2luX2FkbWluXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czoyNzoiaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FkbWluIjt9fQ==', 1746796241);

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` text COLLATE utf8mb4_unicode_ci,
  `group` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `key`, `value`, `group`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'site_name', 'KFT Fitness', 'general', 1, NULL, NULL),
(2, 'site_description', 'Fitness tracking and workout management', 'general', 1, NULL, NULL),
(3, 'is_dev_mode', 'false', 'system', 1, NULL, NULL),
(4, 'maintenance_mode', '1', 'system', 1, NULL, NULL),
(5, 'allow_registrations', 'true', 'users', 1, NULL, NULL),
(6, 'default_user_role', 'viewer', 'users', 1, NULL, NULL),
(7, 'contact_email', '<EMAIL>', 'contact', 1, NULL, NULL),
(8, 'max_login_attempts', '5', 'security', 1, NULL, NULL),
(9, 'lockout_time', '30', 'security', 1, NULL, NULL),
(10, 'session_lifetime', '1440', 'security', 1, NULL, NULL),
(12, 'deepseek_enabled', '1', NULL, 1, NULL, NULL),
(15, 'deepseek_api_key', '***********************************', NULL, 1, NULL, NULL),
(16, 'quote_categories', 'motivation, workout, home workout,', NULL, 1, NULL, NULL),
(19, 'moderate_quotes', '1', NULL, 1, NULL, NULL),
(21, 'quote_fallback_source', 'external_api', NULL, 1, NULL, NULL),
(26, 'platform_name', 'KFT Fitness', NULL, 1, NULL, NULL),
(27, 'default_language', 'en', NULL, 1, NULL, NULL),
(28, 'timezone', 'UTC', NULL, 1, NULL, NULL),
(29, 'enable_registration', '1', NULL, 1, NULL, NULL),
(31, 'support_email', '<EMAIL>', NULL, 1, NULL, NULL),
(47, 'password_min_length', '8', NULL, 1, NULL, NULL),
(48, 'require_special', '1', NULL, 1, NULL, NULL),
(49, 'require_number', '1', NULL, 1, NULL, NULL),
(50, 'session_timeout', '30', NULL, 1, NULL, NULL),
(51, 'allow_password_reset', '1', NULL, 1, NULL, NULL),
(52, 'lockout_attempts', '5', NULL, 1, NULL, NULL),
(53, 'enable_2fa', '0', NULL, 1, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `streak_days`
--

CREATE TABLE `streak_days` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `date` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `username` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone_number` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `height` double DEFAULT NULL,
  `weight` double DEFAULT NULL,
  `is_premium` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `last_login` timestamp NULL DEFAULT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  `device_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `gender` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fitness_goal` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `age` int(11) DEFAULT NULL,
  `verification_code` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `verification_expires_at` datetime DEFAULT NULL,
  `profile_image_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `assigned_staff_id` bigint(20) UNSIGNED DEFAULT NULL,
  `pin` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pin_expires_at` datetime DEFAULT NULL,
  `pin_used` tinyint(1) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `username`, `email`, `phone_number`, `email_verified_at`, `password`, `height`, `weight`, `is_premium`, `is_active`, `last_login`, `remember_token`, `created_at`, `updated_at`, `device_id`, `phone`, `gender`, `fitness_goal`, `age`, `verification_code`, `verification_expires_at`, `profile_image_url`, `assigned_staff_id`, `pin`, `pin_expires_at`, `pin_used`) VALUES
(1, 'John Doe', 'john.doe', '<EMAIL>', '', NULL, '$2y$10$TMthQoXyW6Ho62cORnHviebv9vVRNkmKXeBOz6hLF8CZ/aML5hfdq', NULL, NULL, 0, 1, NULL, NULL, '2025-06-15 18:55:09', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0),
(24, 'Farook', 'farook4110', NULL, '+919895035757', NULL, NULL, 188, 76, 1, 1, '2025-06-13 16:58:15', NULL, '2025-05-28 02:29:04', '2025-06-13 16:58:18', 'AP3A.240905.015.A2', '+919895035757', 'male', 'weight_loss', 33, '492059', '2025-05-29 02:29:04', NULL, 7, '9947', NULL, 1),
(25, 'Mahira', 'mahira9993', NULL, '+918714151952', NULL, NULL, 152, 52, 1, 1, '2025-06-28 20:19:02', NULL, '2025-05-28 07:07:10', '2025-06-28 20:19:06', 'QP1A.190711.020', '+918714151952', 'female', 'weight_loss', 27, '480236', '2025-05-29 07:07:10', NULL, 7, '9424', '2026-06-28 20:18:57', 1),
(27, 'jafer sadik', 'sdfwe32', NULL, '+919656444498', NULL, NULL, 193, 75, 1, 1, '2025-07-05 06:54:28', NULL, '2025-06-12 22:01:29', '2025-07-05 06:54:30', 'web_device', '+919656444498', 'male', 'weight_loss', 34, '756313', '2025-06-13 22:01:29', NULL, 7, '4525', '2026-07-05 06:54:04', 1),
(28, 'test', 'test5335', NULL, '+919876543210', NULL, NULL, 160, 100, 1, 1, NULL, NULL, '2025-06-13 06:03:12', NULL, NULL, '+919876543210', 'female', 'weight_loss', 20, '458471', '2025-06-14 06:03:12', NULL, 7, '3283', NULL, 0),
(29, 'soorya', 'soorya@123', NULL, '+919746613507', NULL, NULL, 157, 57, 1, 1, '2025-06-28 21:12:53', NULL, '2025-06-14 04:53:17', '2025-06-28 21:12:56', 'S3RWBS32.125-29-2-4-3', '+919746613507', 'female', 'weight_loss', 22, '253407', '2025-06-15 04:53:17', NULL, 7, '8745', '2026-06-28 07:45:29', 1),
(30, 'Shabnam', 'shabnam6523', NULL, '+918137087075', NULL, NULL, 161, 44, 1, 1, '2025-07-01 05:46:13', NULL, '2025-06-14 05:03:57', '2025-07-01 05:46:20', 'AP3A.240905.015.A2_NC', '+918137087075', 'female', 'weight_loss', 24, '127432', '2025-06-15 05:03:57', NULL, 7, '9344', '2026-07-01 05:46:00', 1),
(32, 'Rajeshwari', 'rajeshwari9423', NULL, '+918848597612', NULL, NULL, 154, 44, 1, 1, '2025-07-03 09:47:20', NULL, '2025-06-14 05:21:23', '2025-07-03 09:47:24', 'AP3A.240905.015.A2', '+918848597612', 'female', 'weight_loss', 25, '973841', '2025-06-15 05:21:23', NULL, 7, '4515', '2026-07-03 09:46:59', 1),
(33, 'Sannidhi K Shetty', 'sannidhikshetty9736', NULL, '+916361356281', NULL, NULL, 163, 43, 1, 1, '2025-06-28 17:56:41', NULL, '2025-06-14 05:27:26', '2025-07-03 05:21:01', 'UP1A.231005.007', '+916361356281', 'female', 'weight_loss', 24, '513282', '2025-06-15 05:27:26', NULL, 5, '2434', '2026-06-28 07:46:55', 1),
(34, 'Sabah', 'sabah3982', NULL, '+919633111952', NULL, NULL, 150, 47, 1, 1, '2025-07-01 07:35:07', NULL, '2025-06-14 05:30:55', '2025-06-21 08:31:00', 'RKQ1.211001.001', '+919633111952', 'female', 'weight_loss', 24, '238962', '2025-06-15 05:30:55', NULL, 7, '7515', '2026-07-01 07:35:02', 1),
(35, 'Kadeejath Rahina Nasreen', 'kadeejathrahinanasreen9067', NULL, '+919972198900', NULL, NULL, 151, 40, 1, 1, '2025-06-28 07:51:30', NULL, '2025-06-14 05:34:10', '2025-06-28 07:51:34', 'AP3A.240905.015.A2_NC', '+919972198900', 'female', 'weight_loss', 24, '936324', '2025-06-15 05:34:10', NULL, 7, '8512', '2026-06-28 07:40:05', 1),
(36, 'Ramya.T', 'ramyat1088', NULL, '+918590996832', NULL, NULL, 150, 49, 1, 1, '2025-06-28 07:57:38', NULL, '2025-06-14 05:36:44', '2025-06-28 07:57:42', 'AP3A.240905.015.A2', '+918590996832', 'female', 'weight_loss', 23, '987394', '2025-06-15 05:36:44', NULL, 7, '6699', '2026-06-28 07:41:52', 1),
(37, 'maariya', 'maariya5510', NULL, '+919526007767', NULL, NULL, 160, 50, 1, 1, '2025-06-28 08:04:33', NULL, '2025-06-14 05:48:39', '2025-06-28 08:04:38', 'T2SRS33.72-22-4-11', '+919526007767', 'female', 'weight_loss', 21, '310794', '2025-06-15 05:48:39', NULL, NULL, '3688', '2026-07-01 06:51:22', 0),
(38, 'Thawhida', 'thawhida2508', NULL, '+919633216952', NULL, NULL, 167, 61, 1, 1, '2025-06-28 07:57:33', NULL, '2025-06-14 06:11:54', '2025-06-28 07:57:38', 'S3RWBS32.125-29-2-4-3', '+919633216952', 'female', 'weight_loss', 24, '903196', '2025-06-15 06:11:54', NULL, 7, '3978', '2026-06-28 07:40:52', 1),
(39, 'Shifa', 'shifa3481', NULL, '+918129283507', NULL, NULL, 150, 42, 1, 1, '2025-06-28 08:27:09', NULL, '2025-06-14 15:24:07', '2025-06-28 08:27:13', 'AP3A.240905.015.A2_NC', '+918129283507', 'female', 'weight_loss', 23, '428011', '2025-06-15 15:24:07', NULL, 7, '6929', '2026-06-28 07:43:38', 1),
(40, 'Nahla', 'nahla1352', NULL, '+918714840774', NULL, NULL, 157, 50, 1, 1, '2025-06-28 08:23:18', NULL, '2025-06-14 15:37:21', '2025-06-28 08:23:24', 'SKQ1.211103.001', '+918714840774', 'female', 'weight_loss', 24, '625897', '2025-06-15 15:37:21', NULL, NULL, '6876', '2026-06-28 07:41:21', 1),
(41, 'Nusrin', 'nusrin7442', NULL, '+919061053507', NULL, NULL, 150, 50, 1, 1, '2025-06-28 07:58:54', NULL, '2025-06-14 15:48:40', '2025-06-28 07:58:58', 'UP1A.231005.007', '+919061053507', '', 'weight_loss', 25, '756365', '2025-06-15 15:48:40', NULL, 7, '3643', '2026-06-28 07:36:25', 1),
(42, 'dfasfs', 'dfasfs4714', NULL, '+912321312321', NULL, NULL, NULL, NULL, 1, 1, NULL, NULL, '2025-06-16 16:02:08', NULL, NULL, '+912321312321', '', '', NULL, '735244', '2025-06-17 16:02:08', NULL, NULL, '0255', NULL, 0),
(43, 'Bilaal', 'bilaal9019', NULL, '+917907149696', NULL, NULL, 153, 55, 1, 1, '2025-06-30 03:52:21', NULL, '2025-06-19 05:08:12', '2025-06-30 03:52:24', 'TP1A.220905.001', '+917907149696', 'male', 'weight_loss', 21, '104665', '2025-06-20 05:08:12', NULL, 7, '8092', '2026-06-28 07:47:16', 1),
(44, 'Deekshitha D', 'deekshitha2591', NULL, '+918951693373', NULL, NULL, 130, 30, 1, 1, '2025-06-30 14:42:01', NULL, '2025-06-19 07:44:57', '2025-07-04 06:05:52', 'UKQ1.230917.001', '+918951693373', 'female', 'weight_loss', 27, '329596', '2025-06-20 07:44:57', NULL, 7, '8718', '2026-06-28 07:47:52', 1),
(45, 'test22e', 'testdontdelete5761', NULL, '+917012056778', NULL, NULL, 122, 221, 1, 1, '2025-06-21 16:23:35', NULL, '2025-06-19 09:29:29', NULL, 'EC737108-FE31-4146-9CF6-B288A7AC94BC', '+917012056778', 'male', 'weight_loss', 11, '659472', '2025-06-20 09:29:29', NULL, 7, '6705', '2026-06-21 16:23:15', 1);

-- --------------------------------------------------------

--
-- Table structure for table `user_activity_log`
--

CREATE TABLE `user_activity_log` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `activity_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `related_id` int(11) DEFAULT NULL,
  `details` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_activity_log`
--

INSERT INTO `user_activity_log` (`id`, `user_id`, `activity_type`, `related_id`, `details`, `created_at`, `updated_at`) VALUES
(55, 27, 'admin_unlock', 2, '{\"admin_id\": 1, \"video_id\": 2, \"course_id\": 2}', '2025-06-14 06:19:50', NULL),
(56, 27, 'video_progress', 1, '{\"action\": \"play\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 30, \"watch_duration\": 30}', '2025-06-14 15:34:18', NULL),
(57, 27, 'video_progress', 1, '{\"action\": \"progress_update\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 120, \"watch_duration\": 120}', '2025-06-14 13:34:18', NULL),
(58, 27, 'video_progress', 1, '{\"action\": \"progress_update\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 300, \"watch_duration\": 300}', '2025-06-14 11:34:18', NULL),
(59, 27, 'video_progress', 1, '{\"action\": \"complete\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": true, \"last_position\": 450, \"watch_duration\": 450}', '2025-06-14 09:34:18', NULL),
(60, 27, 'video_progress', 2, '{\"action\": \"play\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 30, \"watch_duration\": 30}', '2025-06-14 15:34:18', NULL),
(61, 27, 'video_progress', 2, '{\"action\": \"progress_update\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 120, \"watch_duration\": 120}', '2025-06-14 13:34:18', NULL),
(62, 27, 'video_progress', 2, '{\"action\": \"progress_update\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 300, \"watch_duration\": 300}', '2025-06-14 11:34:18', NULL),
(63, 27, 'video_progress', 2, '{\"action\": \"complete\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": true, \"last_position\": 450, \"watch_duration\": 450}', '2025-06-14 09:34:18', NULL),
(64, 27, 'video_progress', 4, '{\"action\": \"play\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 30, \"watch_duration\": 30}', '2025-06-14 15:34:18', NULL),
(65, 27, 'video_progress', 4, '{\"action\": \"progress_update\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 120, \"watch_duration\": 120}', '2025-06-14 13:34:18', NULL),
(66, 27, 'video_progress', 4, '{\"action\": \"progress_update\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 300, \"watch_duration\": 300}', '2025-06-14 11:34:18', NULL),
(67, 27, 'video_progress', 4, '{\"action\": \"complete\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": true, \"last_position\": 450, \"watch_duration\": 450}', '2025-06-14 09:34:18', NULL),
(68, 27, 'video_progress', 7, '{\"action\": \"play\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 30, \"watch_duration\": 30}', '2025-06-14 15:34:18', NULL),
(69, 27, 'video_progress', 7, '{\"action\": \"progress_update\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 120, \"watch_duration\": 120}', '2025-06-14 13:34:18', NULL),
(70, 27, 'video_progress', 7, '{\"action\": \"progress_update\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 300, \"watch_duration\": 300}', '2025-06-14 11:34:18', NULL),
(71, 27, 'video_progress', 7, '{\"action\": \"complete\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": true, \"last_position\": 450, \"watch_duration\": 450}', '2025-06-14 09:34:18', NULL),
(72, 27, 'video_progress', 1, '{\"action\": \"complete\", \"timestamp\": \"2025-06-15 17:58:15\", \"is_completed\": true, \"last_position\": 180, \"watch_duration\": 180}', '2025-06-15 17:58:15', NULL),
(73, 27, 'video_progress', 1, '{\"action\": \"complete\", \"timestamp\": \"2025-06-15 18:04:05\", \"is_completed\": true, \"last_position\": 180, \"watch_duration\": 180}', '2025-06-15 18:04:05', NULL),
(74, 27, 'video_progress', 1, '{\"action\": \"complete\", \"timestamp\": \"2025-06-15 18:14:07\", \"is_completed\": true, \"last_position\": 180, \"watch_duration\": 180}', NULL, NULL),
(75, 27, 'video_progress', 2, '{\"action\": \"progress_update\", \"timestamp\": \"2025-06-18 15:02:46\", \"is_completed\": false, \"last_position\": 30, \"watch_duration\": 30}', '2025-06-18 15:02:46', NULL),
(76, 27, 'video_progress', 2, '{\"action\": \"progress_update\", \"timestamp\": \"2025-06-18 15:34:34\", \"is_completed\": false, \"last_position\": 30, \"watch_duration\": 30}', '2025-06-18 15:34:34', NULL),
(77, 27, 'video_progress', 2, '{\"action\": \"progress_update\", \"timestamp\": \"2025-06-20 16:06:46\", \"is_completed\": false, \"last_position\": 30, \"watch_duration\": 30}', '2025-06-20 16:06:46', NULL),
(78, 27, 'video_progress', 1, '{\"action\": \"progress_update\", \"timestamp\": \"2025-06-20 16:29:17\", \"is_completed\": false, \"last_position\": 30, \"watch_duration\": 30}', '2025-06-20 16:29:17', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_course_enrollments`
--

CREATE TABLE `user_course_enrollments` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `course_id` bigint(20) UNSIGNED NOT NULL,
  `enrollment_date` date DEFAULT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `status` enum('active','completed','cancelled') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `is_paid` tinyint(1) NOT NULL DEFAULT '0',
  `amount_paid` decimal(10,2) NOT NULL DEFAULT '0.00',
  `payment_method` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `transaction_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_course_enrollments`
--

INSERT INTO `user_course_enrollments` (`id`, `user_id`, `course_id`, `enrollment_date`, `start_date`, `end_date`, `status`, `is_paid`, `amount_paid`, `payment_method`, `transaction_id`, `created_at`, `updated_at`) VALUES
(19, 24, 2, '2025-05-28', '2025-05-28', '2025-11-24', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(20, 25, 2, '2025-05-28', '2025-05-28', '2025-11-24', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(22, 27, 2, '2025-06-12', '2025-06-12', '2025-12-09', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(23, 28, 2, '2025-06-13', '2025-06-13', '2025-12-10', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(24, 29, 2, '2025-06-14', '2025-06-14', '2025-12-11', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(25, 30, 2, '2025-06-14', '2025-06-14', '2025-12-11', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(26, 32, 2, '2025-06-14', '2025-06-14', '2025-12-11', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(28, 33, 2, '2025-06-14', '2025-06-14', '2025-12-11', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(29, 34, 2, '2025-06-14', '2025-06-14', '2025-12-11', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(30, 35, 2, '2025-06-14', '2025-06-14', '2025-12-11', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(31, 36, 2, '2025-06-14', '2025-06-14', '2025-12-11', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(32, 37, 2, '2025-06-14', '2025-06-14', '2025-12-11', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(33, 38, 2, '2025-06-14', '2025-06-14', '2025-12-11', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(34, 39, 2, '2025-06-14', '2025-06-14', '2025-12-11', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(35, 40, 2, '2025-06-14', '2025-06-14', '2025-12-11', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(36, 41, 2, '2025-06-14', '2025-06-14', '2025-12-11', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(41, 43, 2, '2025-06-19', '2025-06-19', '2025-07-31', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(42, 44, 2, '2025-06-19', '2025-06-19', '2025-07-31', 'active', 0, 0.00, NULL, NULL, NULL, NULL),
(44, 45, 2, '2025-06-19', '2025-06-19', '2025-07-31', 'active', 0, 0.00, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_food_items`
--

CREATE TABLE `user_food_items` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `calories` int(11) NOT NULL,
  `protein` decimal(5,2) DEFAULT NULL,
  `carbs` decimal(5,2) DEFAULT NULL,
  `fat` decimal(5,2) DEFAULT NULL,
  `serving_size` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_quote_preferences`
--

CREATE TABLE `user_quote_preferences` (
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `preferred_categories` text COLLATE utf8mb4_unicode_ci,
  `personalization_enabled` tinyint(1) NOT NULL DEFAULT '1',
  `last_quote_id` bigint(20) UNSIGNED DEFAULT NULL,
  `last_quote_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_quote_preferences`
--

INSERT INTO `user_quote_preferences` (`user_id`, `preferred_categories`, `personalization_enabled`, `last_quote_id`, `last_quote_date`, `created_at`, `updated_at`) VALUES
(24, '', 1, NULL, NULL, NULL, NULL),
(25, '', 1, NULL, NULL, NULL, NULL),
(27, '', 1, NULL, NULL, NULL, NULL),
(29, '', 1, NULL, NULL, NULL, NULL),
(30, '', 1, NULL, NULL, NULL, NULL),
(32, '', 1, NULL, NULL, NULL, NULL),
(33, '', 1, NULL, NULL, NULL, NULL),
(34, '', 1, NULL, NULL, NULL, NULL),
(35, '', 1, NULL, NULL, NULL, NULL),
(36, '', 1, NULL, NULL, NULL, NULL),
(37, '', 1, NULL, NULL, NULL, NULL),
(38, '', 1, NULL, NULL, NULL, NULL),
(39, '', 1, NULL, NULL, NULL, NULL),
(40, '', 1, NULL, NULL, NULL, NULL),
(41, '', 1, NULL, NULL, NULL, NULL),
(43, '', 1, NULL, NULL, NULL, NULL),
(44, '', 1, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_video_progress`
--

CREATE TABLE `user_video_progress` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `video_id` bigint(20) UNSIGNED NOT NULL,
  `is_unlocked` tinyint(1) NOT NULL DEFAULT '0',
  `is_completed` tinyint(1) NOT NULL DEFAULT '0',
  `watch_duration_seconds` int(11) NOT NULL DEFAULT '0',
  `last_position_seconds` int(11) NOT NULL DEFAULT '0',
  `unlock_date` date DEFAULT NULL,
  `completion_date` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `last_watched_at` timestamp NULL DEFAULT NULL,
  `progress_percentage` int(11) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_video_progress`
--

INSERT INTO `user_video_progress` (`id`, `user_id`, `video_id`, `is_unlocked`, `is_completed`, `watch_duration_seconds`, `last_position_seconds`, `unlock_date`, `completion_date`, `created_at`, `updated_at`, `last_watched_at`, `progress_percentage`) VALUES
(6, 25, 1, 1, 0, 0, 0, '2025-05-28', NULL, NULL, NULL, NULL, 0),
(8, 25, 2, 1, 0, 0, 0, '2025-06-05', NULL, NULL, NULL, NULL, 0),
(10, 27, 1, 1, 1, 30, 30, '2025-06-22', NULL, NULL, '2025-06-15 18:14:07', NULL, 0),
(11, 24, 1, 1, 0, 0, 0, '2025-06-13', NULL, NULL, NULL, NULL, 0),
(12, 24, 2, 1, 0, 0, 0, '2025-06-13', NULL, NULL, NULL, NULL, 0),
(13, 29, 1, 1, 0, 0, 0, '2025-06-14', NULL, NULL, NULL, NULL, 0),
(14, 30, 1, 1, 0, 0, 0, '2025-06-14', NULL, NULL, NULL, NULL, 0),
(15, 32, 1, 1, 0, 0, 0, '2025-06-14', NULL, NULL, NULL, NULL, 0),
(16, 33, 1, 1, 0, 0, 0, '2025-06-14', NULL, NULL, NULL, NULL, 0),
(17, 34, 1, 1, 0, 0, 0, '2025-06-14', NULL, NULL, NULL, NULL, 0),
(18, 35, 1, 1, 0, 0, 0, '2025-06-14', NULL, NULL, NULL, NULL, 0),
(19, 36, 1, 1, 0, 0, 0, '2025-06-14', NULL, NULL, NULL, NULL, 0),
(20, 37, 1, 1, 0, 0, 0, '2025-06-14', NULL, NULL, NULL, NULL, 0),
(21, 38, 1, 1, 0, 0, 0, '2025-06-14', NULL, NULL, NULL, NULL, 0),
(22, 27, 2, 1, 0, 30, 30, '2025-06-22', NULL, NULL, NULL, NULL, 0),
(23, 25, 4, 1, 0, 0, 0, '2025-06-14', NULL, NULL, NULL, NULL, 0),
(24, 40, 1, 1, 0, 0, 0, '2025-06-14', NULL, NULL, NULL, NULL, 0),
(25, 39, 1, 1, 0, 0, 0, '2025-06-14', NULL, NULL, NULL, NULL, 0),
(26, 41, 1, 1, 0, 0, 0, '2025-06-14', NULL, NULL, NULL, NULL, 0),
(30, 24, 4, 1, 0, 0, 0, '2025-06-16', NULL, NULL, NULL, NULL, 0),
(31, 25, 7, 1, 0, 0, 0, '2025-06-18', NULL, NULL, NULL, NULL, 0),
(32, 43, 1, 1, 0, 0, 0, '2025-06-19', NULL, NULL, NULL, NULL, 0),
(33, 44, 1, 1, 0, 0, 0, '2025-06-19', NULL, NULL, NULL, NULL, 0),
(35, 45, 1, 1, 0, 0, 0, '2025-06-19', NULL, NULL, NULL, NULL, 0),
(36, 37, 2, 1, 0, 0, 0, '2025-06-21', NULL, NULL, NULL, NULL, 0),
(37, 40, 2, 1, 0, 0, 0, '2025-06-21', NULL, NULL, NULL, NULL, 0),
(38, 41, 2, 1, 0, 0, 0, '2025-06-21', NULL, NULL, NULL, NULL, 0),
(39, 39, 2, 1, 0, 0, 0, '2025-06-21', NULL, NULL, NULL, NULL, 0),
(40, 35, 2, 1, 0, 0, 0, '2025-06-21', NULL, NULL, NULL, NULL, 0),
(41, 38, 2, 1, 0, 0, 0, '2025-06-21', NULL, NULL, NULL, NULL, 0),
(42, 30, 2, 1, 0, 0, 0, '2025-06-21', NULL, NULL, NULL, NULL, 0),
(43, 32, 2, 1, 0, 0, 0, '2025-06-21', NULL, NULL, NULL, NULL, 0),
(44, 36, 2, 1, 0, 0, 0, '2025-06-21', NULL, NULL, NULL, NULL, 0),
(45, 33, 2, 1, 0, 0, 0, '2025-06-21', NULL, NULL, NULL, NULL, 0),
(46, 29, 2, 1, 0, 0, 0, '2025-06-21', NULL, NULL, NULL, NULL, 0),
(47, 34, 2, 1, 0, 0, 0, '2025-06-21', NULL, NULL, NULL, NULL, 0),
(49, 27, 4, 1, 0, 0, 0, '2025-06-27', NULL, NULL, NULL, NULL, 0),
(51, 27, 7, 1, 0, 0, 0, '2025-07-03', NULL, NULL, NULL, NULL, 0),
(52, 27, 8, 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, 0),
(57, 25, 8, 1, 0, 0, 0, '2025-06-25', NULL, NULL, NULL, NULL, 0),
(58, 43, 2, 1, 0, 0, 0, '2025-06-26', NULL, NULL, NULL, NULL, 0),
(59, 44, 2, 1, 0, 0, 0, '2025-06-26', NULL, NULL, NULL, NULL, 0),
(61, 30, 4, 1, 0, 0, 0, '2025-06-28', NULL, NULL, NULL, NULL, 0),
(62, 32, 4, 1, 0, 0, 0, '2025-06-28', NULL, NULL, NULL, NULL, 0),
(63, 37, 4, 1, 0, 0, 0, '2025-06-28', NULL, NULL, NULL, NULL, 0),
(64, 34, 4, 1, 0, 0, 0, '2025-06-28', NULL, NULL, NULL, NULL, 0),
(65, 35, 4, 1, 0, 0, 0, '2025-06-28', NULL, NULL, NULL, NULL, 0),
(66, 38, 4, 1, 0, 0, 0, '2025-06-28', NULL, NULL, NULL, NULL, 0),
(67, 36, 4, 1, 0, 0, 0, '2025-06-28', NULL, NULL, NULL, NULL, 0),
(68, 41, 4, 1, 0, 0, 0, '2025-06-28', NULL, NULL, NULL, NULL, 0),
(69, 40, 4, 1, 0, 0, 0, '2025-06-28', NULL, NULL, NULL, NULL, 0),
(70, 39, 4, 1, 0, 0, 0, '2025-06-28', NULL, NULL, NULL, NULL, 0),
(71, 33, 4, 1, 0, 0, 0, '2025-06-28', NULL, NULL, NULL, NULL, 0),
(72, 29, 4, 1, 0, 0, 0, '2025-06-29', NULL, NULL, NULL, NULL, 0),
(73, 43, 4, 1, 0, 0, 0, '2025-07-03', NULL, NULL, NULL, NULL, 0),
(74, 44, 4, 1, 0, 0, 0, '2025-07-03', NULL, NULL, NULL, NULL, 0),
(76, 33, 7, 1, 0, 0, 0, '2025-07-05', NULL, NULL, NULL, NULL, 0),
(77, 39, 7, 1, 0, 0, 0, '2025-07-05', NULL, NULL, NULL, NULL, 0),
(78, 34, 7, 1, 0, 0, 0, '2025-07-05', NULL, NULL, NULL, NULL, 0),
(79, 41, 7, 1, 0, 0, 0, '2025-07-05', NULL, NULL, NULL, NULL, 0),
(80, 30, 7, 1, 0, 0, 0, '2025-07-05', NULL, NULL, NULL, NULL, 0),
(81, 38, 7, 1, 0, 0, 0, '2025-07-05', NULL, NULL, NULL, NULL, 0),
(82, 36, 7, 1, 0, 0, 0, '2025-07-05', NULL, NULL, NULL, NULL, 0);

-- --------------------------------------------------------

--
-- Table structure for table `video_access_logs`
--

CREATE TABLE `video_access_logs` (
  `id` int(11) NOT NULL,
  `vimeo_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `video_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(50) COLLATE utf8_unicode_ci NOT NULL,
  `timestamp` timestamp NULL DEFAULT NULL,
  `app_domain` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `watch_duration_seconds` int(11) DEFAULT '0',
  `last_position_seconds` int(11) DEFAULT '0',
  `is_completed` tinyint(1) DEFAULT '0',
  `device_info` json DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

--
-- Dumping data for table `video_access_logs`
--

INSERT INTO `video_access_logs` (`id`, `vimeo_id`, `video_id`, `user_id`, `action`, `timestamp`, `app_domain`, `ip_address`, `user_agent`, `created_at`, `watch_duration_seconds`, `last_position_seconds`, `is_completed`, `device_info`) VALUES
(1, NULL, 2, 27, 'progress_update', '2025-06-18 15:02:46', 'app', '*************', 'Dart/3.7 (dart:io)', '2025-06-18 15:02:46', 0, 0, 0, NULL),
(2, NULL, 2, 27, 'progress_update', '2025-06-18 15:34:34', 'app', '*************', 'Dart/3.7 (dart:io)', '2025-06-18 15:34:34', 0, 0, 0, NULL),
(3, NULL, 2, 27, 'progress_update', '2025-06-20 16:06:46', 'app', '*************', 'Dart/3.7 (dart:io)', '2025-06-20 16:06:46', 0, 0, 0, NULL),
(4, NULL, 1, 27, 'progress_update', '2025-06-20 16:29:17', 'app', '**************', 'Dart/3.7 (dart:io)', '2025-06-20 16:29:17', 0, 0, 0, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `video_analytics`
--

CREATE TABLE `video_analytics` (
  `id` int(11) NOT NULL,
  `video_id` int(11) NOT NULL,
  `total_views` int(11) DEFAULT '0',
  `total_completions` int(11) DEFAULT '0',
  `total_watch_duration` int(11) DEFAULT '0',
  `average_watch_duration` int(11) DEFAULT '0',
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `water_reminders`
--

CREATE TABLE `water_reminders` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `interval_hours` int(11) NOT NULL DEFAULT '2',
  `start_time` time NOT NULL DEFAULT '08:00:00',
  `end_time` time NOT NULL DEFAULT '22:00:00',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_verified` tinyint(1) NOT NULL DEFAULT '0',
  `verified_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `water_reminders`
--

INSERT INTO `water_reminders` (`id`, `user_id`, `interval_hours`, `start_time`, `end_time`, `is_active`, `created_at`, `updated_at`, `is_verified`, `verified_at`) VALUES
(1, 6, 2, '08:00:00', '22:00:00', 1, '2025-05-09 21:38:40', '2025-05-09 21:38:40', 0, NULL),
(2, 7, 2, '08:00:00', '22:00:00', 1, '2025-05-09 21:42:10', '2025-05-09 21:42:10', 0, NULL),
(3, 8, 2, '08:00:00', '22:00:00', 1, '2025-05-09 21:45:05', '2025-05-09 21:45:05', 0, NULL),
(4, 9, 2, '08:00:00', '22:00:00', 1, '2025-05-10 04:27:52', '2025-05-10 04:27:52', 0, NULL),
(5, 10, 2, '08:00:00', '22:00:00', 1, '2025-05-11 05:59:38', '2025-05-11 05:59:38', 0, NULL),
(6, 11, 2, '08:00:00', '22:00:00', 0, '2025-05-12 05:44:57', '2025-05-12 09:10:03', 0, NULL),
(7, 12, 2, '08:00:00', '22:00:00', 1, '2025-05-12 05:56:21', '2025-05-12 13:46:00', 0, NULL),
(8, 13, 2, '08:00:00', '22:00:00', 1, '2025-05-12 20:57:16', '2025-05-12 20:57:16', 0, NULL),
(9, 14, 2, '08:00:00', '22:00:00', 1, '2025-05-12 20:59:37', '2025-05-12 20:59:37', 0, NULL),
(10, 18, 2, '08:00:00', '22:00:00', 1, '2025-05-13 12:48:11', '2025-05-13 12:48:11', 0, NULL),
(11, 19, 2, '08:00:00', '22:00:00', 1, '2025-05-13 13:11:45', '2025-05-13 13:11:45', 0, NULL),
(12, 20, 2, '08:00:00', '22:00:00', 1, '2025-05-13 13:36:16', '2025-05-13 13:36:16', 0, NULL),
(13, 21, 2, '08:00:00', '22:00:00', 1, '2025-05-13 13:41:04', '2025-05-13 13:41:04', 0, NULL),
(14, 22, 2, '08:00:00', '22:00:00', 0, '2025-05-13 13:43:12', '2025-05-13 13:43:57', 0, NULL),
(15, 23, 2, '08:00:00', '22:00:00', 1, '2025-05-13 17:26:03', '2025-05-13 17:26:03', 0, NULL),
(16, 24, 2, '08:00:00', '22:00:00', 1, '2025-05-28 02:29:05', '2025-05-28 02:29:05', 0, NULL),
(17, 25, 2, '08:00:00', '22:00:00', 1, '2025-05-28 07:07:10', '2025-05-29 14:33:32', 0, NULL),
(18, 26, 1, '08:00:00', '22:00:00', 1, '2025-05-30 13:32:30', '2025-06-01 17:02:38', 0, NULL),
(19, 27, 2, '08:00:00', '22:00:00', 1, '2025-06-12 22:01:29', '2025-06-12 22:01:29', 0, NULL),
(20, 28, 2, '08:00:00', '22:00:00', 1, '2025-06-13 06:03:12', '2025-06-13 06:03:12', 0, NULL),
(21, 29, 2, '08:00:00', '22:00:00', 1, '2025-06-14 04:53:17', '2025-06-14 04:53:17', 0, NULL),
(22, 30, 2, '08:00:00', '22:00:00', 1, '2025-06-14 05:03:57', '2025-06-14 05:03:57', 0, NULL),
(23, 31, 2, '08:00:00', '22:00:00', 1, '2025-06-14 05:17:35', '2025-06-14 05:17:35', 0, NULL),
(24, 32, 2, '08:00:00', '22:00:00', 1, '2025-06-14 05:21:23', '2025-06-14 05:21:23', 0, NULL),
(25, 33, 2, '08:00:00', '22:00:00', 1, '2025-06-14 05:27:26', '2025-06-14 05:27:26', 0, NULL),
(26, 34, 2, '08:00:00', '22:00:00', 1, '2025-06-14 05:30:55', '2025-06-14 05:30:55', 0, NULL),
(27, 35, 2, '08:00:00', '22:00:00', 1, '2025-06-14 05:34:10', '2025-06-14 05:34:10', 0, NULL),
(28, 36, 2, '08:00:00', '22:00:00', 1, '2025-06-14 05:36:44', '2025-06-14 05:36:44', 0, NULL),
(29, 37, 2, '08:00:00', '22:00:00', 1, '2025-06-14 05:48:39', '2025-06-14 05:48:39', 0, NULL),
(30, 38, 2, '08:00:00', '22:00:00', 1, '2025-06-14 06:11:54', '2025-06-14 06:11:54', 0, NULL),
(31, 39, 2, '08:00:00', '22:00:00', 1, '2025-06-14 15:24:07', '2025-06-14 15:24:07', 0, NULL),
(32, 40, 2, '08:00:00', '22:00:00', 1, '2025-06-14 15:37:21', '2025-06-14 15:37:21', 0, NULL),
(33, 41, 2, '08:00:00', '22:00:00', 1, '2025-06-14 15:48:40', '2025-06-14 15:48:40', 0, NULL),
(34, 42, 2, '08:00:00', '22:00:00', 1, '2025-06-16 16:02:08', '2025-06-16 16:02:08', 0, NULL),
(35, 43, 2, '08:00:00', '22:00:00', 1, '2025-06-19 05:08:12', '2025-06-19 05:08:12', 0, NULL),
(36, 44, 2, '08:00:00', '22:00:00', 1, '2025-06-19 07:44:57', '2025-06-19 07:44:57', 0, NULL),
(37, 45, 2, '08:00:00', '22:00:00', 1, '2025-06-19 09:29:29', '2025-06-19 09:29:29', 0, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `workout_history`
--

CREATE TABLE `workout_history` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `duration_minutes` int(11) NOT NULL DEFAULT '0',
  `calories_burned` int(11) DEFAULT NULL,
  `date` datetime NOT NULL,
  `workout_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `intensity` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `workout_programs`
--

CREATE TABLE `workout_programs` (
  `id` int(11) NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `difficulty` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `duration_weeks` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `workout_records`
--

CREATE TABLE `workout_records` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `workout_id` int(11) DEFAULT NULL,
  `duration` int(11) NOT NULL COMMENT 'in minutes',
  `calories_burned` int(11) DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `recorded_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `duration_minutes` int(11) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `workout_records`
--

INSERT INTO `workout_records` (`id`, `user_id`, `workout_id`, `duration`, `calories_burned`, `notes`, `recorded_at`, `duration_minutes`) VALUES
(1, 1, 1, 30, 250, 'Morning cardio session', '2025-05-08 18:50:18', 0),
(2, 2, 2, 45, 350, 'Evening strength training', '2025-05-07 18:50:18', 0),
(3, 3, 3, 60, 500, 'Full body workout', '2025-05-06 18:50:18', 0),
(4, 4, 4, 20, 180, 'Quick HIIT session', '2025-05-05 18:50:18', 0),
(5, 5, 5, 90, 700, 'Long distance running', '2025-05-04 18:50:18', 0),
(6, 1, 6, 40, 300, 'Yoga and stretching', '2025-05-03 18:50:18', 0),
(7, 2, 7, 50, 400, 'Weight lifting', '2025-05-02 18:50:18', 0),
(8, 3, 1, 35, 280, 'Morning run', '2025-05-09 18:50:18', 0),
(16, 15, NULL, 30, NULL, NULL, '2025-05-13 06:25:34', 0),
(17, 16, NULL, 45, NULL, NULL, '2025-05-13 06:27:33', 0),
(18, 17, NULL, 60, NULL, NULL, '2025-05-13 06:27:37', 0);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_action_logs`
--
ALTER TABLE `admin_action_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_admin_user` (`admin_user_id`),
  ADD KEY `idx_target_user` (`target_user_id`),
  ADD KEY `idx_action_type` (`action_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `admin_permissions`
--
ALTER TABLE `admin_permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`);

--
-- Indexes for table `admin_users`
--
ALTER TABLE `admin_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `admin_users_username_unique` (`username`),
  ADD UNIQUE KEY `admin_users_email_unique` (`email`),
  ADD KEY `parent_admin_id` (`parent_admin_id`),
  ADD KEY `parent_admin_id_2` (`parent_admin_id`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_role` (`role`),
  ADD KEY `idx_username` (`username`);

--
-- Indexes for table `admin_user_permissions`
--
ALTER TABLE `admin_user_permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `admin_user_permission` (`admin_user_id`,`permission_id`),
  ADD KEY `fk_admin_user_permissions_permission_id` (`permission_id`);

--
-- Indexes for table `api_tokens`
--
ALTER TABLE `api_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `api_tokens_token_unique` (`token`),
  ADD KEY `api_tokens_user_id_foreign` (`user_id`),
  ADD KEY `idx_device_id_api_tokens` (`device_id`),
  ADD KEY `idx_is_revoked_api_tokens` (`is_revoked`),
  ADD KEY `idx_last_used_api_tokens` (`last_used`),
  ADD KEY `idx_api_tokens_user_device` (`user_id`,`device_id`,`is_revoked`);

--
-- Indexes for table `audit_logs`
--
ALTER TABLE `audit_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `staff_id` (`staff_id`),
  ADD KEY `affected_user_id` (`affected_user_id`),
  ADD KEY `idx_audit_logs_action_type` (`action_type`),
  ADD KEY `idx_audit_logs_timestamp` (`timestamp`);

--
-- Indexes for table `bmi_records`
--
ALTER TABLE `bmi_records`
  ADD PRIMARY KEY (`id`),
  ADD KEY `bmi_records_user_id_foreign` (`user_id`);

--
-- Indexes for table `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `calorie_goals`
--
ALTER TABLE `calorie_goals`
  ADD PRIMARY KEY (`id`),
  ADD KEY `calorie_goals_user_id_foreign` (`user_id`);

--
-- Indexes for table `calorie_logs`
--
ALTER TABLE `calorie_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `calorie_logs_user_id_foreign` (`user_id`),
  ADD KEY `calorie_logs_log_date_index` (`log_date`);

--
-- Indexes for table `chats`
--
ALTER TABLE `chats`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `admin_id` (`admin_id`);

--
-- Indexes for table `courses`
--
ALTER TABLE `courses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `course_purchases`
--
ALTER TABLE `course_purchases`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `course_id` (`course_id`);

--
-- Indexes for table `course_videos`
--
ALTER TABLE `course_videos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `course_videos_course_id_foreign` (`course_id`);

--
-- Indexes for table `device_sessions`
--
ALTER TABLE `device_sessions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_device` (`user_id`,`device_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_device_id` (`device_id`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_last_activity` (`last_activity`),
  ADD KEY `idx_device_sessions_user_active` (`user_id`,`is_active`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `food_items`
--
ALTER TABLE `food_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `food_items_name_index` (`name`),
  ADD KEY `food_items_category_index` (`category`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indexes for table `job_batches`
--
ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `messages`
--
ALTER TABLE `messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `chat_id` (`chat_id`),
  ADD KEY `sender_id` (`sender_id`),
  ADD KEY `receiver_id` (`receiver_id`),
  ADD KEY `status` (`status`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `motivational_quotes`
--
ALTER TABLE `motivational_quotes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `motivational_quotes_category_index` (`category`),
  ADD KEY `motivational_quotes_is_active_index` (`is_active`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `type` (`type`),
  ADD KEY `is_read` (`is_read`),
  ADD KEY `recipient_id` (`recipient_id`),
  ADD KEY `recipient_type` (`recipient_type`);

--
-- Indexes for table `notification_settings`
--
ALTER TABLE `notification_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `admin_id` (`admin_id`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `pending_users`
--
ALTER TABLE `pending_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `pending_users_phone_number_unique` (`phone_number`);

--
-- Indexes for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `pin_usage_history`
--
ALTER TABLE `pin_usage_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_pin` (`user_id`,`pin`);

--
-- Indexes for table `program_enrollments`
--
ALTER TABLE `program_enrollments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_program_id` (`program_id`);

--
-- Indexes for table `quote_settings`
--
ALTER TABLE `quote_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `quote_settings_setting_key_unique` (`setting_key`);

--
-- Indexes for table `refresh_tokens`
--
ALTER TABLE `refresh_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `token` (`token`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_token` (`token`),
  ADD KEY `idx_device_id` (`device_id`),
  ADD KEY `idx_expires_at` (`expires_at`);

--
-- Indexes for table `security_alerts`
--
ALTER TABLE `security_alerts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_alert_type` (`alert_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `settings_key_unique` (`key`),
  ADD KEY `settings_group_index` (`group`);

--
-- Indexes for table `streak_days`
--
ALTER TABLE `streak_days`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `streak_days_user_id_date_unique` (`user_id`,`date`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_username_unique` (`username`),
  ADD UNIQUE KEY `users_phone_number_unique` (`phone_number`),
  ADD UNIQUE KEY `users_email_unique` (`email`),
  ADD KEY `assigned_staff_id` (`assigned_staff_id`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_phone_number` (`phone_number`),
  ADD KEY `idx_assigned_staff_id` (`assigned_staff_id`),
  ADD KEY `idx_device_id` (`device_id`),
  ADD KEY `idx_users_device_id_active` (`device_id`,`is_active`);

--
-- Indexes for table `user_activity_log`
--
ALTER TABLE `user_activity_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_activity_log_user_id_foreign` (`user_id`),
  ADD KEY `idx_user_activity_log_user_type_related` (`user_id`,`activity_type`,`related_id`);

--
-- Indexes for table `user_course_enrollments`
--
ALTER TABLE `user_course_enrollments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_course_enrollments_user_id_course_id_unique` (`user_id`,`course_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_course_id` (`course_id`);

--
-- Indexes for table `user_food_items`
--
ALTER TABLE `user_food_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_food_items_user_id_foreign` (`user_id`);

--
-- Indexes for table `user_quote_preferences`
--
ALTER TABLE `user_quote_preferences`
  ADD PRIMARY KEY (`user_id`);

--
-- Indexes for table `user_video_progress`
--
ALTER TABLE `user_video_progress`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_video_progress_user_id_video_id_unique` (`user_id`,`video_id`),
  ADD KEY `user_video_progress_video_id_foreign` (`video_id`),
  ADD KEY `idx_user_video_progress_user_completed` (`user_id`,`is_completed`),
  ADD KEY `idx_user_video_progress_video_completed` (`video_id`,`is_completed`);

--
-- Indexes for table `video_access_logs`
--
ALTER TABLE `video_access_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_video_user` (`video_id`,`user_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `video_analytics`
--
ALTER TABLE `video_analytics`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `video_id` (`video_id`),
  ADD KEY `idx_video_id` (`video_id`);

--
-- Indexes for table `water_reminders`
--
ALTER TABLE `water_reminders`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `workout_history`
--
ALTER TABLE `workout_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `workout_history_user_id_foreign` (`user_id`);

--
-- Indexes for table `workout_programs`
--
ALTER TABLE `workout_programs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `workout_records`
--
ALTER TABLE `workout_records`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `workout_id` (`workout_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_action_logs`
--
ALTER TABLE `admin_action_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `admin_permissions`
--
ALTER TABLE `admin_permissions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `admin_users`
--
ALTER TABLE `admin_users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `admin_user_permissions`
--
ALTER TABLE `admin_user_permissions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=76;

--
-- AUTO_INCREMENT for table `api_tokens`
--
ALTER TABLE `api_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=401;

--
-- AUTO_INCREMENT for table `audit_logs`
--
ALTER TABLE `audit_logs`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT for table `bmi_records`
--
ALTER TABLE `bmi_records`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=410;

--
-- AUTO_INCREMENT for table `calorie_goals`
--
ALTER TABLE `calorie_goals`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `calorie_logs`
--
ALTER TABLE `calorie_logs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `courses`
--
ALTER TABLE `courses`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `course_purchases`
--
ALTER TABLE `course_purchases`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `course_videos`
--
ALTER TABLE `course_videos`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `device_sessions`
--
ALTER TABLE `device_sessions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `food_items`
--
ALTER TABLE `food_items`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `messages`
--
ALTER TABLE `messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=165;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `motivational_quotes`
--
ALTER TABLE `motivational_quotes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `notification_settings`
--
ALTER TABLE `notification_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `pending_users`
--
ALTER TABLE `pending_users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pin_usage_history`
--
ALTER TABLE `pin_usage_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `program_enrollments`
--
ALTER TABLE `program_enrollments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `quote_settings`
--
ALTER TABLE `quote_settings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `refresh_tokens`
--
ALTER TABLE `refresh_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `security_alerts`
--
ALTER TABLE `security_alerts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=67;

--
-- AUTO_INCREMENT for table `streak_days`
--
ALTER TABLE `streak_days`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=46;

--
-- AUTO_INCREMENT for table `user_activity_log`
--
ALTER TABLE `user_activity_log`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=79;

--
-- AUTO_INCREMENT for table `user_course_enrollments`
--
ALTER TABLE `user_course_enrollments`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=45;

--
-- AUTO_INCREMENT for table `user_food_items`
--
ALTER TABLE `user_food_items`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_video_progress`
--
ALTER TABLE `user_video_progress`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=83;

--
-- AUTO_INCREMENT for table `video_access_logs`
--
ALTER TABLE `video_access_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `video_analytics`
--
ALTER TABLE `video_analytics`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `water_reminders`
--
ALTER TABLE `water_reminders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=38;

--
-- AUTO_INCREMENT for table `workout_history`
--
ALTER TABLE `workout_history`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `workout_programs`
--
ALTER TABLE `workout_programs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `workout_records`
--
ALTER TABLE `workout_records`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `admin_users`
--
ALTER TABLE `admin_users`
  ADD CONSTRAINT `fk_parent_admin` FOREIGN KEY (`parent_admin_id`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `admin_user_permissions`
--
ALTER TABLE `admin_user_permissions`
  ADD CONSTRAINT `fk_admin_user_permissions_admin_user_id` FOREIGN KEY (`admin_user_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_admin_user_permissions_permission_id` FOREIGN KEY (`permission_id`) REFERENCES `admin_permissions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `api_tokens`
--
ALTER TABLE `api_tokens`
  ADD CONSTRAINT `api_tokens_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `audit_logs`
--
ALTER TABLE `audit_logs`
  ADD CONSTRAINT `audit_logs_ibfk_1` FOREIGN KEY (`staff_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `audit_logs_ibfk_2` FOREIGN KEY (`affected_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `bmi_records`
--
ALTER TABLE `bmi_records`
  ADD CONSTRAINT `bmi_records_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `calorie_goals`
--
ALTER TABLE `calorie_goals`
  ADD CONSTRAINT `calorie_goals_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `calorie_logs`
--
ALTER TABLE `calorie_logs`
  ADD CONSTRAINT `calorie_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `chats`
--
ALTER TABLE `chats`
  ADD CONSTRAINT `chats_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `course_purchases`
--
ALTER TABLE `course_purchases`
  ADD CONSTRAINT `course_purchases_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `course_purchases_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`);

--
-- Constraints for table `course_videos`
--
ALTER TABLE `course_videos`
  ADD CONSTRAINT `course_videos_course_id_foreign` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `messages`
--
ALTER TABLE `messages`
  ADD CONSTRAINT `messages_ibfk_1` FOREIGN KEY (`chat_id`) REFERENCES `chats` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `refresh_tokens`
--
ALTER TABLE `refresh_tokens`
  ADD CONSTRAINT `refresh_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `streak_days`
--
ALTER TABLE `streak_days`
  ADD CONSTRAINT `streak_days_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `fk_assigned_staff` FOREIGN KEY (`assigned_staff_id`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `user_activity_log`
--
ALTER TABLE `user_activity_log`
  ADD CONSTRAINT `user_activity_log_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_course_enrollments`
--
ALTER TABLE `user_course_enrollments`
  ADD CONSTRAINT `user_course_enrollments_course_id_foreign` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_course_enrollments_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_food_items`
--
ALTER TABLE `user_food_items`
  ADD CONSTRAINT `user_food_items_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_quote_preferences`
--
ALTER TABLE `user_quote_preferences`
  ADD CONSTRAINT `user_quote_preferences_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_video_progress`
--
ALTER TABLE `user_video_progress`
  ADD CONSTRAINT `user_video_progress_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_video_progress_video_id_foreign` FOREIGN KEY (`video_id`) REFERENCES `course_videos` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `workout_history`
--
ALTER TABLE `workout_history`
  ADD CONSTRAINT `workout_history_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
