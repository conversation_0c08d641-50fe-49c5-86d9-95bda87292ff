#!/bin/bash

# KFT Fitness Flutter App Script
# Starts the Flutter development app with HTTP support

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting KFT Fitness Flutter App${NC}"
echo "=================================================="

# Configuration
LOCAL_IP="***************"
WEB_PORT="8080"
FLUTTER_WEB_PORT="8080"

echo -e "${YELLOW}App Configuration:${NC}"
echo "  Local IP: $LOCAL_IP"
echo "  Web Port: $WEB_PORT"
echo "  HTTP Support: Enabled"
echo "  API Endpoint: http://localhost:9001/admin/api/"
echo "=================================================="
echo

# Check Flutter installation
echo -e "${BLUE}Checking Flutter installation...${NC}"
FLUTTER_PATH=$(which flutter)
if [ -z "$FLUTTER_PATH" ]; then
  echo -e "${YELLOW}Flutter not found in PATH. Trying direct path...${NC}"
  FLUTTER_PATH="/Users/<USER>/flutter/bin/flutter"
fi

if [ ! -f "$FLUTTER_PATH" ]; then
    echo -e "${RED}❌ Flutter not found${NC}"
    echo "Please install Flutter first:"
    echo "  - Visit: https://flutter.dev/docs/get-started/install"
    exit 1
fi

FLUTTER_VERSION=$($FLUTTER_PATH --version | head -n 1)
echo -e "${GREEN}✅ Flutter found: $FLUTTER_VERSION${NC}"

# Check if pubspec.yaml exists
if [ ! -f "pubspec.yaml" ]; then
    echo -e "${RED}❌ pubspec.yaml not found${NC}"
    echo "Please ensure you're running this script from the Flutter project root directory"
    exit 1
fi

echo -e "${GREEN}✅ Flutter project found${NC}"

# Get dependencies
echo -e "${BLUE}Getting Flutter dependencies...${NC}"
$FLUTTER_PATH pub get

# Check available devices
echo -e "${BLUE}Checking available devices...${NC}"
$FLUTTER_PATH devices

echo
echo -e "${BLUE}Starting Flutter app...${NC}"
echo -e "${YELLOW}The app will be available at:${NC}"
echo "  - Local: http://localhost:$FLUTTER_WEB_PORT"
echo "  - Network: http://$LOCAL_IP:$FLUTTER_WEB_PORT"
echo
echo -e "${YELLOW}Press Ctrl+C to stop the app${NC}"
echo

# Run the app with HTTP support
$FLUTTER_PATH run -d chrome --web-hostname $LOCAL_IP --web-port $FLUTTER_WEB_PORT

echo
echo -e "${YELLOW}If the app doesn't start, try these alternatives:${NC}"
echo "  flutter run -d chrome"
echo "  flutter run -d web-server --web-port $FLUTTER_WEB_PORT"
echo "  flutter run --web-renderer html -d chrome"
