<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KFT Fitness Splash Screen Generator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .canvas-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        .canvas-item {
            text-align: center;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background: #3D5AFE;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0031CA;
        }
        .download-link {
            display: inline-block;
            margin: 5px;
            padding: 8px 16px;
            background: #00BFA5;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 14px;
        }
        .download-link:hover {
            background: #008E76;
        }
        h1 {
            color: #3D5AFE;
            text-align: center;
        }
        .instructions {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏋️ KFT Fitness Splash Screen Generator</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>Click "Generate Splash Screens" to create all required sizes</li>
                <li>Download each image using the download links</li>
                <li>Replace the existing splash screen images in <code>web/splash/img/</code></li>
                <li>The images will match your app's design system colors</li>
            </ol>
        </div>

        <button onclick="generateAllSplashScreens()">Generate Splash Screens</button>
        <button onclick="clearCanvases()">Clear All</button>

        <div class="canvas-container" id="canvasContainer">
            <!-- Canvases will be generated here -->
        </div>

        <div id="downloadLinks" style="margin-top: 20px;">
            <!-- Download links will appear here -->
        </div>
    </div>

    <script>
        // KFT Design System Colors
        const colors = {
            primary: '#3D5AFE',
            primaryDark: '#0031CA',
            primaryLight: '#8187FF',
            secondary: '#00BFA5',
            background: '#121212',
            backgroundLight: '#F5F5F7',
            text: '#FFFFFF',
            textDark: '#212121'
        };

        const splashSizes = [
            { name: 'light-1x', width: 320, height: 568, theme: 'light' },
            { name: 'light-2x', width: 640, height: 1136, theme: 'light' },
            { name: 'light-3x', width: 960, height: 1704, theme: 'light' },
            { name: 'light-4x', width: 1280, height: 2272, theme: 'light' },
            { name: 'dark-1x', width: 320, height: 568, theme: 'dark' },
            { name: 'dark-2x', width: 640, height: 1136, theme: 'dark' },
            { name: 'dark-3x', width: 960, height: 1704, theme: 'dark' },
            { name: 'dark-4x', width: 1280, height: 2272, theme: 'dark' }
        ];

        function generateAllSplashScreens() {
            const container = document.getElementById('canvasContainer');
            const downloadContainer = document.getElementById('downloadLinks');
            
            container.innerHTML = '';
            downloadContainer.innerHTML = '<h3>Download Links:</h3>';

            splashSizes.forEach(size => {
                generateSplashScreen(size, container, downloadContainer);
            });
        }

        function generateSplashScreen(size, container, downloadContainer) {
            // Create canvas element
            const canvasItem = document.createElement('div');
            canvasItem.className = 'canvas-item';
            
            const canvas = document.createElement('canvas');
            canvas.width = size.width;
            canvas.height = size.height;
            canvas.style.maxWidth = '200px';
            canvas.style.height = 'auto';
            
            const ctx = canvas.getContext('2d');
            
            // Set background
            const bgColor = size.theme === 'dark' ? colors.background : colors.backgroundLight;
            ctx.fillStyle = bgColor;
            ctx.fillRect(0, 0, size.width, size.height);
            
            // Calculate responsive sizes
            const scale = size.width / 320; // Base scale on 1x size
            const logoSize = 80 * scale;
            const fontSize = 24 * scale;
            const smallFontSize = 16 * scale;
            
            // Draw gradient background circle
            const centerX = size.width / 2;
            const centerY = size.height / 2;
            const gradientRadius = logoSize * 1.5;
            
            const gradient = ctx.createRadialGradient(
                centerX, centerY, 0,
                centerX, centerY, gradientRadius
            );
            gradient.addColorStop(0, colors.primary + '40');
            gradient.addColorStop(1, colors.primary + '10');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(centerX, centerY, gradientRadius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw main logo circle
            ctx.fillStyle = colors.primary;
            ctx.beginPath();
            ctx.arc(centerX, centerY, logoSize / 2, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw fitness icon (dumbbell)
            ctx.fillStyle = colors.text;
            ctx.font = `${logoSize * 0.5}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('🏋️', centerX, centerY);
            
            // Draw app name
            const textColor = size.theme === 'dark' ? colors.text : colors.textDark;
            ctx.fillStyle = textColor;
            ctx.font = `bold ${fontSize}px -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('KFT FITNESS', centerX, centerY + logoSize);
            
            // Draw tagline
            ctx.fillStyle = size.theme === 'dark' ? colors.text + '80' : colors.textDark + '80';
            ctx.font = `${smallFontSize}px -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif`;
            ctx.fillText('Your Personal Trainer', centerX, centerY + logoSize + fontSize + 10 * scale);
            
            // Add loading indicator
            const loadingY = size.height - 100 * scale;
            const loadingRadius = 3 * scale;
            const loadingSpacing = 15 * scale;
            
            ctx.fillStyle = colors.primary;
            for (let i = 0; i < 3; i++) {
                const x = centerX - loadingSpacing + (i * loadingSpacing);
                ctx.beginPath();
                ctx.arc(x, loadingY, loadingRadius, 0, 2 * Math.PI);
                ctx.fill();
            }
            
            // Add to container
            const label = document.createElement('div');
            label.textContent = `${size.name}.png (${size.width}x${size.height})`;
            label.style.marginTop = '10px';
            label.style.fontSize = '14px';
            label.style.color = '#666';
            
            canvasItem.appendChild(canvas);
            canvasItem.appendChild(label);
            container.appendChild(canvasItem);
            
            // Create download link
            canvas.toBlob(blob => {
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `${size.name}.png`;
                link.className = 'download-link';
                link.textContent = `Download ${size.name}.png`;
                downloadContainer.appendChild(link);
            });
        }

        function clearCanvases() {
            document.getElementById('canvasContainer').innerHTML = '';
            document.getElementById('downloadLinks').innerHTML = '';
        }

        // Generate splash screens on page load
        window.addEventListener('load', () => {
            setTimeout(generateAllSplashScreens, 500);
        });
    </script>
</body>
</html>
