import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../lib/providers/water_reminder_provider.dart';
import '../lib/services/water_goal_service.dart';
import '../lib/widgets/water_goal_settings_widget.dart';
import '../lib/widgets/water_goal_quick_edit_widget.dart';

/// Example demonstrating the comprehensive water goal management system
class WaterGoalExample extends StatelessWidget {
  const WaterGoalExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => WaterReminderProvider(),
      child: MaterialApp(
        title: 'Water Goal Management Example',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          useMaterial3: true,
        ),
        home: const WaterGoalExamplePage(),
      ),
    );
  }
}

class WaterGoalExamplePage extends StatefulWidget {
  const WaterGoalExamplePage({Key? key}) : super(key: key);

  @override
  State<WaterGoalExamplePage> createState() => _WaterGoalExamplePageState();
}

class _WaterGoalExamplePageState extends State<WaterGoalExamplePage> {
  @override
  void initState() {
    super.initState();
    // Initialize the provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<WaterReminderProvider>(context, listen: false).loadGoalSettings();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Water Goal Management'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Consumer<WaterReminderProvider>(
        builder: (context, provider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCurrentGoalCard(context, provider),
                const SizedBox(height: 20),
                _buildQuickEditExample(context),
                const SizedBox(height: 20),
                _buildActionButtons(context),
                const SizedBox(height: 20),
                _buildGoalPresetsExample(context),
                const SizedBox(height: 20),
                _buildUnitConversionExample(context),
                const SizedBox(height: 20),
                _buildUsageInstructions(context),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCurrentGoalCard(BuildContext context, WaterReminderProvider provider) {
    final theme = Theme.of(context);
    final goalSettings = provider.goalSettings;
    
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.water_drop,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Current Water Goal',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (goalSettings != null) ...[
              _buildGoalInfoRow('Goal Amount:', goalSettings.displayText),
              _buildGoalInfoRow('Preferred Unit:', goalSettings.preferredUnit.fullName),
              _buildGoalInfoRow('In Milliliters:', '${goalSettings.goalInMl}ml'),
              _buildGoalInfoRow('In Liters:', '${WaterGoalService.mlToLiters(goalSettings.goalInMl)}L'),
              _buildGoalInfoRow('Is Preset Goal:', goalSettings.isPresetGoal ? 'Yes' : 'No'),
              _buildGoalInfoRow('Last Updated:', _formatDateTime(goalSettings.lastUpdated)),
            ] else ...[
              const Text('Loading goal settings...'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildGoalInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickEditExample(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Goal Edit Widget',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'This widget provides quick goal adjustments and can be embedded anywhere:',
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: theme.colorScheme.outline),
              ),
              child: const WaterGoalQuickEditWidget(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Goal Management Actions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: () => WaterGoalSettingsWidget.showAsBottomSheet(context),
                  icon: const Icon(Icons.settings),
                  label: const Text('Full Settings'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _showQuickGoalDialog(context),
                  icon: const Icon(Icons.speed),
                  label: const Text('Quick Set Goal'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _demonstrateUnitSwitch(context),
                  icon: const Icon(Icons.swap_horiz),
                  label: const Text('Switch Units'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _resetToDefaults(context),
                  icon: const Icon(Icons.restore),
                  label: const Text('Reset to Defaults'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalPresetsExample(BuildContext context) {
    final theme = Theme.of(context);
    final presets = WaterGoalService.getGoalPresets();
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Goal Presets',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const Text('Common water intake goals:'),
            const SizedBox(height: 12),
            Consumer<WaterReminderProvider>(
              builder: (context, provider, child) {
                final currentGoal = provider.goalSettings?.goalInMl ?? 2000;
                final preferredUnit = provider.goalSettings?.preferredUnit ?? WaterUnit.liters;
                
                return Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: presets.map((preset) {
                    final isSelected = preset == currentGoal;
                    final displayText = WaterGoalService.formatGoalDisplay(preset, preferredUnit);
                    
                    return FilterChip(
                      label: Text(displayText),
                      selected: isSelected,
                      onSelected: (selected) {
                        if (selected) {
                          provider.updateDailyGoal(preset);
                        }
                      },
                    );
                  }).toList(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUnitConversionExample(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Unit Conversion Examples',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildConversionRow('1000ml', '${WaterGoalService.mlToLiters(1000)}L'),
            _buildConversionRow('1500ml', '${WaterGoalService.mlToLiters(1500)}L'),
            _buildConversionRow('2000ml', '${WaterGoalService.mlToLiters(2000)}L'),
            _buildConversionRow('2.5L', '${WaterGoalService.litersToMl(2.5)}ml'),
            _buildConversionRow('3L', '${WaterGoalService.litersToMl(3.0)}ml'),
          ],
        ),
      ),
    );
  }

  Widget _buildConversionRow(String from, String to) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(width: 80, child: Text(from)),
          const Icon(Icons.arrow_forward, size: 16),
          const SizedBox(width: 8),
          Text(to),
        ],
      ),
    );
  }

  Widget _buildUsageInstructions(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Usage Instructions',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              '1. Use WaterGoalSettingsWidget for comprehensive goal editing\n'
              '2. Use WaterGoalQuickEditWidget for inline goal adjustments\n'
              '3. Goals are automatically saved to local storage\n'
              '4. Unit preferences are persistent across app sessions\n'
              '5. Integration with existing notification system\n'
              '6. Real-time updates across all UI components',
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showQuickGoalDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quick Set Goal'),
        content: const WaterGoalSettingsWidget(showAsBottomSheet: false),
      ),
    );
  }

  void _demonstrateUnitSwitch(BuildContext context) async {
    final provider = Provider.of<WaterReminderProvider>(context, listen: false);
    final currentUnit = provider.goalSettings?.preferredUnit ?? WaterUnit.liters;
    final newUnit = currentUnit == WaterUnit.liters 
        ? WaterUnit.milliliters 
        : WaterUnit.liters;
    
    await provider.updatePreferredUnit(newUnit);
    
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Switched to ${newUnit.fullName}'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _resetToDefaults(BuildContext context) async {
    final provider = Provider.of<WaterReminderProvider>(context, listen: false);
    await provider.updateDailyGoal(2000);
    await provider.updatePreferredUnit(WaterUnit.liters);
    
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Reset to default settings (2L)'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }
}
