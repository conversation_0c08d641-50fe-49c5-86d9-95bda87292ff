_flutterfire_internals
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.56/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.56/lib/
android_intent_plus
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/android_intent_plus-4.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/android_intent_plus-4.0.3/lib/
app_settings
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_settings-5.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_settings-5.2.0/lib/
archive
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/
args
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/lib/
asn1lib
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/
async
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/
awesome_notifications
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/
boolean_selector
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/lib/
cached_network_image
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/
cached_network_image_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib/
cached_network_image_web
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/lib/
characters
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/
checked_yaml
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/lib/
chewie
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/chewie-1.11.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/chewie-1.11.3/lib/
cli_util
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/lib/
clock
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/
collection
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/
connectivity_plus
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib/
connectivity_plus_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/
convert
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/lib/
cross_file
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/
crypto
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/
csslib
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/
cupertino_icons
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
dbus
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/
device_info_plus
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/lib/
device_info_plus_platform_interface
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/lib/
encrypt
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/
equatable
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/
fake_async
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/lib/
ffi
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/
file
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/
file_selector_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/
file_selector_macos
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/
file_selector_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/
file_selector_windows
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/
firebase_analytics
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.0/lib/
firebase_analytics_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-4.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-4.4.0/lib/
firebase_analytics_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+13/lib/
firebase_core
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.14.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.14.0/lib/
firebase_core_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/
firebase_core_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/lib/
fixnum
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/
fl_chart
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/
flutter_advanced_segment
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_advanced_segment-3.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_advanced_segment-3.1.0/lib/
flutter_cache_manager
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/
flutter_inappwebview
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/
flutter_inappwebview_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/lib/
flutter_inappwebview_internal_annotations
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0/lib/
flutter_inappwebview_ios
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/lib/
flutter_inappwebview_macos
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2/lib/
flutter_inappwebview_platform_interface
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/
flutter_inappwebview_web
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/
flutter_inappwebview_windows
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_windows-0.6.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_windows-0.6.0/lib/
flutter_launcher_icons
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/lib/
flutter_lints
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/lib/
flutter_plugin_android_lifecycle
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/lib/
flutter_riverpod
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/
flutter_secure_storage
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/
flutter_secure_storage_linux
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/lib/
flutter_secure_storage_macos
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/lib/
flutter_secure_storage_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/
flutter_secure_storage_web
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/lib/
flutter_secure_storage_windows
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/
flutter_slidable
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_slidable-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_slidable-3.1.2/lib/
font_awesome_flutter
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/
google_fonts
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/
html
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/
http
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/
http_parser
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/
image
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/
image_picker
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/lib/
image_picker_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/lib/
image_picker_for_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib/
image_picker_ios
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/
image_picker_linux
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/
image_picker_macos
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/
image_picker_platform_interface
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/
image_picker_windows
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/
intl
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/
js
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/lib/
json_annotation
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/
leak_tracker
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/lib/
leak_tracker_flutter_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/lib/
matcher
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
meta
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/
mime
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/
nested
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/lib/
nm
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/lib/
octo_image
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/
package_info_plus
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/
package_info_plus_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/
path
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/
path_provider
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/
path_provider_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/
path_provider_foundation
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/
path_provider_linux
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
permission_handler
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.0+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.0+1/lib/
permission_handler_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/lib/
permission_handler_apple
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/lib/
permission_handler_html
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib/
permission_handler_platform_interface
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/
permission_handler_windows
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib/
petitparser
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/
platform
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
pointycastle
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/
posix
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.2/lib/
provider
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/
riverpod
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/
rxdart
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/
screen_protector
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/screen_protector-1.4.2+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/screen_protector-1.4.2+1/lib/
share_plus
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/lib/
share_plus_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-3.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-3.4.0/lib/
shared_preferences
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/
shared_preferences_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/
shared_preferences_foundation
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
source_span
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/
sprintf
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/
sqflite
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/
sqflite_android
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/lib/
sqflite_common
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/
sqflite_darwin
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/lib/
sqflite_platform_interface
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/
stack_trace
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/
state_notifier
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/lib/
stream_channel
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/
string_scanner
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/
synchronized
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/lib/
term_glyph
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/
test_api
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/lib/
timezone
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/lib/
typed_data
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/
url_launcher
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/
url_launcher_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/
url_launcher_ios
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/
url_launcher_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/
url_launcher_macos
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/
url_launcher_platform_interface
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/
url_launcher_web
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/lib/
url_launcher_windows
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/
uuid
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/
vector_math
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/
video_player
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.10.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.10.0/lib/
video_player_android
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.7/lib/
video_player_avfoundation
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/lib/
video_player_platform_interface
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.3.0/lib/
video_player_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/lib/
vimeo_video_player
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vimeo_video_player-1.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vimeo_video_player-1.0.1/lib/
vm_service
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/lib/
wakelock_plus
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/lib/
wakelock_plus_platform_interface
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.3/lib/
web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/
webview_flutter
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0/lib/
webview_flutter_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.7.0/lib/
webview_flutter_platform_interface
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/
webview_flutter_wkwebview
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/lib/
win32
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/
win32_registry
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/lib/
xdg_directories
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/
xml
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/
yaml
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/lib/
kft
3.7
file:///Users/<USER>/Desktop/66/
file:///Users/<USER>/Desktop/66/lib/
sky_engine
3.7
file:///Users/<USER>/flutter/bin/cache/pkg/sky_engine/
file:///Users/<USER>/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///Users/<USER>/flutter/packages/flutter/
file:///Users/<USER>/flutter/packages/flutter/lib/
flutter_test
3.7
file:///Users/<USER>/flutter/packages/flutter_test/
file:///Users/<USER>/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.7
file:///Users/<USER>/flutter/packages/flutter_web_plugins/
file:///Users/<USER>/flutter/packages/flutter_web_plugins/lib/
2
