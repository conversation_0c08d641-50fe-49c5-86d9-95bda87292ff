# 🚀 KFT Fitness HTTP Setup Guide

## ✅ **HTTPS Requirements Removed - HTTP is Now Sufficient!**

This guide will help you set up the KFT Fitness app with HTTP support, run the PHP server dashboard, and configure the database.

---

## 📋 **Prerequisites**

### **Required Software:**
- ✅ **PHP 7.4+** (for server and admin panel)
- ✅ **MySQL 5.7+** (for database)
- ✅ **Flutter SDK** (for mobile app development)
- ✅ **Web Browser** (Chrome, Firefox, Safari, Edge)

### **Installation Commands:**
```bash
# macOS
brew install php mysql flutter

# Ubuntu/Debian
sudo apt-get install php mysql-server
# Install Flutter: https://flutter.dev/docs/get-started/install

# Windows
# Download PHP: https://www.php.net/downloads
# Download MySQL: https://dev.mysql.com/downloads/mysql/
# Download Flutter: https://flutter.dev/docs/get-started/install
```

---

## 🗄️ **Step 1: Database Setup**

### **1.1 Setup Database with Provided SQL File**
```bash
# Make script executable
chmod +x setup_database.sh

# Run database setup
./setup_database.sh
```

**The script will:**
- ✅ Test MySQL connection
- ✅ Create database: `myclo4dz_new_kftdb`
- ✅ Import all tables and data from `admin/myclo4dz_new_kftdb (1).sql`
- ✅ Verify database setup

### **1.2 Manual Database Setup (Alternative)**
```bash
# Connect to MySQL
mysql -u root -p

# Create database
CREATE DATABASE myclo4dz_new_kftdb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# Import SQL file
mysql -u root -p myclo4dz_new_kftdb < "admin/myclo4dz_new_kftdb (1).sql"

# Verify import
USE myclo4dz_new_kftdb;
SHOW TABLES;
```

---

## 🖥️ **Step 2: PHP Server Dashboard**

### **2.1 Start PHP Server**
```bash
# Make script executable
chmod +x run_php_server.sh

# Start PHP server
./run_php_server.sh
```

**Server will start on:**
- 🌐 **Admin Panel**: http://localhost:9001/admin/
- 🔌 **API Endpoint**: http://localhost:9001/admin/api/
- 📱 **App API**: http://localhost:9001/admin/username_check_app.php

### **2.2 Manual PHP Server Start**
```bash
# Start from project root
php -S 0.0.0.0:9001
```

### **2.3 Access Admin Panel**
1. Open browser: http://localhost:9001/admin/
2. Login with admin credentials (check database for users)
3. Manage users, courses, and settings

---

## 📱 **Step 3: Flutter App**

### **3.1 Start Flutter App**
```bash
# Make script executable
chmod +x run_flutter_app.sh

# Start Flutter app
./run_flutter_app.sh
```

**App will be available at:**
- 🌐 **Local**: http://localhost:8080
- 🌐 **Network**: http://***************:8080

### **3.2 Manual Flutter Start**
```bash
# Get dependencies
flutter pub get

# Run on web
flutter run -d chrome --web-port 8080

# Or run on mobile
flutter run -d android
flutter run -d ios
```

---

## 🔧 **HTTP Configuration Changes Made**

### **Network Configuration Updated:**
- ✅ **Production endpoint**: Changed from HTTPS to HTTP
- ✅ **Development endpoints**: All use HTTP
- ✅ **Vimeo connections**: Updated to use HTTP
- ✅ **SSL requirements**: Removed for faster loading

### **Files Modified:**
- `lib/config/network_config.dart` - Updated endpoints to HTTP
- `lib/services/instant_loading_service.dart` - HTTP preconnections
- `admin/username_check_app.php` - Enhanced debugging
- `lib/pages/enhanced_login_page.dart` - Fixed URL construction

---

## 🎯 **Testing the Setup**

### **1. Test Database Connection**
```bash
mysql -u myclo4dz_new_kftdb -p"U.q.!)hDK+gR" -e "USE myclo4dz_new_kftdb; SELECT COUNT(*) FROM users;"
```

### **2. Test PHP Server**
```bash
curl http://localhost:9001/admin/
curl http://localhost:9001/admin/api/ping.php
```

### **3. Test Flutter App**
1. Open: http://localhost:8080
2. Try username login
3. Check network tab for HTTP requests
4. Verify instant loading

---

## 🚀 **Quick Start Commands**

### **Complete Setup (3 Terminal Windows):**

**Terminal 1 - Database:**
```bash
./setup_database.sh
```

**Terminal 2 - PHP Server:**
```bash
./run_php_server.sh
```

**Terminal 3 - Flutter App:**
```bash
./run_flutter_app.sh
```

---

## 📦 **Deployment Files**

### **Available Builds:**
- ✅ `flutter_web_http_enabled.zip` - **Latest with HTTP support**
- ✅ `dashboard_backup_username_login_fixed.zip` - **Updated PHP backend**
- ✅ `admin/myclo4dz_new_kftdb (1).sql` - **Complete database**

### **Production Deployment:**
1. Extract `flutter_web_http_enabled.zip` to web server
2. Extract dashboard backup to server
3. Import database SQL file
4. Update config files with production settings
5. Test HTTP endpoints

---

## 🔍 **Troubleshooting**

### **Database Issues:**
```bash
# Check MySQL service
sudo systemctl status mysql  # Linux
brew services list | grep mysql  # macOS

# Reset MySQL password
sudo mysql_secure_installation
```

### **PHP Server Issues:**
```bash
# Check PHP version
php -v

# Check if port is in use
lsof -i :9001

# Kill process on port
kill -9 $(lsof -t -i:9001)
```

### **Flutter Issues:**
```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter build web --release

# Check Flutter doctor
flutter doctor
```

---

## 🎉 **Success Indicators**

### **✅ Everything Working When:**
- Database setup completes without errors
- PHP server starts on http://localhost:9001
- Admin panel loads at http://localhost:9001/admin/
- Flutter app loads at http://localhost:8080
- Username login works without "failed to check username" error
- App loads instantly with HTTP support

---

**🎯 The app now works with HTTP only - no HTTPS required for loading! This provides faster, simpler deployment and development experience.**
