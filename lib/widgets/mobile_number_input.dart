import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_system/kft_design_system.dart';
import '../utils/phone_utils.dart';
import 'country_code_picker.dart';

/// Mobile Number Input Widget
/// Combines country code picker with phone number input
class MobileNumberInput extends StatefulWidget {
  final TextEditingController? controller;
  final String label;
  final String? hint;
  final String? errorText;
  final String? helperText;
  final bool isEnabled;
  final bool isRequired;
  final bool autofocus;
  final String initialCountryCode;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final Function()? onTap;
  final TextInputAction? textInputAction;
  final FocusNode? focusNode;

  const MobileNumberInput({
    Key? key,
    this.controller,
    required this.label,
    this.hint,
    this.errorText,
    this.helperText,
    this.isEnabled = true,
    this.isRequired = false,
    this.autofocus = false,
    this.initialCountryCode = '+91',
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.textInputAction,
    this.focusNode,
  }) : super(key: key);

  @override
  State<MobileNumberInput> createState() => _MobileNumberInputState();
}

class _MobileNumberInputState extends State<MobileNumberInput> {
  late String _selectedCountryCode;
  String? _phoneValidationMessage;
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _selectedCountryCode = widget.initialCountryCode;
    _controller = widget.controller ?? TextEditingController();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (widget.label.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              widget.label + (widget.isRequired ? ' *' : ''),
              style: KFTDesignSystem.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
                color: KFTDesignSystem.textPrimaryColor,
              ),
            ),
          ),

        // Input Row
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Country Code Picker
            CountryCodePicker(
              selectedCountryCode: _selectedCountryCode,
              onCountryCodeChanged: (code) {
                setState(() {
                  _selectedCountryCode = code;
                });
                _validateAndNotify();
              },
              isEnabled: widget.isEnabled,
            ),
            
            const SizedBox(width: 12),
            
            // Phone Number Input
            Expanded(
              child: TextField(
                controller: _controller,
                focusNode: widget.focusNode,
                keyboardType: TextInputType.phone,
                textInputAction: widget.textInputAction ?? TextInputAction.done,
                enabled: widget.isEnabled,
                autofocus: widget.autofocus,
                maxLength: 15, // International standard
                onChanged: (value) {
                  _validatePhoneNumber(value);
                  widget.onChanged?.call(_getFullPhoneNumber());
                },
                onSubmitted: (value) {
                  widget.onSubmitted?.call(_getFullPhoneNumber());
                },
                onTap: widget.onTap,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(15),
                ],
                style: KFTDesignSystem.bodyMedium,
                cursorColor: KFTDesignSystem.primaryColor,
                decoration: InputDecoration(
                  hintText: widget.hint ?? _getDefaultHint(),
                  errorText: widget.errorText,
                  counterText: '', // Hide character counter
                  hintStyle: KFTDesignSystem.bodyMedium.copyWith(
                    color: KFTDesignSystem.textSecondaryColor,
                  ),
                  errorStyle: KFTDesignSystem.bodySmall.copyWith(
                    color: KFTDesignSystem.errorColor,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: KFTDesignSystem.borderColor,
                      width: 1.5,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: KFTDesignSystem.borderColor,
                      width: 1.5,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: KFTDesignSystem.primaryColor,
                      width: 2,
                    ),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: KFTDesignSystem.errorColor,
                      width: 1.5,
                    ),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: KFTDesignSystem.errorColor,
                      width: 2,
                    ),
                  ),
                  disabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: KFTDesignSystem.borderColor.withOpacity(0.5),
                      width: 1.5,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                ),
              ),
            ),
          ],
        ),

        // Helper/Validation Text
        if (_phoneValidationMessage != null || widget.helperText != null)
          Padding(
            padding: const EdgeInsets.only(top: 6, left: 4),
            child: Text(
              _phoneValidationMessage ?? widget.helperText!,
              style: KFTDesignSystem.bodySmall.copyWith(
                color: _phoneValidationMessage != null && _phoneValidationMessage!.contains('too')
                  ? KFTDesignSystem.errorColor
                  : KFTDesignSystem.textSecondaryColor,
              ),
            ),
          ),
      ],
    );
  }

  String _getDefaultHint() {
    switch (_selectedCountryCode) {
      case '+91':
        return '9876543210';
      case '+1':
        return '2345678901';
      case '+44':
        return '7700900123';
      default:
        return 'Enter phone number';
    }
  }

  String _getFullPhoneNumber() {
    final phoneNumber = _controller.text.trim();
    if (phoneNumber.isEmpty) return '';
    return '$_selectedCountryCode$phoneNumber';
  }

  void _validatePhoneNumber(String value) {
    if (value.isEmpty) {
      setState(() {
        _phoneValidationMessage = null;
      });
      return;
    }

    final fullNumber = _getFullPhoneNumber();
    final validationResult = PhoneUtils.validatePhoneNumber(fullNumber);
    
    setState(() {
      if (validationResult.isValid) {
        _phoneValidationMessage = 'Valid mobile number format';
      } else {
        _phoneValidationMessage = validationResult.errorMessage;
      }
    });
  }

  void _validateAndNotify() {
    final phoneNumber = _controller.text.trim();
    if (phoneNumber.isNotEmpty) {
      _validatePhoneNumber(phoneNumber);
      widget.onChanged?.call(_getFullPhoneNumber());
    }
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }
}
