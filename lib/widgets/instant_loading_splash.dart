import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/instant_loading_service.dart';

/// ⚡ Instant Loading Splash Screen ⚡
/// Ultra-fast splash screen that appears instantly while Flutter loads
class InstantLoadingSplash extends StatefulWidget {
  final Widget child;
  final Duration minDisplayTime;
  final VoidCallback? onLoadingComplete;

  const InstantLoadingSplash({
    Key? key,
    required this.child,
    this.minDisplayTime = const Duration(milliseconds: 500),
    this.onLoadingComplete,
  }) : super(key: key);

  @override
  State<InstantLoadingSplash> createState() => _InstantLoadingSplashState();
}

class _InstantLoadingSplashState extends State<InstantLoadingSplash>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _logoController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _logoAnimation;
  
  bool _isLoading = true;
  bool _canHide = false;
  final InstantLoadingService _loadingService = InstantLoadingService();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startLoading();
  }

  void _setupAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));

    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    // Start logo animation immediately
    _logoController.forward();
  }

  Future<void> _startLoading() async {
    try {
      // Initialize instant loading service
      await _loadingService.initialize();
      
      // Wait for minimum display time
      await Future.delayed(widget.minDisplayTime);
      
      setState(() {
        _canHide = true;
      });
      
      // Hide splash screen
      _hideSplash();
    } catch (e) {
      debugPrint('❌ Loading error: $e');
      // Hide splash even on error
      _hideSplash();
    }
  }

  void _hideSplash() {
    if (!mounted) return;
    
    _fadeController.forward().then((_) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        _loadingService.markFirstLoadComplete();
        widget.onLoadingComplete?.call();
      }
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _logoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isLoading) {
      return widget.child;
    }

    return Scaffold(
      body: AnimatedBuilder(
        animation: _fadeAnimation,
        builder: (context, child) {
          return Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF3D5AFE),
                    Color(0xFF536DFE),
                  ],
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Animated logo
                    AnimatedBuilder(
                      animation: _logoAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _logoAnimation.value,
                          child: Container(
                            width: 100,
                            height: 100,
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(25),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                              ],
                            ),
                            child: const Center(
                              child: Text(
                                '🥋',
                                style: TextStyle(fontSize: 50),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    
                    const SizedBox(height: 32),
                    
                    // App title
                    const Text(
                      'KFT Fitness',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 1.2,
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Subtitle
                    Text(
                      'Personal Training App',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 16,
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                    
                    const SizedBox(height: 48),
                    
                    // Loading indicator
                    SizedBox(
                      width: 40,
                      height: 40,
                      child: CircularProgressIndicator(
                        strokeWidth: 3,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.white.withOpacity(0.8),
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Loading text
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 500),
                      child: Text(
                        _getLoadingText(),
                        key: ValueKey(_getLoadingText()),
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 14,
                          fontWeight: FontWeight.w300,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  String _getLoadingText() {
    if (!_loadingService.isInitialized) {
      return 'Initializing...';
    } else if (!_canHide) {
      return 'Loading your workout...';
    } else {
      return 'Ready!';
    }
  }
}

/// Minimal instant splash for web
class WebInstantSplash extends StatelessWidget {
  final Widget child;

  const WebInstantSplash({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!kIsWeb) return child;

    return FutureBuilder<void>(
      future: InstantLoadingService().waitForInitialization(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          return child;
        }

        // Show minimal loading while waiting
        return const Material(
          color: Color(0xFF3D5AFE),
          child: Center(
            child: SizedBox(
              width: 40,
              height: 40,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
          ),
        );
      },
    );
  }
}
