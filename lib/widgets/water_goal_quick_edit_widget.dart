import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/water_reminder_provider.dart';
import '../services/water_goal_service.dart';
import 'water_goal_settings_widget.dart';

/// Quick water goal editing widget for main screens
/// Provides one-tap goal modification with minimal UI
class WaterGoalQuickEditWidget extends StatelessWidget {
  final bool showFullSettings;
  final EdgeInsets? padding;
  final VoidCallback? onGoalUpdated;

  const WaterGoalQuickEditWidget({
    Key? key,
    this.showFullSettings = true,
    this.padding,
    this.onGoalUpdated,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<WaterReminderProvider>(
      builder: (context, provider, child) {
        final goalSettings = provider.goalSettings;
        if (goalSettings == null) {
          return const SizedBox.shrink();
        }
        
        return Container(
          padding: padding ?? const EdgeInsets.all(12),
          child: _buildGoalDisplay(context, provider, goalSettings),
        );
      },
    );
  }

  Widget _buildGoalDisplay(
    BuildContext context,
    WaterReminderProvider provider,
    WaterGoalSettings settings,
  ) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        // Goal icon and label
        Icon(
          Icons.flag_outlined,
          size: 16,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(width: 6),
        Text(
          'Goal:',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 4),
        
        // Current goal display
        GestureDetector(
          onTap: () => _showGoalOptions(context),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.colorScheme.primary.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  settings.displayText,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.edit,
                  size: 12,
                  color: theme.colorScheme.primary,
                ),
              ],
            ),
          ),
        ),
        
        const Spacer(),
        
        // Quick adjustment buttons
        _buildQuickAdjustmentButtons(context, provider, settings),
      ],
    );
  }

  Widget _buildQuickAdjustmentButtons(
    BuildContext context,
    WaterReminderProvider provider,
    WaterGoalSettings settings,
  ) {
    final theme = Theme.of(context);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Decrease button
        _buildAdjustmentButton(
          context,
          Icons.remove,
          () => _adjustGoal(context, provider, -250),
          enabled: settings.goalInMl > 500,
        ),
        const SizedBox(width: 8),
        // Increase button
        _buildAdjustmentButton(
          context,
          Icons.add,
          () => _adjustGoal(context, provider, 250),
          enabled: settings.goalInMl < 5000,
        ),
      ],
    );
  }

  Widget _buildAdjustmentButton(
    BuildContext context,
    IconData icon,
    VoidCallback onPressed,
    {bool enabled = true}
  ) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: enabled ? onPressed : null,
      child: Container(
        width: 28,
        height: 28,
        decoration: BoxDecoration(
          color: enabled 
              ? theme.colorScheme.primary.withOpacity(0.1)
              : theme.colorScheme.onSurface.withOpacity(0.05),
          borderRadius: BorderRadius.circular(14),
          border: Border.all(
            color: enabled 
                ? theme.colorScheme.primary.withOpacity(0.3)
                : theme.colorScheme.onSurface.withOpacity(0.1),
          ),
        ),
        child: Icon(
          icon,
          size: 14,
          color: enabled 
              ? theme.colorScheme.primary
              : theme.colorScheme.onSurface.withOpacity(0.3),
        ),
      ),
    );
  }

  void _showGoalOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _buildGoalOptionsSheet(context),
    );
  }

  Widget _buildGoalOptionsSheet(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurface.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          
          // Title
          Text(
            'Goal Options',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          
          // Quick presets
          _buildQuickPresets(context),
          const SizedBox(height: 16),
          
          // Full settings button
          if (showFullSettings) ...[
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Colors.transparent,
                    builder: (context) => const WaterGoalSettingsWidget(showAsBottomSheet: true),
                  );
                },
                icon: const Icon(Icons.settings),
                label: const Text('Advanced Settings'),
              ),
            ),
            const SizedBox(height: 8),
          ],
          
          // Close button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ),
          
          // Bottom padding for safe area
          SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
        ],
      ),
    );
  }

  Widget _buildQuickPresets(BuildContext context) {
    final theme = Theme.of(context);
    final presets = WaterGoalService.getGoalPresets();
    
    return Consumer<WaterReminderProvider>(
      builder: (context, provider, child) {
        final currentGoal = provider.goalSettings?.goalInMl ?? 2000;
        final preferredUnit = provider.goalSettings?.preferredUnit ?? WaterUnit.liters;
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Goals',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: presets.map((preset) {
                final isSelected = preset == currentGoal;
                final displayText = WaterGoalService.formatGoalDisplay(preset, preferredUnit);
                
                return GestureDetector(
                  onTap: () => _setGoal(context, provider, preset),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? theme.colorScheme.primary 
                          : theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected 
                            ? theme.colorScheme.primary 
                            : theme.colorScheme.outline,
                      ),
                    ),
                    child: Text(
                      displayText,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: isSelected 
                            ? Colors.white 
                            : theme.colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        );
      },
    );
  }

  Future<void> _adjustGoal(
    BuildContext context,
    WaterReminderProvider provider,
    int adjustment,
  ) async {
    final currentGoal = provider.goalSettings?.goalInMl ?? 2000;
    final newGoal = (currentGoal + adjustment).clamp(500, 5000);
    
    if (newGoal == currentGoal) return;
    
    try {
      await provider.updateDailyGoal(newGoal);
      HapticFeedback.lightImpact();
      
      // Show brief feedback
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Goal ${adjustment > 0 ? 'increased' : 'decreased'} to ${provider.getFormattedGoal()}'),
            duration: const Duration(seconds: 1),
          ),
        );
      }

      // Call the callback to refresh UI
      onGoalUpdated?.call();
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update goal: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _setGoal(
    BuildContext context,
    WaterReminderProvider provider,
    int goalInMl,
  ) async {
    try {
      await provider.updateDailyGoal(goalInMl);
      HapticFeedback.mediumImpact();
      
      if (context.mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text('Goal set to ${provider.getFormattedGoal()}'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }

      // Call the callback to refresh UI
      onGoalUpdated?.call();
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to set goal: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
