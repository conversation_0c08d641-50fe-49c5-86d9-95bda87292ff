import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/optional_pwa_service.dart';

/// PWA settings widget for enabling/disabling install prompts
class PWASettingsWidget extends StatefulWidget {
  const PWASettingsWidget({Key? key}) : super(key: key);

  @override
  State<PWASettingsWidget> createState() => _PWASettingsWidgetState();
}

class _PWASettingsWidgetState extends State<PWASettingsWidget> {
  final OptionalPWAService _pwaService = OptionalPWAService();

  @override
  Widget build(BuildContext context) {
    if (!kIsWeb) return const SizedBox.shrink();

    return ValueListenableBuilder<bool>(
      valueListenable: _pwaService.isEnabled,
      builder: (context, isEnabled, child) {
        return Card(
          margin: const EdgeInsets.all(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFF6366F1).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.install_mobile,
                        color: Color(0xFF6366F1),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'App Installation',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                SwitchListTile(
                  title: const Text(
                    'Show Install Prompts',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  subtitle: Text(
                    isEnabled
                        ? 'You\'ll see prompts to install the app for better performance'
                        : 'Install prompts are disabled',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                    ),
                  ),
                  value: isEnabled,
                  activeColor: const Color(0xFF6366F1),
                  onChanged: (value) async {
                    await _pwaService.setEnabled(value);
                    
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            value
                                ? 'PWA install prompts enabled'
                                : 'PWA install prompts disabled',
                          ),
                          duration: const Duration(seconds: 2),
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                    }
                  },
                ),
                if (_pwaService.isInstallable && isEnabled) ...[
                  const Divider(),
                  ListTile(
                    leading: const Icon(
                      Icons.download,
                      color: Color(0xFF6366F1),
                    ),
                    title: const Text(
                      'Install Now',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    subtitle: const Text(
                      'Install the app for faster loading and offline access',
                      style: TextStyle(fontSize: 13),
                    ),
                    trailing: const Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                    ),
                    onTap: () async {
                      final success = await _pwaService.promptInstall();
                      if (mounted && success) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('App installation started!'),
                            duration: Duration(seconds: 2),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      }
                    },
                  ),
                ],
                if (_pwaService.isInstalled) ...[
                  const Divider(),
                  ListTile(
                    leading: const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                    ),
                    title: const Text(
                      'App Installed',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    subtitle: const Text(
                      'You\'re using the installed version of the app',
                      style: TextStyle(fontSize: 13),
                    ),
                  ),
                ],
                if (_pwaService.isMobile && !_pwaService.isInstallable) ...[
                  const Divider(),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Colors.blue,
                              size: 20,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Manual Installation',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _pwaService.getInstallInstructions(),
                          style: const TextStyle(
                            fontSize: 13,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
