import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/optional_pwa_service.dart';
import '../design_system/kft_design_system.dart';

/// Optional PWA install widget that can be enabled/disabled
/// Shows as a card or floating action button, not as a banner
class OptionalPWAInstallWidget extends StatefulWidget {
  final PWADisplayType displayType;
  final VoidCallback? onInstallSuccess;
  final VoidCallback? onDismiss;

  const OptionalPWAInstallWidget({
    Key? key,
    this.displayType = PWADisplayType.card,
    this.onInstallSuccess,
    this.onDismiss,
  }) : super(key: key);

  @override
  State<OptionalPWAInstallWidget> createState() => _OptionalPWAInstallWidgetState();
}

enum PWADisplayType {
  card,
  floatingButton,
  listTile,
}

class _OptionalPWAInstallWidgetState extends State<OptionalPWAInstallWidget>
    with SingleTickerProviderStateMixin {
  final OptionalPWAService _pwaService = OptionalPWAService();
  bool _isInstalling = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _handleInstall() async {
    if (_isInstalling) return;

    setState(() {
      _isInstalling = true;
    });

    try {
      final success = await _pwaService.promptInstall();

      if (success) {
        await Future.delayed(const Duration(milliseconds: 300));
        widget.onInstallSuccess?.call();
      }
    } catch (e) {
      debugPrint('Error installing PWA: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isInstalling = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!kIsWeb) return const SizedBox.shrink();

    return ValueListenableBuilder<bool>(
      valueListenable: _pwaService.isEnabled,
      builder: (context, isEnabled, child) {
        if (!isEnabled || !_pwaService.isInstallable) {
          return const SizedBox.shrink();
        }

        return AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: _buildWidget(context),
            );
          },
        );
      },
    );
  }

  Widget _buildWidget(BuildContext context) {
    switch (widget.displayType) {
      case PWADisplayType.card:
        return _buildCard(context);
      case PWADisplayType.floatingButton:
        return _buildFloatingButton(context);
      case PWADisplayType.listTile:
        return _buildListTile(context);
    }
  }

  Widget _buildCard(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF6366F1).withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.install_mobile,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Install App',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: widget.onDismiss,
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Get the full app experience with offline access and faster loading.',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isInstalling ? null : _handleInstall,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: const Color(0xFF6366F1),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isInstalling
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6366F1)),
                        ),
                      )
                    : const Text(
                        'Install Now',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingButton(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: _isInstalling ? null : _handleInstall,
      backgroundColor: const Color(0xFF6366F1),
      foregroundColor: Colors.white,
      icon: _isInstalling
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : const Icon(Icons.install_mobile),
      label: Text(_isInstalling ? 'Installing...' : 'Install App'),
    );
  }

  Widget _buildListTile(BuildContext context) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: const Color(0xFF6366F1).withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Icon(
          Icons.install_mobile,
          color: Color(0xFF6366F1),
          size: 24,
        ),
      ),
      title: const Text(
        'Install App',
        style: TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: const Text(
        'Get faster loading and offline access',
        style: TextStyle(
          fontSize: 13,
          color: Colors.grey,
        ),
      ),
      trailing: _isInstalling
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6366F1)),
              ),
            )
          : const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey,
            ),
      onTap: _isInstalling ? null : _handleInstall,
    );
  }
}
