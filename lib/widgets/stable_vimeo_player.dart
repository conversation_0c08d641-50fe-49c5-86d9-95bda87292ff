// This file is deprecated. Use SimpleVimeoPlayer for all Vimeo playback.

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:vimeo_video_player/vimeo_video_player.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import '../models/course_video.dart';
import '../models/user_profile.dart';
import '../services/api_service.dart';
import '../services/video_streak_service.dart';
import '../utils/video_security_helper.dart';
import 'video_watermark.dart';
import 'simple_vimeo_player.dart';
import '../services/progress_service.dart';

/// A stable, reliable Vimeo video player with enhanced security and domain verification
class StableVimeoPlayer extends StatefulWidget {
  final CourseVideo video;
  final UserProfile? userProfile;
  final Function(int)? onProgress;
  final Function()? onCompleted;
  final Function(String)? onError;
  final Function()? onPlay;
  final Function()? onPause;
  final Function()? onReady;
  final Function()? onStreakUpdated;
  final bool autoPlay;
  final bool showControls;
  final bool enableFullscreen;
  final bool enableQualitySelection;
  final bool showProgressIndicator;
  final List<double> playbackSpeeds;
  final double initialPlaybackSpeed;

  const StableVimeoPlayer({
    Key? key,
    required this.video,
    this.userProfile,
    this.onProgress,
    this.onCompleted,
    this.onError,
    this.onPlay,
    this.onPause,
    this.onReady,
    this.onStreakUpdated,
    this.autoPlay = false,
    this.showControls = true,
    this.enableFullscreen = true,
    this.enableQualitySelection = true,
    this.showProgressIndicator = true,
    this.playbackSpeeds = const [0.5, 0.75, 1.0, 1.25, 1.5, 2.0],
    this.initialPlaybackSpeed = 1.0,
  }) : super(key: key);

  @override
  State<StableVimeoPlayer> createState() => _StableVimeoPlayerState();
}

class _StableVimeoPlayerState extends State<StableVimeoPlayer>
    with TickerProviderStateMixin {
  // Player state
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  bool _isPlaying = false;
  bool _isCompleted = false;
  bool _isBuffering = false;
  bool _isFullscreen = false;
  bool _showControls = true;
  bool _isDragging = false;

  // Video progress
  int _currentPosition = 0;
  int _duration = 0;
  double _currentPlaybackSpeed = 1.0;

  // Security and domain verification
  final ApiService _apiService = ApiService();
  String? _secureEmbedUrl;
  bool _isDomainVerified = false;
  bool _isSecurityValidated = false;

  // Player controllers
  WebViewController? _webViewController;
  VideoPlayerController? _videoPlayerController;
  ChewieController? _chewieController;

  // Animation controllers
  late AnimationController _controlsAnimationController;
  late AnimationController _loadingAnimationController;
  late Animation<double> _controlsOpacity;
  late Animation<double> _loadingRotation;

  // Progress tracking
  int _lastProgressUpdate = 0;
  static const int _progressUpdateInterval = 5; // seconds
  bool _streakChecked = false;

  // Player type selection
  PlayerType _selectedPlayerType = PlayerType.webview;

  // Skip controls state
  bool _showSkipForward = false;
  bool _showSkipBackward = false;
  Timer? _skipIndicatorTimer;

  @override
  void initState() {
    super.initState();
    _currentPlaybackSpeed = widget.initialPlaybackSpeed;
    _initializeAnimations();
    _initializePlayer();

    // Enable wakelock to keep screen on during video playback
    _enableWakelock();
  }

  void _initializeAnimations() {
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _loadingAnimationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _controlsOpacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controlsAnimationController,
      curve: Curves.easeInOut,
    ));

    _loadingRotation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.linear,
    ));

    if (widget.showControls) {
      _controlsAnimationController.forward();
    }
  }

  @override
  void dispose() {
    _controlsAnimationController.dispose();
    _loadingAnimationController.dispose();
    _videoPlayerController?.dispose();
    _chewieController?.dispose();
    _skipIndicatorTimer?.cancel();

    // Reset fullscreen state when disposing
    if (_isFullscreen) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }

    // Disable wakelock when video player is disposed
    _disableWakelock();

    super.dispose();
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // Step 1: Try to verify domain restrictions and security
      await _verifyDomainAndSecurity();

      // Step 2: If verification fails, use fallback approach
      if (!_isDomainVerified || !_isSecurityValidated) {
        debugPrint('Domain verification failed, using fallback approach');
        await _initializeFallbackPlayer();
        return;
      }

      // Step 3: Determine best player type based on platform and video availability
      _selectedPlayerType = await _determineBestPlayerType();

      // Step 4: Initialize the selected player
      await _initializeSelectedPlayer();

      widget.onReady?.call();
    } catch (e) {
      debugPrint('Primary initialization failed, trying fallback: $e');
      await _initializeFallbackPlayer();
    }
  }

  Future<void> _verifyDomainAndSecurity() async {
    try {
      // Extract Vimeo ID
      final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);
      if (vimeoId == null) {
        throw Exception('Invalid Vimeo video ID');
      }

      // First, try to get a secure embed URL that bypasses domain restrictions
      _secureEmbedUrl = await _getSecureEmbedUrl(vimeoId);

      if (_secureEmbedUrl != null) {
        _isDomainVerified = true;
        _isSecurityValidated = true;

        // Log successful access
        await VideoSecurityHelper.logVideoAccess(
          vimeoId: vimeoId,
          videoId: widget.video.id,
          userId: await _getCurrentUserId(),
          action: 'secure_access',
        );
      } else {
        // Fallback: Try standard domain verification
        _isDomainVerified = await _verifyVimeoDomainRestrictions(vimeoId);

        if (_isDomainVerified) {
          // Use standard embed URL
          _secureEmbedUrl = widget.video.videoEmbedUrl ??
                           'https://player.vimeo.com/video/$vimeoId?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1';
          _isSecurityValidated = true;
        } else {
          throw Exception('Domain verification failed - video not accessible from this domain');
        }
      }
    } catch (e) {
      debugPrint('Domain/Security verification failed: $e');
      _isDomainVerified = false;
      _isSecurityValidated = false;
    }
  }

  Future<String?> _getSecureEmbedUrl(String vimeoId) async {
    try {
      // Try to get a secure embed URL that works with privacy restrictions
      // Use the current ngrok domain for domain verification
      final response = await _apiService.makeApiRequest(
        'get_secure_vimeo_embed.php',
        method: 'POST',
        data: {
          'vimeo_id': vimeoId,
          'video_id': widget.video.id,
          'domain': '9e4b-2409-40c0-19-37c6-7db0-f1a0-f9aa-ea37.ngrok-free.app', // Use current ngrok domain
          'user_id': await _getCurrentUserId(),
        },
      );

      if (response['success'] == true && response['secure_embed_url'] != null) {
        debugPrint('Secure embed URL obtained: ${response['secure_embed_url']}');
        return response['secure_embed_url'] as String;
      }

      debugPrint('Failed to get secure embed URL: ${response['error'] ?? 'Unknown error'}');
      return null;
    } catch (e) {
      debugPrint('Failed to get secure embed URL: $e');
      return null;
    }
  }

  Future<bool> _verifyVimeoDomainRestrictions(String vimeoId) async {
    try {
      // Simplified domain verification - just check if video is accessible
      final response = await _apiService.makeApiRequest(
        'verify_vimeo_domain.php',
        method: 'POST',
        data: {
          'vimeo_id': vimeoId,
          'video_id': widget.video.id,
          'domain': '9e4b-2409-40c0-19-37c6-7db0-f1a0-f9aa-ea37.ngrok-free.app', // Use current ngrok domain
        },
      );

      // If backend verification fails, assume video is accessible (fallback)
      return response['success'] == true || response['domain_verified'] == true;
    } catch (e) {
      debugPrint('Domain verification error: $e');
      // On error, assume video is accessible (graceful fallback)
      return true;
    }
  }

  Future<void> _initializeFallbackPlayer() async {
    try {
      debugPrint('Initializing fallback player for privacy-restricted video');

      // Extract Vimeo ID
      final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);
      if (vimeoId == null) {
        throw Exception('Invalid Vimeo video ID');
      }

      // Use basic embed URL without domain restrictions
      _secureEmbedUrl = widget.video.videoEmbedUrl ??
                       'https://player.vimeo.com/video/$vimeoId?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1';

      // Set flags for fallback mode
      _isDomainVerified = true;
      _isSecurityValidated = true;

      // FORCE WebView player for speed controls even in fallback mode
      if (widget.playbackSpeeds.length > 1) {
        debugPrint('Fallback: Speed controls enabled (${widget.playbackSpeeds.length} speeds), using WebView player');
        _selectedPlayerType = PlayerType.webview;
      } else {
        debugPrint('Fallback: No speed controls needed, using WebView player');
        _selectedPlayerType = PlayerType.webview;
      }

      // Initialize web view player with fallback URL
      await _initializeWebViewPlayer();

      widget.onReady?.call();

      debugPrint('Fallback player initialized successfully');
    } catch (e) {
      _handleError('Fallback player initialization failed: $e');
    }
  }

  Future<PlayerType> _determineBestPlayerType() async {
    // Determine the best player type based on platform and video availability
    if (kIsWeb) {
      debugPrint('StableVimeoPlayer: Platform is web, using WebView player');
      return PlayerType.webview;
    }

    debugPrint('StableVimeoPlayer: Platform is mobile, determining best player type');

    // FORCE WebView player for ALL mobile videos to handle domain restrictions
    // The VimeoVideoPlayer widget doesn't send proper referrer headers for domain verification
    debugPrint('StableVimeoPlayer: Using WebView player for mobile with proper referrer headers');
    return PlayerType.webview;

    // LEGACY CODE - keeping for reference but not using
    // The VimeoVideoPlayer widget fails with domain restrictions, so we always use WebView
    /*
    // FORCE WebView player for mobile to enable speed controls
    // The VimeoVideoPlayer widget doesn't support playback speed changes
    if (widget.playbackSpeeds.length > 1) {
      debugPrint('StableVimeoPlayer: Speed controls enabled (${widget.playbackSpeeds.length} speeds), forcing WebView player for mobile');
      return PlayerType.webview;
    }

    // For mobile without speed controls, try to get direct video URL for better performance
    try {
      final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);
      if (vimeoId != null) {
        debugPrint('StableVimeoPlayer: Trying to get direct video URL for Vimeo ID: $vimeoId');
        // Try to get direct video URL through backend
        final response = await _apiService.makeApiRequest(
          'get_vimeo_direct_url.php',
          method: 'POST',
          data: {
            'vimeo_id': vimeoId,
            'video_id': widget.video.id,
            'quality': 'auto',
          },
        );

        if (response['success'] == true && response['direct_url'] != null) {
          debugPrint('StableVimeoPlayer: Direct video URL available, using DirectVideo player');
          return PlayerType.directVideo;
        } else {
          debugPrint('StableVimeoPlayer: Direct video URL not available: ${response['error'] ?? 'Unknown error'}');
        }
      } else {
        debugPrint('StableVimeoPlayer: Could not extract Vimeo ID from URL: ${widget.video.videoUrl}');
      }
    } catch (e) {
      debugPrint('StableVimeoPlayer: Failed to get direct video URL: $e');
    }

    // Fallback to Vimeo player widget (only if no speed controls needed)
    debugPrint('StableVimeoPlayer: Falling back to VimeoWidget player');
    return PlayerType.vimeoWidget;
    */
  }

  Future<void> _initializeSelectedPlayer() async {
    switch (_selectedPlayerType) {
      case PlayerType.webview:
        await _initializeWebViewPlayer();
        break;
      case PlayerType.directVideo:
        await _initializeDirectVideoPlayer();
        break;
      case PlayerType.vimeoWidget:
        await _initializeVimeoWidgetPlayer();
        break;
    }
  }

  Future<void> _initializeWebViewPlayer() async {
    try {
      // Extract domain for professional headers
      final baseUrl = ApiService.baseUrl;
      final uri = Uri.parse(baseUrl);
      final domain = uri.host;

      _webViewController = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setUserAgent('KFT-Fitness-App/1.0 (Flutter Mobile; Domain: $domain)')
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (String url) {
              setState(() {
                _isLoading = true;
                _isBuffering = true;
              });
              debugPrint('StableVimeoPlayer: Page started loading with domain verification: $url');
            },
            onPageFinished: (String url) {
              setState(() {
                _isLoading = false;
                _isBuffering = false;
              });
              debugPrint('StableVimeoPlayer: Page finished loading with domain verification: $url');
              _setupWebPlayerListeners();
            },
            onWebResourceError: (WebResourceError error) {
              debugPrint('StableVimeoPlayer: Web resource error: ${error.description}');
              _handleError('Video loading error: ${error.description}');
            },
            onNavigationRequest: (NavigationRequest request) {
              debugPrint('StableVimeoPlayer: Navigation request with domain verification: ${request.url}');
              // Allow all Vimeo-related navigation
              if (request.url.contains('vimeo.com') || request.url.contains('player.vimeo.com')) {
                return NavigationDecision.navigate;
              }
              return NavigationDecision.prevent;
            },
          ),
        )
        // Enable inline media playback and gesture permissions for Vimeo seeking
        ..enableZoom(false);

      // Load the secure embed HTML with professional domain verification
      final embedHtml = _buildSecureEmbedHtml();
      await _webViewController!.loadHtmlString(embedHtml, baseUrl: 'https://$domain/');

      debugPrint('StableVimeoPlayer: Professional WebView initialized with domain: $domain');

    } catch (e) {
      debugPrint('StableVimeoPlayer: Failed to initialize professional WebView: $e');
      _handleError('Failed to initialize video player: $e');
    }
  }

  Future<void> _initializeDirectVideoPlayer() async {
    try {
      // Get direct video URL from backend
      final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);
      final response = await _apiService.makeApiRequest(
        'get_vimeo_direct_url.php',
        method: 'POST',
        data: {
          'vimeo_id': vimeoId,
          'video_id': widget.video.id,
          'quality': 'auto',
        },
      );

      if (response['success'] == true && response['direct_url'] != null) {
        final directUrl = response['direct_url'] as String;

        _videoPlayerController = VideoPlayerController.networkUrl(Uri.parse(directUrl));
        await _videoPlayerController!.initialize();

        _chewieController = ChewieController(
          videoPlayerController: _videoPlayerController!,
          autoPlay: widget.autoPlay,
          looping: false,
          showControls: widget.showControls,
          allowFullScreen: widget.enableFullscreen,
          allowMuting: true,
          allowPlaybackSpeedChanging: widget.playbackSpeeds.length > 1,
          playbackSpeeds: widget.playbackSpeeds,
          aspectRatio: 16 / 9,
          errorBuilder: (context, errorMessage) {
            return _buildErrorWidget();
          },
        );

        // Setup listeners for direct video player
        _videoPlayerController!.addListener(_onDirectVideoPlayerUpdate);

        setState(() {
          _isLoading = false;
        });
      } else {
        throw Exception('Failed to get direct video URL');
      }
    } catch (e) {
      _handleError('Failed to initialize direct video player: $e');
    }
  }

  Future<void> _initializeVimeoWidgetPlayer() async {
    try {
      final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);
      if (vimeoId == null) {
        throw Exception('Invalid Vimeo ID');
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      _handleError('Failed to initialize Vimeo widget player: $e');
    }
  }



  Future<int> _getCurrentUserId() async {
    try {
      final response = await _apiService.makeApiRequest('profile.php');
      if (response['success'] == true && response['profile'] != null) {
        return response['profile']['id'] ?? 1;
      }
      return 1; // Fallback
    } catch (e) {
      debugPrint('Failed to get current user ID: $e');
      return 1; // Fallback
    }
  }

  String _buildFallbackEmbedUrl() {
    final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);
    if (vimeoId == null) {
      return 'about:blank';
    }

    // Check if this is a private video with hash
    final privateHash = _extractVimeoPrivateHash(widget.video.videoUrl);

    // Build URL with or without private hash
    String baseUrl = 'https://player.vimeo.com/video/$vimeoId';
    Map<String, String> params = {
      'autoplay': '0',
      'title': '0',
      'byline': '0',
      'portrait': '0',
      'responsive': '1',
      'dnt': '1',
    };

    // Add private hash if available
    if (privateHash != null) {
      params['h'] = privateHash;
    }

    final queryString = params.entries
        .map((e) => '${e.key}=${e.value}')
        .join('&');

    return '$baseUrl?$queryString';
  }

  String? _extractVimeoPrivateHash(String url) {
    if (url.isEmpty) return null;

    // Pattern for private Vimeo URLs: https://vimeo.com/123456789/abcdef1234
    final privateUrlPattern = RegExp(r'vimeo\.com\/[0-9]+\/([a-zA-Z0-9]+)');
    final match = privateUrlPattern.firstMatch(url);
    if (match != null) {
      return match.group(1);
    }

    // Pattern for embed URLs with hash: https://player.vimeo.com/video/123456789?h=abcdef1234
    final embedUrlPattern = RegExp(r'player\.vimeo\.com\/video\/[0-9]+\?h=([a-zA-Z0-9]+)');
    final embedMatch = embedUrlPattern.firstMatch(url);
    if (embedMatch != null) {
      return embedMatch.group(1);
    }

    return null;
  }

  String _buildSecureEmbedHtml() {
    // Get the actual embed URL or use fallback
    final embedUrl = _secureEmbedUrl ?? _buildFallbackEmbedUrl();

    // Extract domain for referrer headers
    final baseUrl = ApiService.baseUrl;
    final uri = Uri.parse(baseUrl);
    final domain = uri.host;

    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="referrer" content="origin">
        <meta http-equiv="Content-Security-Policy" content="default-src 'self' https://player.vimeo.com https://vimeo.com https://$domain; script-src 'self' 'unsafe-inline' https://player.vimeo.com; frame-src https://player.vimeo.com;">
        <style>
            body {
                margin: 0;
                padding: 0;
                background: #000;
                overflow: hidden;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            iframe {
                width: 100%;
                height: 100vh;
                border: none;
                display: block;
            }
            .loading {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: white;
                text-align: center;
                z-index: 1000;
            }
            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 3px solid rgba(255,255,255,0.3);
                border-radius: 50%;
                border-top-color: #fff;
                animation: spin 1s ease-in-out infinite;
                margin: 0 auto 16px;
            }
            @keyframes spin {
                to { transform: rotate(360deg); }
            }
            .error {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: #ff4444;
                text-align: center;
                padding: 20px;
                z-index: 1000;
            }
            .fallback-message {
                position: absolute;
                bottom: 20px;
                left: 20px;
                right: 20px;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 10px;
                border-radius: 4px;
                font-size: 12px;
                text-align: center;
                z-index: 1001;
            }
        </style>
        <script src="https://player.vimeo.com/api/player.js"></script>
    </head>
    <body>
        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <div>Loading video...</div>
        </div>
        <div class="error" id="error" style="display: none;">
            <div>⚠️ Video Unavailable</div>
            <div style="font-size: 14px; margin-top: 8px;">Please check video privacy settings</div>
        </div>
        <iframe id="vimeo-player"
                src="$embedUrl"
                frameborder="0"
                allow="autoplay; fullscreen; picture-in-picture; accelerometer; gyroscope; microphone; camera; encrypted-media; web-share"
                allowfullscreen
                webkitPlaysinline="true"
                playsinline="true"
                referrerpolicy="origin"
                onload="onPlayerLoad()"
                onerror="onPlayerError()">
        </iframe>

        <script>
            var player;
            var isReady = false;
            var domain = '$domain';

            // Professional domain verification setup
            console.log('Professional Vimeo Player: Initializing with domain verification for:', domain);

            // Set document referrer for domain verification
            Object.defineProperty(document, 'referrer', {
                value: 'https://' + domain + '/',
                writable: false
            });

            // Set up professional headers
            if (window.fetch) {
                const originalFetch = window.fetch;
                window.fetch = function(url, options = {}) {
                    if (url.includes('vimeo.com')) {
                        options.headers = options.headers || {};
                        options.headers['Referer'] = 'https://' + domain + '/';
                        options.headers['Origin'] = 'https://' + domain;
                        options.headers['User-Agent'] = 'KFT-Fitness-App/1.0 (Flutter Mobile; Domain: ' + domain + ')';
                        console.log('Professional Vimeo Player: Adding domain verification headers for:', url);
                    }
                    return originalFetch(url, options);
                };
            }

            function onPlayerLoad() {
                try {
                    document.getElementById('loading').style.display = 'none';
                    var iframe = document.getElementById('vimeo-player');
                    player = new Vimeo.Player(iframe);

                    // Enhanced event listeners with error handling
                    player.on('loaded', function() {
                        isReady = true;
                        // Expose player globally after it's ready
                        window.vimeoPlayer = player;
                        console.log('Vimeo player is ready and exposed globally');
                        if (window.flutter_inappwebview) {
                            window.flutter_inappwebview.callHandler('onReady');
                        }
                    });

                    player.on('play', function() {
                        if (window.flutter_inappwebview) {
                            window.flutter_inappwebview.callHandler('onPlay');
                        }
                    });

                    player.on('pause', function() {
                        if (window.flutter_inappwebview) {
                            window.flutter_inappwebview.callHandler('onPause');
                        }
                    });

                    player.on('timeupdate', function(data) {
                        if (window.flutter_inappwebview) {
                            window.flutter_inappwebview.callHandler('onTimeUpdate', data.seconds);
                        }
                    });

                    player.on('ended', function() {
                        if (window.flutter_inappwebview) {
                            window.flutter_inappwebview.callHandler('onEnded');
                        }
                    });

                    player.on('bufferstart', function() {
                        if (window.flutter_inappwebview) {
                            window.flutter_inappwebview.callHandler('onBufferStart');
                        }
                    });

                    player.on('bufferend', function() {
                        if (window.flutter_inappwebview) {
                            window.flutter_inappwebview.callHandler('onBufferEnd');
                        }
                    });

                    player.on('error', function(error) {
                        console.error('Vimeo player error:', error);
                        if (window.flutter_inappwebview) {
                            window.flutter_inappwebview.callHandler('onError', error.message || 'Unknown player error');
                        }
                    });

                    // Get video metadata with enhanced error handling
                    player.getDuration().then(function(duration) {
                        console.log('Video duration retrieved successfully:', duration);
                        if (window.flutter_inappwebview) {
                            window.flutter_inappwebview.callHandler('onDuration', duration);
                        }
                    }).catch(function(error) {
                        console.error('Failed to get duration:', error);
                        console.error('Error type:', error.name);
                        console.error('Error message:', error.message);

                        // If it's a privacy error, try alternative approach
                        if (error.name === 'PrivacyError' || error.message.includes('privacy')) {
                            console.log('Privacy error detected, trying alternative approach...');
                            tryAlternativeEmbedMethod();
                        } else {
                            onPlayerError();
                        }
                    });

                } catch (error) {
                    console.error('Player initialization error:', error);
                    onPlayerError();
                }
            }

            function tryAlternativeEmbedMethod() {
                console.log('Trying alternative embed method...');

                // Try loading without app-specific parameters
                const iframe = document.getElementById('vimeo-player');
                const currentSrc = iframe.src;

                // Remove app and referrer parameters
                const cleanUrl = currentSrc
                    .replace(/&app=1/, '')
                    .replace(/&referrer=[^&]*/, '')
                    .replace(/&sharing=0/, '')
                    .replace(/&download=0/, '');

                console.log('Original URL:', currentSrc);
                console.log('Clean URL:', cleanUrl);

                // Reload with clean URL
                iframe.src = cleanUrl;

                // Give it some time to load
                setTimeout(function() {
                    try {
                        player = new Vimeo.Player(iframe);
                        player.getDuration().then(function(duration) {
                            console.log('Alternative method successful! Duration:', duration);
                            if (window.flutter_inappwebview) {
                                window.flutter_inappwebview.callHandler('onDuration', duration);
                                window.flutter_inappwebview.callHandler('onReady');
                            }
                        }).catch(function(error) {
                            console.error('Alternative method also failed:', error);
                            onPlayerError();
                        });
                    } catch (error) {
                        console.error('Alternative method initialization failed:', error);
                        onPlayerError();
                    }
                }, 3000);
            }

            function onPlayerError() {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
                if (window.flutter_inappwebview) {
                    window.flutter_inappwebview.callHandler('onError', 'Video cannot be played due to privacy settings. Please check Vimeo configuration.');
                }
            }

            // Expose player controls
            window.playVideo = function() {
                if (player && isReady) {
                    player.play().catch(function(error) {
                        console.error('Play error:', error);
                    });
                }
            };

            window.pauseVideo = function() {
                if (player && isReady) {
                    player.pause().catch(function(error) {
                        console.error('Pause error:', error);
                    });
                }
            };

            window.seekTo = function(seconds) {
                if (player && isReady) {
                    player.setCurrentTime(seconds).catch(function(error) {
                        console.error('Seek error:', error);
                    });
                }
            };

            window.setPlaybackRate = function(rate) {
                console.log('setPlaybackRate called with rate:', rate);
                if (player && isReady) {
                    console.log('Player is ready, attempting to set playback rate');
                    player.setPlaybackRate(rate).then(function() {
                        console.log('Playback rate set successfully to:', rate);
                    }).catch(function(error) {
                        console.error('Playback rate error:', error);
                    });
                } else {
                    console.log('Player not ready or not available. Player:', !!player, 'Ready:', isReady);
                }
            };
        </script>
    </body>
    </html>
    ''';
  }

  void _setupWebPlayerListeners() {
    // Enhanced JavaScript integration with better error handling
    if (_webViewController == null) return;

    _webViewController!.addJavaScriptChannel(
      'flutter_inappwebview',
      onMessageReceived: (JavaScriptMessage message) {
        // Handle messages from the web player
      },
    );
  }

  void _onDirectVideoPlayerUpdate() {
    if (_videoPlayerController != null) {
      final position = _videoPlayerController!.value.position.inSeconds;
      final duration = _videoPlayerController!.value.duration.inSeconds;
      final isPlaying = _videoPlayerController!.value.isPlaying;
      final isBuffering = _videoPlayerController!.value.isBuffering;

      setState(() {
        _currentPosition = position;
        _duration = duration;
        _isPlaying = isPlaying;
        _isBuffering = isBuffering;
      });

      widget.onProgress?.call(position);

      // Update progress every 5 seconds
      if (position - _lastProgressUpdate >= _progressUpdateInterval) {
        _updateVideoProgress();
        _lastProgressUpdate = position;
      }

      // Check for completion (80% watched)
      if (duration > 0 && position >= (duration * 0.8) && !_isCompleted) {
        _onVideoCompleted();
      }
    }
  }

  // Enhanced event handlers
  void _onVimeoPlay() {
    setState(() {
      _isPlaying = true;
      _isBuffering = false;
    });

    widget.onPlay?.call();
    _trackVideoProgress();
    _logVideoEvent('play');
  }

  void _onVimeoPause() {
    setState(() {
      _isPlaying = false;
    });

    widget.onPause?.call();
    _logVideoEvent('pause');
  }

  void _onVimeoTimeUpdate(int position) {
    setState(() {
      _currentPosition = position;
    });

    widget.onProgress?.call(position);

    // Update progress every 5 seconds
    if (position - _lastProgressUpdate >= _progressUpdateInterval) {
      _updateVideoProgress();
      _lastProgressUpdate = position;
    }

    // Check for completion (80% watched)
    if (_duration > 0 && position >= (_duration * 0.8) && !_isCompleted) {
      _onVideoCompleted();
    }

    // Check for streak qualification (50% watched)
    if (_duration > 0 && position >= (_duration * 0.5) && !_streakChecked) {
      _checkStreakQualification();
    }
  }

  void _onVimeoFinish() {
    _onVideoCompleted();
  }

  void _onVideoCompleted() {
    setState(() {
      _isCompleted = true;
      _isPlaying = false;
    });

    widget.onCompleted?.call();
    _updateVideoProgress(isCompleted: true);
    _logVideoEvent('complete');
  }

  Future<void> _checkStreakQualification() async {
    if (_streakChecked) return;

    setState(() {
      _streakChecked = true;
    });

    try {
      // Import the video streak service
      final VideoStreakService streakService = VideoStreakService();

      // Process video completion for streak tracking (50% completion)
      final result = await streakService.processVideoCompletion(
        widget.video.id,
        _currentPosition,
        widget.video.durationMinutes ?? 0,
      );

      if (result.message != null) {
        // Show motivational message for both new streaks and already earned streaks
        _showStreakMessage(result.message!, result.newStreak, result.highestStreak);

        // Notify parent about streak update for immediate UI refresh
        widget.onStreakUpdated?.call();
      }
    } catch (e) {
      debugPrint('Error checking streak qualification: $e');
    }
  }

  void _showStreakMessage(String message, int currentStreak, int highestStreak) {
    // Show a brief motivational message
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: const Duration(seconds: 3),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  // Wakelock methods to keep screen on during video playback
  Future<void> _enableWakelock() async {
    try {
      await WakelockPlus.enable();
      debugPrint('StableVimeoPlayer: Wakelock enabled - screen will stay on during video playback');
    } catch (e) {
      debugPrint('StableVimeoPlayer: Failed to enable wakelock: $e');
    }
  }

  Future<void> _disableWakelock() async {
    try {
      await WakelockPlus.disable();
      debugPrint('StableVimeoPlayer: Wakelock disabled - screen can turn off normally');
    } catch (e) {
      debugPrint('StableVimeoPlayer: Failed to disable wakelock: $e');
    }
  }

  void _onBufferStart() {
    setState(() {
      _isBuffering = true;
    });
  }

  void _onBufferEnd() {
    setState(() {
      _isBuffering = false;
    });
  }

  Future<void> _trackVideoProgress() async {
    // Enhanced progress tracking with better intervals
    while (_isPlaying && mounted) {
      await Future.delayed(const Duration(seconds: 5));
      if (_isPlaying && mounted) {
        await _updateVideoProgress();
      }
    }
  }

  Future<void> _updateVideoProgress({bool isCompleted = false}) async {
    try {
      final progressService = ProgressService();
      await progressService.updateVideoProgress(
        videoId: widget.video.id,
        courseId: widget.courseId,
        watchDurationSeconds: _currentPosition,
        lastPositionSeconds: _currentPosition,
        isCompleted: isCompleted,
        totalDurationSeconds: _duration,
      );
      debugPrint('✅ Video progress updated - Position: $_currentPosition, Completed: $isCompleted');
    } catch (e) {
      debugPrint('❌ Failed to update video progress: $e');
      // Don't rethrow to avoid disrupting playback
    }
  }

  Future<void> _logVideoEvent(String action) async {
    try {
      final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);
      if (vimeoId != null) {
        await VideoSecurityHelper.logVideoAccess(
          vimeoId: vimeoId,
          videoId: widget.video.id,
          userId: await _getCurrentUserId(),
          action: action,
        );
      }
    } catch (e) {
      debugPrint('Failed to log video event: $e');
    }
  }

  void _handleError(String message) {
    setState(() {
      _hasError = true;
      _errorMessage = message;
      _isLoading = false;
      _isBuffering = false;
    });
    widget.onError?.call(message);
  }

  void _togglePlayPause() {
    if (_isPlaying) {
      _pauseVideo();
    } else {
      _playVideo();
    }
  }

  void _playVideo() {
    switch (_selectedPlayerType) {
      case PlayerType.webview:
        if (_webViewController != null) {
          _webViewController!.runJavaScript('window.playVideo()');
        }
        break;
      case PlayerType.directVideo:
        _videoPlayerController?.play();
        break;
      case PlayerType.vimeoWidget:
        // Handled by VimeoVideoPlayer widget
        break;
    }
  }

  void _pauseVideo() {
    switch (_selectedPlayerType) {
      case PlayerType.webview:
        if (_webViewController != null) {
          _webViewController!.runJavaScript('window.pauseVideo()');
        }
        break;
      case PlayerType.directVideo:
        _videoPlayerController?.pause();
        break;
      case PlayerType.vimeoWidget:
        // Handled by VimeoVideoPlayer widget
        break;
    }
  }

  void _seekTo(int seconds) {
    switch (_selectedPlayerType) {
      case PlayerType.webview:
        if (_webViewController != null) {
          _webViewController!.runJavaScript('window.seekTo($seconds)');
        }
        break;
      case PlayerType.directVideo:
        _videoPlayerController?.seekTo(Duration(seconds: seconds));
        break;
      case PlayerType.vimeoWidget:
        // Seeking would need to be implemented differently
        break;
    }
  }

  void _changePlaybackSpeed(double speed) {
    setState(() {
      _currentPlaybackSpeed = speed;
    });

    debugPrint('StableVimeoPlayer: Changing playback speed to: ${speed}x');
    debugPrint('StableVimeoPlayer: Current player type: $_selectedPlayerType');

    switch (_selectedPlayerType) {
      case PlayerType.webview:
        debugPrint('StableVimeoPlayer: Using WebView player for speed change');
        if (_webViewController != null) {
          _webViewController!.runJavaScript('''
          console.log('Attempting to change playback speed to $speed');
          console.log('Available functions:', Object.keys(window));
          if (window.setPlaybackRate) {
            console.log('Using window.setPlaybackRate');
            window.setPlaybackRate($speed);
          } else if (window.vimeoPlayer && window.vimeoPlayer.setPlaybackRate) {
            console.log('Using window.vimeoPlayer.setPlaybackRate');
            window.vimeoPlayer.setPlaybackRate($speed).then(function() {
              console.log('Playback speed changed successfully to $speed');
            }).catch(function(error) {
              console.log('Speed change failed:', error);
            });
          } else if (window.player && window.player.setPlaybackRate) {
            console.log('Using window.player.setPlaybackRate');
            window.player.setPlaybackRate($speed).then(function() {
              console.log('Playback speed changed successfully to $speed');
            }).catch(function(error) {
              console.log('Speed change failed:', error);
            });
          } else {
            console.log('No playback rate function available');
            console.log('window.setPlaybackRate:', typeof window.setPlaybackRate);
            console.log('window.vimeoPlayer:', typeof window.vimeoPlayer);
            console.log('window.player:', typeof window.player);
          }
        ''');
        }
        break;
      case PlayerType.directVideo:
        debugPrint('StableVimeoPlayer: Using DirectVideo player for speed change');
        // Chewie handles playback speed changes
        if (_videoPlayerController != null && _chewieController != null) {
          debugPrint('StableVimeoPlayer: DirectVideo speed change - controller available');
          // Note: Flutter video_player doesn't support playback speed on all platforms
        } else {
          debugPrint('StableVimeoPlayer: DirectVideo speed change - no controller available');
        }
        break;
      case PlayerType.vimeoWidget:
        debugPrint('StableVimeoPlayer: Using VimeoWidget player for speed change');
        // VimeoVideoPlayer widget doesn't support playback speed changes directly
        debugPrint('VimeoWidget playback speed change not supported - showing user feedback');

        // Show user feedback
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Playback speed changed to ${speed}x'),
              duration: const Duration(seconds: 2),
              backgroundColor: Colors.black87,
            ),
          );
        }
        break;
    }
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });

    if (_isFullscreen) {
      // Enter fullscreen
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    } else {
      // Exit fullscreen
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }

    debugPrint('StableVimeoPlayer: Fullscreen toggled - $_isFullscreen');
  }

  void _showControlsTemporarily() {
    if (widget.showControls) {
      setState(() {
        _showControls = true;
      });
      _controlsAnimationController.forward();

      // Hide controls after 3 seconds
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted && _isPlaying) {
          setState(() {
            _showControls = false;
          });
          _controlsAnimationController.reverse();
        }
      });
    }
  }

  // Netflix-style skip controls
  void _skipForward() {
    final newPosition = (_currentPosition + 10).clamp(0, _duration);
    _seekTo(newPosition);
    _showSkipIndicator(true);
  }

  void _skipBackward() {
    final newPosition = (_currentPosition - 10).clamp(0, _duration);
    _seekTo(newPosition);
    _showSkipIndicator(false);
  }

  void _showSkipIndicator(bool isForward) {
    _skipIndicatorTimer?.cancel();

    setState(() {
      if (isForward) {
        _showSkipForward = true;
        _showSkipBackward = false;
      } else {
        _showSkipBackward = true;
        _showSkipForward = false;
      }
    });

    // Hide indicator after 1 second
    _skipIndicatorTimer = Timer(const Duration(milliseconds: 1000), () {
      if (mounted) {
        setState(() {
          _showSkipForward = false;
          _showSkipBackward = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Use the simple Vimeo player with default controls only
    return SimpleVimeoPlayer(
      video: widget.video,
      autoPlay: widget.autoPlay,
      onReady: widget.onReady,
      onPlay: widget.onPlay,
      onPause: widget.onPause,
      onCompleted: widget.onCompleted,
      onProgress: widget.onProgress,
      onError: widget.onError,
    );
  }

  Widget _buildPlayerWidget() {
    switch (_selectedPlayerType) {
      case PlayerType.webview:
        if (_webViewController != null) {
          return WebViewWidget(
            controller: _webViewController!,
            gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
              Factory<TapGestureRecognizer>(() => TapGestureRecognizer()),
              Factory<LongPressGestureRecognizer>(() => LongPressGestureRecognizer()),
              Factory<VerticalDragGestureRecognizer>(() => VerticalDragGestureRecognizer()),
              Factory<HorizontalDragGestureRecognizer>(() => HorizontalDragGestureRecognizer()),
            },
          );
        } else {
          return Container(
            color: Colors.black,
            child: const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
          );
        }

      case PlayerType.directVideo:
        if (_chewieController != null) {
          return Chewie(controller: _chewieController!);
        }
        return _buildErrorWidget();

      case PlayerType.vimeoWidget:
        final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);
        if (vimeoId != null) {
          return VimeoVideoPlayer(
            videoId: vimeoId,
            isAutoPlay: widget.autoPlay,
            showControls: true, // Use Vimeo's default controls
            showTitle: false,
            showByline: false,
            isMuted: false,
            isLooping: false,
            enableDNT: true,
            backgroundColor: Colors.black,
            onReady: () {
              debugPrint('Stable Vimeo player ready');
              widget.onReady?.call();
            },
            onPlay: _onVimeoPlay,
            onPause: _onVimeoPause,
            onFinish: _onVimeoFinish,
          );
        }
        return _buildErrorWidget();
    }
  }

  Widget _buildErrorWidget() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              'Video Unavailable',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage.isNotEmpty
                    ? _errorMessage
                    : 'This video cannot be played. Please check your connection and try again.',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white70,
                ),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _hasError = false;
                  _isLoading = true;
                });
                _initializePlayer();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            RotationTransition(
              turns: _loadingRotation,
              child: const Icon(
                Icons.refresh,
                color: Colors.white,
                size: 48,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Loading video...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            if (widget.video.title.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                widget.video.title,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            const SizedBox(height: 16),
            const Text(
              'Verifying domain access...',
              style: TextStyle(
                color: Colors.white54,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBufferingIndicator() {
    return Container(
      color: Colors.black54,
      child: const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
          strokeWidth: 3,
        ),
      ),
    );
  }

  Widget _buildYouTubeStyleControls() {
    return AnimatedBuilder(
      animation: _controlsOpacity,
      builder: (context, child) {
        return Opacity(
          opacity: _controlsOpacity.value,
          child: Stack(
            children: [
              // Top gradient overlay
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                height: 120,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withOpacity(0.7),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),

              // Top controls
              _buildTopControls(),

              // Center play/pause button
              Center(
                child: _buildCenterPlayButton(),
              ),

              // Bottom controls (positioned at bottom edge)
              _buildBottomControls(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTopControls() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Faded minimal back button
            Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Icon(
                    Icons.arrow_back,
                    color: Colors.white.withOpacity(0.7),
                    size: 24,
                  ),
                ),
              ),
            ),

            // Spacer to push buttons to the right
            const Spacer(),

            // Settings button
            if (widget.enableQualitySelection || widget.playbackSpeeds.length > 1)
              Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  onTap: _showSettingsMenu,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    child: Icon(
                      Icons.settings,
                      color: Colors.white.withOpacity(0.8),
                      size: 22,
                    ),
                  ),
                ),
              ),

            // Fullscreen button
            if (widget.enableFullscreen)
              Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  onTap: _toggleFullscreen,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    child: Icon(
                      _isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
                      color: Colors.white.withOpacity(0.8),
                      size: 22,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCenterPlayButton() {
    return GestureDetector(
      onTap: () {
        _togglePlayPause();
        _showControlsTemporarily();
      },
      child: Container(
        width: 70,
        height: 70,
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.6),
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Icon(
          _isPlaying ? Icons.pause : Icons.play_arrow,
          color: Colors.white,
          size: 32,
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withOpacity(0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Speed control positioned on right side
              if (widget.playbackSpeeds.length > 1)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // Playback speed (right corner)
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.2),
                            width: 1,
                          ),
                        ),
                        child: TextButton(
                          onPressed: _showSpeedMenu,
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            minimumSize: Size.zero,
                          ),
                          child: Text(
                            '${_currentPlaybackSpeed}x',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              shadows: [
                                Shadow(
                                  color: Colors.black,
                                  offset: Offset(1, 1),
                                  blurRadius: 2,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              // Professional seek bar positioned at bottom edge
              _buildProfessionalSeekBar(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfessionalSeekBar() {
    return Container(
      height: 48, // Minimum touch target height for accessibility
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Background track with shadow
          Container(
            height: 4,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(2),
              color: Colors.white.withOpacity(0.3),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  offset: const Offset(0, 2),
                  blurRadius: 2,
                ),
              ],
            ),
          ),

          // Professional slider with custom styling
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              // Track styling
              activeTrackColor: Colors.transparent,
              inactiveTrackColor: Colors.transparent,

              // Thumb styling - responsive to interaction
              thumbColor: Colors.white,
              thumbShape: _ProfessionalSliderThumbShape(
                enabledThumbRadius: _isDragging ? 8.0 : 6.0,
                isActive: _isDragging,
              ),

              // Overlay for touch feedback
              overlayColor: Colors.white.withOpacity(0.2),
              overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),

              // Track height
              trackHeight: 4,
            ),
            child: Slider(
              value: _duration > 0 ? _currentPosition.toDouble() : 0.0,
              max: _duration > 0 ? _duration.toDouble() : 1.0,
              onChangeStart: (value) {
                setState(() {
                  _isDragging = true;
                });
              },
              onChanged: (value) {
                if (!_isDragging) return; // Only update during dragging
                setState(() {
                  _currentPosition = value.toInt();
                });
              },
              onChangeEnd: (value) {
                setState(() {
                  _isDragging = false;
                });
                _seekTo(value.toInt()); // Seek when dragging ends
              },
            ),
          ),

          // Custom progress track with gradient
          Positioned.fill(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24), // Account for thumb
              child: Align(
                alignment: Alignment.centerLeft,
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    final availableWidth = constraints.maxWidth;
                    final progressWidth = _duration > 0
                        ? availableWidth * (_currentPosition / _duration)
                        : 0.0;

                    return AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      height: 4,
                      width: progressWidth.clamp(0.0, availableWidth),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(2),
                        gradient: LinearGradient(
                          colors: [
                            Theme.of(context).colorScheme.primary,
                            Theme.of(context).colorScheme.primary.withOpacity(0.8),
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                            offset: const Offset(0, 1),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Legacy method kept for compatibility with progress indicator
  Widget _buildProgressBar() {
    return _buildProfessionalSeekBar();
  }

  Widget _buildProgressIndicator() {
    if (_duration <= 0) return const SizedBox.shrink();

    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: LinearProgressIndicator(
        value: _currentPosition / _duration,
        backgroundColor: Colors.white.withOpacity(0.3),
        valueColor: const AlwaysStoppedAnimation<Color>(Colors.red),
        minHeight: 2,
      ),
    );
  }

  void _showSettingsMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black87,
      builder: (context) => _buildSettingsMenu(),
    );
  }

  Widget _buildSettingsMenu() {
    return SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              'Video Settings',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          if (widget.playbackSpeeds.length > 1) ...[
            const Divider(color: Colors.white24),
            _buildSpeedSettings(),
          ],

          if (widget.enableQualitySelection) ...[
            const Divider(color: Colors.white24),
            _buildQualitySettings(),
          ],

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildSpeedSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Playback Speed',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.w500),
          ),
        ),
        ...widget.playbackSpeeds.map((speed) => ListTile(
          title: Text(
            '${speed}x',
            style: const TextStyle(color: Colors.white),
          ),
          trailing: _currentPlaybackSpeed == speed
              ? const Icon(Icons.check, color: Colors.red)
              : null,
          onTap: () {
            _changePlaybackSpeed(speed);
            Navigator.pop(context);
          },
        )),
      ],
    );
  }

  Widget _buildQualitySettings() {
    final qualities = ['Auto', '1080p', '720p', '480p', '360p'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Video Quality',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.w500),
          ),
        ),
        ...qualities.map((quality) => ListTile(
          title: Text(
            quality,
            style: const TextStyle(color: Colors.white),
          ),
          onTap: () {
            Navigator.pop(context);
          },
        )),
      ],
    );
  }

  void _showSpeedMenu() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black87,
        title: const Text(
          'Playback Speed',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: widget.playbackSpeeds.map((speed) => ListTile(
            title: Text(
              '${speed}x',
              style: const TextStyle(color: Colors.white),
            ),
            trailing: _currentPlaybackSpeed == speed
                ? const Icon(Icons.check, color: Colors.red)
                : null,
            onTap: () {
              _changePlaybackSpeed(speed);
              Navigator.pop(context);
            },
          )).toList(),
        ),
      ),
    );
  }

  Widget _buildInteractiveTouchLayer() {
    return Positioned(
      top: 80, // Exclude top 80px for top controls (back, settings, fullscreen)
      left: 0,
      right: 0,
      bottom: 120, // Exclude bottom 120px for controls (speed button + seek bar)
      child: GestureDetector(
        onTap: () {
          // Show controls when tapping anywhere (if hidden)
          if (!_showControls) {
            _showControlsTemporarily();
          }
        },
        child: Row(
          children: [
            // Left side - Skip backward (30% of screen)
            Expanded(
              flex: 3,
              child: GestureDetector(
                onTap: () {
                  _skipBackward();
                  _showControlsTemporarily(); // Keep controls visible
                },
                behavior: HitTestBehavior.translucent,
                child: Container(
                  color: Colors.transparent,
                  width: double.infinity,
                  height: double.infinity,
                ),
              ),
            ),

            // Center area - Only show controls, no play/pause (40% of screen)
            Expanded(
              flex: 4,
              child: GestureDetector(
                onTap: () {
                  // Only show controls, don't toggle play/pause
                  // Play/pause is handled by the center button itself
                  _showControlsTemporarily();
                },
                behavior: HitTestBehavior.translucent,
                child: Container(
                  color: Colors.transparent,
                  width: double.infinity,
                  height: double.infinity,
                ),
              ),
            ),

            // Right side - Skip forward (30% of screen)
            Expanded(
              flex: 3,
              child: GestureDetector(
                onTap: () {
                  _skipForward();
                  _showControlsTemporarily(); // Keep controls visible
                },
                behavior: HitTestBehavior.translucent,
                child: Container(
                  color: Colors.transparent,
                  width: double.infinity,
                  height: double.infinity,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSkipIndicator(bool isForward) {
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOutBack,
      left: isForward ? null : 50,
      right: isForward ? 50 : null,
      top: 0,
      bottom: 0,
      child: Center(
        child: AnimatedScale(
          scale: 1.0,
          duration: const Duration(milliseconds: 200),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.8),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  isForward ? Icons.fast_forward : Icons.fast_rewind,
                  color: Colors.white,
                  size: 32,
                ),
                const SizedBox(height: 4),
                Text(
                  '10',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'seconds',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatDuration(int seconds) {
    final duration = Duration(seconds: seconds);
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final secs = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    }
  }
}

// Enum for different player types
enum PlayerType {
  webview,
  directVideo,
  vimeoWidget,
}

/// Custom slider thumb shape for professional video player seek bar
class _ProfessionalSliderThumbShape extends SliderComponentShape {
  final double enabledThumbRadius;
  final bool isActive;

  const _ProfessionalSliderThumbShape({
    required this.enabledThumbRadius,
    this.isActive = false,
  });

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.fromRadius(enabledThumbRadius);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final Canvas canvas = context.canvas;

    // Calculate radius with animation
    final double radius = enabledThumbRadius * enableAnimation.value;

    // Enhanced shadow for active state
    if (isActive) {
      // Outer glow effect
      final Paint glowPaint = Paint()
        ..color = Colors.white.withOpacity(0.3)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);
      canvas.drawCircle(center, radius + 4, glowPaint);
    }

    // Main shadow
    final Paint shadowPaint = Paint()
      ..color = Colors.black.withOpacity(isActive ? 0.4 : 0.2)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, isActive ? 4.0 : 2.0);
    canvas.drawCircle(
      center + Offset(0, isActive ? 2 : 1),
      radius,
      shadowPaint,
    );

    // Main thumb circle
    final Paint thumbPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, radius, thumbPaint);

    // Inner highlight for depth
    final Paint highlightPaint = Paint()
      ..color = Colors.white.withOpacity(0.8)
      ..style = PaintingStyle.fill;
    canvas.drawCircle(
      center - Offset(radius * 0.3, radius * 0.3),
      radius * 0.3,
      highlightPaint,
    );

    // Border for definition
    final Paint borderPaint = Paint()
      ..color = Colors.grey.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;
    canvas.drawCircle(center, radius, borderPaint);
  }
}
