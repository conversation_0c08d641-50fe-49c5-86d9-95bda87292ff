import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/simple_pwa_service.dart';
import '../design_system/kft_design_system.dart';

/// Mobile-specific PWA install dialog with platform-specific instructions
/// Provides step-by-step guidance for iOS Safari, Android Chrome, etc.
class MobilePWADialog extends StatefulWidget {
  final VoidCallback? onDismiss;

  const MobilePWADialog({
    Key? key,
    this.onDismiss,
  }) : super(key: key);

  /// Show the mobile PWA install dialog
  static Future<void> show(
    BuildContext context, {
    VoidCallback? onDismiss,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (context) => MobilePWADialog(
        onDismiss: onDismiss,
      ),
    );
  }

  @override
  State<MobilePWADialog> createState() => _MobilePWADialogState();
}

class _MobilePWADialogState extends State<MobilePWADialog>
    with SingleTickerProviderStateMixin {
  final SimplePWAService _pwaService = SimplePWAService();
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleDismiss() {
    _pwaService.markDialogShown();
    Navigator.of(context).pop();
    widget.onDismiss?.call();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Dialog(
      backgroundColor: Colors.transparent,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 380),
          margin: const EdgeInsets.all(KFTDesignSystem.spacingLg),
          decoration: BoxDecoration(
            color: KFTDesignSystem.getSurfaceColor(context),
            borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusLg),
            boxShadow: [
              BoxShadow(
                color: isDarkMode 
                    ? Colors.black.withOpacity(0.4)
                    : Colors.black.withOpacity(0.15),
                blurRadius: 28,
                offset: const Offset(0, 14),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(theme, isDarkMode),
              _buildInstructions(theme, isDarkMode),
              _buildActions(theme, isDarkMode),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(KFTDesignSystem.spacingLg),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            KFTDesignSystem.primaryColor,
            KFTDesignSystem.primaryColor.withOpacity(0.8),
          ],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(KFTDesignSystem.borderRadiusLg),
          topRight: Radius.circular(KFTDesignSystem.borderRadiusLg),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(KFTDesignSystem.spacingMd),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusMd),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Icon(
              _pwaService.isIOS ? Icons.ios_share : Icons.add_to_home_screen,
              color: Colors.white,
              size: 28,
            ),
          ),
          const SizedBox(width: KFTDesignSystem.spacingMd),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Install KFT Fitness',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                    letterSpacing: -0.5,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  _pwaService.isIOS 
                      ? 'Add to your home screen'
                      : 'Install as an app',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions(ThemeData theme, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.all(KFTDesignSystem.spacingLg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_pwaService.isIOS) ..._buildIOSInstructions(theme),
          if (_pwaService.isAndroid) ..._buildAndroidInstructions(theme),
          if (!_pwaService.isIOS && !_pwaService.isAndroid) ..._buildGenericInstructions(theme),
        ],
      ),
    );
  }

  List<Widget> _buildIOSInstructions(ThemeData theme) {
    return [
      Text(
        'To install this app on your iPhone/iPad:',
        style: theme.textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w600,
          color: KFTDesignSystem.getTextPrimaryColor(context),
        ),
      ),
      const SizedBox(height: KFTDesignSystem.spacingMd),
      _buildStep(theme, '1', 'Tap the Share button', Icons.ios_share),
      const SizedBox(height: KFTDesignSystem.spacingMd),
      _buildStep(theme, '2', 'Scroll down and tap "Add to Home Screen"', Icons.add_to_home_screen),
      const SizedBox(height: KFTDesignSystem.spacingMd),
      _buildStep(theme, '3', 'Tap "Add" to install', Icons.check_circle_outline),
    ];
  }

  List<Widget> _buildAndroidInstructions(ThemeData theme) {
    return [
      Text(
        'To install this app on your Android device:',
        style: theme.textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w600,
          color: KFTDesignSystem.getTextPrimaryColor(context),
        ),
      ),
      const SizedBox(height: KFTDesignSystem.spacingMd),
      _buildStep(theme, '1', 'Tap the menu (⋮) in your browser', Icons.more_vert),
      const SizedBox(height: KFTDesignSystem.spacingMd),
      _buildStep(theme, '2', 'Select "Add to Home screen" or "Install app"', Icons.add_to_home_screen),
      const SizedBox(height: KFTDesignSystem.spacingMd),
      _buildStep(theme, '3', 'Tap "Install" to confirm', Icons.check_circle_outline),
    ];
  }

  List<Widget> _buildGenericInstructions(ThemeData theme) {
    return [
      Text(
        'To install this app:',
        style: theme.textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w600,
          color: KFTDesignSystem.getTextPrimaryColor(context),
        ),
      ),
      const SizedBox(height: KFTDesignSystem.spacingMd),
      _buildStep(theme, '1', 'Look for an "Install" or "Add to Home Screen" option in your browser menu', Icons.add_to_home_screen),
      const SizedBox(height: KFTDesignSystem.spacingMd),
      _buildStep(theme, '2', 'Follow your browser\'s installation prompts', Icons.check_circle_outline),
    ];
  }

  Widget _buildStep(ThemeData theme, String number, String text, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 28,
          height: 28,
          decoration: BoxDecoration(
            color: KFTDesignSystem.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(14),
            border: Border.all(
              color: KFTDesignSystem.primaryColor.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Center(
            child: Text(
              number,
              style: theme.textTheme.bodySmall?.copyWith(
                color: KFTDesignSystem.primaryColor,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ),
        const SizedBox(width: KFTDesignSystem.spacingMd),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    icon,
                    size: 18,
                    color: KFTDesignSystem.getTextSecondaryColor(context),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      text,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: KFTDesignSystem.getTextPrimaryColor(context),
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActions(ThemeData theme, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(KFTDesignSystem.spacingLg),
      decoration: BoxDecoration(
        color: KFTDesignSystem.getBackgroundColor(context).withOpacity(0.5),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(KFTDesignSystem.borderRadiusLg),
          bottomRight: Radius.circular(KFTDesignSystem.borderRadiusLg),
        ),
      ),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: _handleDismiss,
          style: ElevatedButton.styleFrom(
            backgroundColor: KFTDesignSystem.primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            elevation: 2,
            shadowColor: KFTDesignSystem.primaryColor.withOpacity(0.3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusSm),
            ),
          ),
          child: Text(
            'Got it!',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }
}
