import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/optional_pwa_service.dart';
import 'optional_pwa_install_widget.dart';

/// Optional PWA prompt wrapper that respects user preferences
/// Only shows prompts when enabled and doesn't interfere with full screen
class OptionalPWAPrompt extends StatefulWidget {
  final Widget child;
  final bool showInitialPrompt;
  final bool showFloatingButton;

  const OptionalPWAPrompt({
    Key? key,
    required this.child,
    this.showInitialPrompt = true,
    this.showFloatingButton = false,
  }) : super(key: key);

  @override
  State<OptionalPWAPrompt> createState() => _OptionalPWAPromptState();
}

class _OptionalPWAPromptState extends State<OptionalPWAPrompt> {
  final OptionalPWAService _pwaService = OptionalPWAService();
  bool _hasShownInitialPrompt = false;
  bool _showFloatingButton = false;

  @override
  void initState() {
    super.initState();
    if (kIsWeb) {
      _initializePWA();
    }
  }

  Future<void> _initializePWA() async {
    await _pwaService.initialize();
    
    // Listen for installability changes
    _pwaService.installabilityStream.listen((isInstallable) {
      if (mounted && isInstallable && widget.showInitialPrompt) {
        _checkAndShowInitialPrompt();
      }
      
      if (mounted && widget.showFloatingButton) {
        setState(() {
          _showFloatingButton = isInstallable && _pwaService.enabled;
        });
      }
    });

    // Listen for enabled state changes
    _pwaService.isEnabled.addListener(() {
      if (mounted && widget.showFloatingButton) {
        setState(() {
          _showFloatingButton = _pwaService.isInstallable && _pwaService.enabled;
        });
      }
    });

    // Check if we should show initial prompt
    if (widget.showInitialPrompt) {
      _checkAndShowInitialPrompt();
    }

    // Set floating button state
    if (widget.showFloatingButton) {
      setState(() {
        _showFloatingButton = _pwaService.isInstallable && _pwaService.enabled;
      });
    }
  }

  void _checkAndShowInitialPrompt() {
    if (_hasShownInitialPrompt || !_pwaService.shouldShowInitialPrompt()) {
      return;
    }

    // Show prompt after a delay to ensure UI is ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_hasShownInitialPrompt) {
        _showInitialPromptDialog();
      }
    });
  }

  void _showInitialPromptDialog() {
    if (!mounted || _hasShownInitialPrompt) return;

    _hasShownInitialPrompt = true;
    _pwaService.markInitialPromptShown();

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: OptionalPWAInstallWidget(
          displayType: PWADisplayType.card,
          onInstallSuccess: () {
            if (mounted) {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('App installation started!'),
                  duration: Duration(seconds: 2),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }
          },
          onDismiss: () {
            if (mounted) {
              Navigator.of(context).pop();
            }
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: widget.child,
      floatingActionButton: _showFloatingButton
          ? OptionalPWAInstallWidget(
              displayType: PWADisplayType.floatingButton,
              onInstallSuccess: () {
                setState(() {
                  _showFloatingButton = false;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('App installation started!'),
                    duration: Duration(seconds: 2),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
            )
          : null,
    );
  }
}

/// Extension to easily add PWA install widget to any page
extension PWAInstallExtension on Widget {
  /// Wrap widget with optional PWA prompt
  Widget withOptionalPWA({
    bool showInitialPrompt = true,
    bool showFloatingButton = false,
  }) {
    return OptionalPWAPrompt(
      showInitialPrompt: showInitialPrompt,
      showFloatingButton: showFloatingButton,
      child: this,
    );
  }
}

/// PWA install card that can be embedded anywhere
class PWAInstallCard extends StatelessWidget {
  final VoidCallback? onInstallSuccess;
  final VoidCallback? onDismiss;

  const PWAInstallCard({
    Key? key,
    this.onInstallSuccess,
    this.onDismiss,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!kIsWeb) return const SizedBox.shrink();

    return OptionalPWAInstallWidget(
      displayType: PWADisplayType.card,
      onInstallSuccess: onInstallSuccess,
      onDismiss: onDismiss,
    );
  }
}

/// PWA install list tile that can be embedded in settings
class PWAInstallListTile extends StatelessWidget {
  final VoidCallback? onInstallSuccess;

  const PWAInstallListTile({
    Key? key,
    this.onInstallSuccess,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!kIsWeb) return const SizedBox.shrink();

    return OptionalPWAInstallWidget(
      displayType: PWADisplayType.listTile,
      onInstallSuccess: onInstallSuccess,
    );
  }
}
