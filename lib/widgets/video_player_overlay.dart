import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/course_video.dart';
import '../models/course.dart';
import '../services/api_service.dart';
import '../services/course_tracking_service.dart';
import '../design_system/kft_design_system.dart';
import '../widgets/official_vimeo_player.dart';
import '../utils/video_security_helper.dart';
// Removed global notification manager import
import '../widgets/custom_vimeo_player.dart';
import '../services/progress_service.dart';

class VideoPlayerOverlay extends StatefulWidget {
  final CourseVideo video;
  final Course? course;
  final int? courseId;
  final Function(CourseVideo)? onVideoCompleted;
  final Function(String)? onError;
  final bool showControls;
  final bool isFullScreen;
  final Function? onClose;

  const VideoPlayerOverlay({
    Key? key,
    required this.video,
    this.course,
    this.courseId,
    this.onVideoCompleted,
    this.onError,
    this.showControls = true,
    this.isFullScreen = false,
    this.onClose,
  }) : super(key: key);

  @override
  _VideoPlayerOverlayState createState() => _VideoPlayerOverlayState();
}

class _VideoPlayerOverlayState extends State<VideoPlayerOverlay> with SingleTickerProviderStateMixin {
  final ApiService _apiService = ApiService();
  final CourseTrackingService _trackingService = CourseTrackingService();

  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  bool _isCompleted = false;
  bool _isLoading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();

    // Removed notification manager call

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
      parent: _animationController,
        curve: Curves.easeOut,
      ),
    );

    _animationController.forward();

    // Track video progress
    _trackVideoProgress();

    // Track this video as the last accessed video
    _trackVideoAccess();
  }

  Future<void> _trackVideoProgress() async {
    // Wait a few seconds to simulate initial watching
    await Future.delayed(const Duration(seconds: 3));

    if (!mounted) return;

    try {
      await _apiService.updateVideoProgress(
        videoId: widget.video.id,
        watchDurationSeconds: 30, // Simulate watching 30 seconds
        lastPositionSeconds: 30,
      );
    } catch (e) {
      // Do NOT show error messages or clear tokens for video progress tracking failures
      // This prevents disrupting the user experience during video playback
      debugPrint('Failed to track video progress: $e');
    }
  }

  Future<void> _trackVideoAccess() async {
    try {
      if (widget.course != null) {
        await _trackingService.saveLastOpenedCourse(widget.course!);
      }
      await _trackingService.saveLastAccessedVideo(widget.video);
    } catch (e) {
      print('Error tracking video access: $e');
    }
  }

  @override
  void dispose() {
    // Removed notification manager call

    _animationController.dispose();
    super.dispose();
  }

  Future<void> _closeOverlay() async {
    await _animationController.reverse();
    if (mounted) {
      if (widget.onClose != null) {
        widget.onClose!();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
        builder: (context, child) {
          return Material(
          color: Colors.black.withOpacity(0.9 * _opacityAnimation.value),
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: child,
          ),
          );
        },
      child: Stack(
        children: [
          Positioned.fill(
            child: GestureDetector(
              onTap: _closeOverlay,
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
          Positioned.fill(
        child: SafeArea(
          child: Column(
            children: [
              // Header with close button
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        widget.video.title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white),
                      onPressed: _closeOverlay,
                    ),
                  ],
                ),
              ),

              // Video player
              Expanded(
                child: Center(
                  child: _buildPlayerWidget(),
                ),
              ),
            ],
          ),
        ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlayerWidget() {
    return Stack(
      children: [
        // Video player
        widget.video.videoProvider == 'vimeo'
            ? AspectRatio(
                aspectRatio: 16 / 9,
                child: OfficialVimeoPlayer(
                  video: widget.video,
                  onProgress: (position) {
                    // Update video progress in real-time
                    _updateVideoProgressSecure(position);
                  },
                  onCompleted: () {
                    // Mark video as completed
                    _markAsCompleted();

                    // Log completion for analytics
                    VideoSecurityHelper.logVideoAccess(
                      vimeoId: VideoSecurityHelper.extractVimeoId(widget.video.videoUrl) ?? '',
                      videoId: widget.video.id,
                      userId: 1, // Get from auth service
                      action: 'complete',
                    );
                  },
                  onPlay: () {
                    debugPrint('Official Vimeo player overlay: Video started playing');
                  },
                  onPause: () {
                    debugPrint('Official Vimeo player overlay: Video paused');
                  },
                  onError: (error) {
                    setState(() {
                      _errorMessage = error.toString();
                    });
                  },
                ),
              )
            : const Center(
                child: Text(
                  'Unable to load video player',
                  style: TextStyle(color: Colors.white),
                ),
              ),
      ],
    );
  }

  Future<void> _updateVideoProgressSecure(int position) async {
    try {
      final progressService = ProgressService();
      await progressService.updateVideoProgress(
        videoId: widget.video.id,
        courseId: widget.courseId,
        watchDurationSeconds: position,
        lastPositionSeconds: position,
        totalDurationSeconds: widget.video.durationMinutes != null ? widget.video.durationMinutes! * 60 : null,
      );
      debugPrint('✅ Video progress updated - Position: $position');
    } catch (e) {
      debugPrint('❌ Failed to update video progress: $e');
      // Don't rethrow to avoid disrupting playback
    }
  }

  Future<void> _markAsCompleted() async {
    if (_isCompleted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final progressService = ProgressService();
      final durationSeconds = widget.video.durationMinutes != null ? widget.video.durationMinutes! * 60 : 0;
      await progressService.updateVideoProgress(
        videoId: widget.video.id,
        courseId: widget.courseId,
        isCompleted: true,
        totalDurationSeconds: durationSeconds,
        watchDurationSeconds: durationSeconds,
      );

      setState(() {
        _isCompleted = true;
        _isLoading = false;
      });

      debugPrint('✅ Video marked as completed');
    } catch (e) {
      setState(() {
        _errorMessage = e.toString().replaceAll('Exception: ', '');
        _isLoading = false;
      });
      debugPrint('❌ Failed to mark video as completed: $e');
    }
  }
}
