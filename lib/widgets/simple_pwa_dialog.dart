import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/simple_pwa_service.dart';
import '../design_system/kft_design_system.dart';
import 'mobile_pwa_dialog.dart';

/// Simple, professional PWA install dialog
/// Shows a clean, professional prompt for PWA installation
class SimplePWADialog extends StatefulWidget {
  final VoidCallback? onInstallSuccess;
  final VoidCallback? onDismiss;

  const SimplePWADialog({
    Key? key,
    this.onInstallSuccess,
    this.onDismiss,
  }) : super(key: key);

  /// Show the simple PWA install dialog
  static Future<void> show(
    BuildContext context, {
    VoidCallback? onInstallSuccess,
    VoidCallback? onDismiss,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (context) => SimplePWADialog(
        onInstallSuccess: onInstallSuccess,
        onDismiss: onDismiss,
      ),
    );
  }

  @override
  State<SimplePWADialog> createState() => _SimplePWADialogState();
}

class _SimplePWADialogState extends State<SimplePWADialog>
    with SingleTickerProviderStateMixin {
  final SimplePWAService _pwaService = SimplePWAService();
  bool _isInstalling = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _handleInstall() async {
    if (_isInstalling) return;

    setState(() {
      _isInstalling = true;
    });

    try {
      final success = await _pwaService.promptInstall();

      if (success) {
        // Brief delay for user feedback
        await Future.delayed(const Duration(milliseconds: 300));

        if (mounted) {
          Navigator.of(context).pop();
          widget.onInstallSuccess?.call();
        }
      } else {
        // If native prompt failed, check if we should show mobile instructions
        if (_pwaService.isMobile && mounted) {
          Navigator.of(context).pop();
          // Show mobile-specific dialog
          MobilePWADialog.show(
            context,
            onDismiss: widget.onDismiss,
          );
        } else {
          setState(() {
            _isInstalling = false;
          });
        }
      }
    } catch (e) {
      debugPrint('❌ Error during PWA installation: $e');
      setState(() {
        _isInstalling = false;
      });
    }
  }

  void _handleDismiss() {
    _pwaService.markPromptShown();
    Navigator.of(context).pop();
    widget.onDismiss?.call();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Dialog(
      backgroundColor: Colors.transparent,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 420),
          margin: const EdgeInsets.all(KFTDesignSystem.spacingLg),
          decoration: BoxDecoration(
            color: KFTDesignSystem.getSurfaceColor(context),
            borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusLg),
            boxShadow: [
              BoxShadow(
                color: isDarkMode
                    ? Colors.black.withOpacity(0.3)
                    : Colors.black.withOpacity(0.1),
                blurRadius: 24,
                offset: const Offset(0, 12),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: isDarkMode
                    ? Colors.black.withOpacity(0.2)
                    : Colors.black.withOpacity(0.05),
                blurRadius: 6,
                offset: const Offset(0, 4),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(theme, isDarkMode),
              _buildContent(theme, isDarkMode),
              _buildActions(theme, isDarkMode),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(KFTDesignSystem.spacingLg),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            KFTDesignSystem.primaryColor,
            KFTDesignSystem.primaryColor.withOpacity(0.8),
          ],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(KFTDesignSystem.borderRadiusLg),
          topRight: Radius.circular(KFTDesignSystem.borderRadiusLg),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(KFTDesignSystem.spacingMd),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusMd),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: const Icon(
              Icons.download_rounded,
              color: Colors.white,
              size: 28,
            ),
          ),
          const SizedBox(width: KFTDesignSystem.spacingMd),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Install KFT Fitness',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                    letterSpacing: -0.5,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  'Get instant access to your workouts',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(ThemeData theme, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.all(KFTDesignSystem.spacingLg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Install this app on your device for:',
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: KFTDesignSystem.getTextPrimaryColor(context),
              fontSize: 16,
            ),
          ),
          const SizedBox(height: KFTDesignSystem.spacingLg),
          _buildBenefit(theme, Icons.flash_on_outlined, 'Lightning-fast access', 'Launch instantly from your home screen'),
          const SizedBox(height: KFTDesignSystem.spacingMd),
          _buildBenefit(theme, Icons.offline_bolt_outlined, 'Works offline', 'Access your workouts without internet'),
          const SizedBox(height: KFTDesignSystem.spacingMd),
          _buildBenefit(theme, Icons.home_outlined, 'Native app experience', 'Full-screen, distraction-free workouts'),
        ],
      ),
    );
  }

  Widget _buildBenefit(ThemeData theme, IconData icon, String title, String description) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: KFTDesignSystem.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 20,
            color: KFTDesignSystem.primaryColor,
          ),
        ),
        const SizedBox(width: KFTDesignSystem.spacingMd),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: KFTDesignSystem.getTextPrimaryColor(context),
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: KFTDesignSystem.getTextSecondaryColor(context),
                  fontSize: 13,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActions(ThemeData theme, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(KFTDesignSystem.spacingLg),
      decoration: BoxDecoration(
        color: KFTDesignSystem.getBackgroundColor(context).withOpacity(0.5),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(KFTDesignSystem.borderRadiusLg),
          bottomRight: Radius.circular(KFTDesignSystem.borderRadiusLg),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextButton(
              onPressed: _isInstalling ? null : _handleDismiss,
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 14),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusSm),
                ),
              ),
              child: Text(
                'Not now',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: KFTDesignSystem.getTextSecondaryColor(context),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          const SizedBox(width: KFTDesignSystem.spacingMd),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _isInstalling ? null : _handleInstall,
              style: ElevatedButton.styleFrom(
                backgroundColor: KFTDesignSystem.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 14),
                elevation: 2,
                shadowColor: KFTDesignSystem.primaryColor.withOpacity(0.3),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusSm),
                ),
              ),
              child: _isInstalling
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2.5,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.download_rounded,
                          size: 18,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Install App',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
