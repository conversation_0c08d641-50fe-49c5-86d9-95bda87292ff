import 'dart:async';
import 'dart:html' as html;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Enhanced PWA installation service
/// Provides comprehensive PWA installation detection and persistent prompt management
class SimplePWAService {
  static final SimplePWAService _instance = SimplePWAService._internal();
  factory SimplePWAService() => _instance;
  SimplePWAService._internal();

  // Event storage
  dynamic _beforeInstallPromptEvent;

  // State tracking
  bool _isInstalled = false;
  bool _isInstallable = false;
  bool _hasShownPrompt = false;
  bool _userDismissedPermanently = false;
  int _promptDismissCount = 0;
  DateTime? _lastPromptTime;

  // Mobile detection
  bool _isMobile = false;
  bool _isIOS = false;
  bool _isAndroid = false;
  String _browserType = 'unknown';

  // Configuration
  static const int maxDismissCount = 3; // Show prompt up to 3 times
  static const Duration promptCooldown = Duration(hours: 24); // Wait 24h between prompts
  static const String _dismissCountKey = 'pwa_dismiss_count';
  static const String _lastPromptKey = 'pwa_last_prompt';
  static const String _permanentDismissKey = 'pwa_permanent_dismiss';

  // Notifiers
  final ValueNotifier<bool> installPromptAvailable = ValueNotifier<bool>(false);
  final ValueNotifier<bool> shouldShowDialog = ValueNotifier<bool>(false);

  // Stream controllers
  final StreamController<bool> _installabilityController = StreamController<bool>.broadcast();
  final StreamController<bool> _installationController = StreamController<bool>.broadcast();

  /// Stream that emits when PWA installability changes
  Stream<bool> get installabilityStream => _installabilityController.stream;

  /// Stream that emits when PWA installation status changes
  Stream<bool> get installationStream => _installationController.stream;

  /// Whether the PWA is currently installable
  bool get isInstallable => _isInstallable;

  /// Whether the PWA is already installed
  bool get isInstalled => _isInstalled;

  /// Whether we should show the install prompt
  bool get shouldShowPrompt => (_isInstallable || _isMobile) && !_isInstalled && !_userDismissedPermanently && _canShowPrompt();

  /// Mobile device detection getters
  bool get isMobile => _isMobile;
  bool get isIOS => _isIOS;
  bool get isAndroid => _isAndroid;
  String get browserType => _browserType;

  /// Initialize the PWA service
  Future<void> initialize() async {
    if (!kIsWeb) return;

    try {
      debugPrint('🔧 Initializing Enhanced PWA Service...');

      // Detect mobile device and browser
      _detectMobileDevice();

      // Load persistent state
      await _loadPersistentState();

      // Check if already installed
      _checkInstallationStatus();

      // Set up event listeners
      _setupEventListeners();

      // For mobile devices, always show prompts since beforeinstallprompt may not fire
      if (_isMobile) {
        _isInstallable = true;
        installPromptAvailable.value = shouldShowPrompt;
      }

      // Check if we should show dialog on startup
      _checkShouldShowDialog();

      debugPrint('✅ Enhanced PWA Service initialized');
    } catch (e) {
      debugPrint('❌ Error initializing Enhanced PWA Service: $e');
    }
  }

  /// Load persistent state from SharedPreferences
  Future<void> _loadPersistentState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _promptDismissCount = prefs.getInt(_dismissCountKey) ?? 0;
      _userDismissedPermanently = prefs.getBool(_permanentDismissKey) ?? false;

      final lastPromptMillis = prefs.getInt(_lastPromptKey);
      if (lastPromptMillis != null) {
        _lastPromptTime = DateTime.fromMillisecondsSinceEpoch(lastPromptMillis);
      }

      debugPrint('📊 PWA State loaded - Dismiss count: $_promptDismissCount, Permanent dismiss: $_userDismissedPermanently');
    } catch (e) {
      debugPrint('❌ Error loading PWA persistent state: $e');
    }
  }

  /// Save persistent state to SharedPreferences
  Future<void> _savePersistentState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_dismissCountKey, _promptDismissCount);
      await prefs.setBool(_permanentDismissKey, _userDismissedPermanently);

      if (_lastPromptTime != null) {
        await prefs.setInt(_lastPromptKey, _lastPromptTime!.millisecondsSinceEpoch);
      }
    } catch (e) {
      debugPrint('❌ Error saving PWA persistent state: $e');
    }
  }

  /// Check if we can show a prompt based on cooldown and dismiss count
  bool _canShowPrompt() {
    if (_promptDismissCount >= maxDismissCount) {
      return false;
    }

    if (_lastPromptTime != null) {
      final timeSinceLastPrompt = DateTime.now().difference(_lastPromptTime!);
      if (timeSinceLastPrompt < promptCooldown) {
        return false;
      }
    }

    return true;
  }

  /// Detect mobile device and browser type
  void _detectMobileDevice() {
    try {
      final userAgent = html.window.navigator.userAgent.toLowerCase();

      // Detect mobile devices
      _isMobile = userAgent.contains('mobile') ||
                  userAgent.contains('android') ||
                  userAgent.contains('iphone') ||
                  userAgent.contains('ipad') ||
                  userAgent.contains('ipod') ||
                  html.window.screen!.width! <= 768;

      // Detect specific platforms
      _isIOS = userAgent.contains('iphone') ||
               userAgent.contains('ipad') ||
               userAgent.contains('ipod');

      _isAndroid = userAgent.contains('android');

      // Detect browser type
      if (userAgent.contains('chrome') && !userAgent.contains('edg')) {
        _browserType = 'chrome';
      } else if (userAgent.contains('safari') && !userAgent.contains('chrome')) {
        _browserType = 'safari';
      } else if (userAgent.contains('firefox')) {
        _browserType = 'firefox';
      } else if (userAgent.contains('edg')) {
        _browserType = 'edge';
      } else {
        _browserType = 'other';
      }

      debugPrint('📱 Device detection - Mobile: $_isMobile, iOS: $_isIOS, Android: $_isAndroid, Browser: $_browserType');
    } catch (e) {
      debugPrint('❌ Error detecting mobile device: $e');
    }
  }

  /// Check if we should show the dialog on startup
  void _checkShouldShowDialog() {
    // Show dialog if installable and should show prompt
    if (shouldShowPrompt) {
      // Longer delay for mobile devices to ensure everything is loaded
      final delay = _isMobile ? const Duration(milliseconds: 3000) : const Duration(milliseconds: 1500);

      Future.delayed(delay, () {
        if (!_isInstalled && (_isInstallable || _isMobile) && !_userDismissedPermanently) {
          shouldShowDialog.value = true;
          debugPrint('🔔 PWA dialog should be shown on startup (Mobile: $_isMobile)');
        }
      });
    }
  }

  /// Check if the app is already installed
  void _checkInstallationStatus() {
    try {
      // Check multiple indicators for installation
      final standaloneQuery = html.window.matchMedia('(display-mode: standalone)');
      final fullscreenQuery = html.window.matchMedia('(display-mode: fullscreen)');
      final minimalUiQuery = html.window.matchMedia('(display-mode: minimal-ui)');

      // Check if running in any PWA mode
      _isInstalled = standaloneQuery.matches || fullscreenQuery.matches || minimalUiQuery.matches;

      // Also check for iOS Safari standalone mode
      if (!_isInstalled && _isIOS) {
        try {
          // Check for iOS standalone mode using window.navigator.standalone
          final jsResult = html.window.navigator.userAgent.contains('standalone') ||
                          html.window.matchMedia('(display-mode: standalone)').matches;
          _isInstalled = jsResult;

          // Additional check for iOS home screen app
          if (!_isInstalled) {
            // Check if running from home screen (no Safari UI)
            final windowHeight = html.window.innerHeight;
            final screenHeight = html.window.screen!.height;
            final isFullscreen = windowHeight == screenHeight;
            _isInstalled = isFullscreen && _isIOS;
          }
        } catch (e) {
          debugPrint('⚠️ Could not check iOS standalone mode: $e');
        }
      }

      if (_isInstalled) {
        debugPrint('📱 PWA is already installed (standalone mode detected)');
        installPromptAvailable.value = false;
        shouldShowDialog.value = false;
        _installationController.add(true);
      } else {
        debugPrint('🌐 PWA is running in browser mode');
      }
    } catch (e) {
      debugPrint('❌ Error checking installation status: $e');
    }
  }

  /// Set up PWA event listeners
  void _setupEventListeners() {
    try {
      // Listen for beforeinstallprompt event
      html.window.addEventListener('beforeinstallprompt', (event) {
        debugPrint('📱 PWA beforeinstallprompt event received');
        event.preventDefault();

        if (!_isInstalled) {
          _beforeInstallPromptEvent = event;
          _isInstallable = true;
          installPromptAvailable.value = shouldShowPrompt;
          _installabilityController.add(_isInstallable);

          // Check if we should show dialog
          _checkShouldShowDialog();

          debugPrint('✅ PWA is installable, prompt available: ${shouldShowPrompt}');
        }
      });

      // Listen for appinstalled event
      html.window.addEventListener('appinstalled', (event) {
        debugPrint('🎉 PWA installed successfully');
        _isInstalled = true;
        _isInstallable = false;
        _beforeInstallPromptEvent = null;
        installPromptAvailable.value = false;
        shouldShowDialog.value = false;
        _installabilityController.add(false);
        _installationController.add(true);

        // Reset dismiss state since app is now installed
        _resetDismissState();
      });

      // Listen for display mode changes (for better installation detection)
      try {
        final standaloneQuery = html.window.matchMedia('(display-mode: standalone)');
        standaloneQuery.addEventListener('change', (event) {
          _checkInstallationStatus();
        });
      } catch (e) {
        debugPrint('⚠️ Could not set up display mode listener: $e');
      }

      debugPrint('✅ Enhanced PWA event listeners set up');
    } catch (e) {
      debugPrint('❌ Error setting up PWA event listeners: $e');
    }
  }

  /// Reset dismiss state when app is installed
  Future<void> _resetDismissState() async {
    _promptDismissCount = 0;
    _userDismissedPermanently = false;
    _lastPromptTime = null;
    await _savePersistentState();
  }

  /// Prompt the user to install the PWA
  Future<bool> promptInstall() async {
    if (!kIsWeb || _isInstalled) {
      debugPrint('⚠️ Cannot prompt install - not available (installed: $_isInstalled)');
      return false;
    }

    try {
      debugPrint('📱 Prompting user to install PWA... (Mobile: $_isMobile, iOS: $_isIOS, Browser: $_browserType)');

      // Record that we're showing a prompt
      _lastPromptTime = DateTime.now();
      await _savePersistentState();

      // For browsers that support beforeinstallprompt
      if (_beforeInstallPromptEvent != null) {
        final dynamic prompt = _beforeInstallPromptEvent;
        final result = await prompt.prompt();

        if (result.outcome == 'accepted') {
          debugPrint('🎉 User accepted PWA installation');
          return true;
        } else {
          debugPrint('❌ User declined PWA installation');
          await _handlePromptDismiss();
          return false;
        }
      }

      // For mobile browsers without beforeinstallprompt (like iOS Safari)
      // Return false to trigger custom dialog with instructions
      debugPrint('📱 Native prompt not available, will show custom dialog');
      return false;

    } catch (e) {
      debugPrint('❌ Error prompting PWA install: $e');
      await _handlePromptDismiss();
      return false;
    }
  }

  /// Handle when user dismisses the prompt
  Future<void> _handlePromptDismiss() async {
    _promptDismissCount++;

    if (_promptDismissCount >= maxDismissCount) {
      _userDismissedPermanently = true;
      debugPrint('🚫 User has dismissed PWA prompt $_promptDismissCount times - marking as permanent dismiss');
    }

    await _savePersistentState();
    installPromptAvailable.value = false;
    shouldShowDialog.value = false;
  }

  /// Mark that the dialog prompt has been shown (but not necessarily dismissed)
  void markDialogShown() {
    shouldShowDialog.value = false;
    debugPrint('📋 PWA dialog marked as shown');
  }

  /// Mark that the prompt has been shown (legacy method for compatibility)
  void markPromptShown() {
    markDialogShown();
  }

  /// Force show the install dialog (for testing or manual triggers)
  void forceShowDialog() {
    if (_isInstallable && !_isInstalled) {
      shouldShowDialog.value = true;
      debugPrint('🔧 Forcing PWA dialog to show');
    }
  }

  /// Reset all PWA state (for testing purposes)
  Future<void> resetState() async {
    _promptDismissCount = 0;
    _userDismissedPermanently = false;
    _lastPromptTime = null;
    _hasShownPrompt = false;
    await _savePersistentState();

    // Re-check if we should show prompts
    if (_isInstallable && !_isInstalled) {
      installPromptAvailable.value = true;
      shouldShowDialog.value = true;
    }

    debugPrint('🔄 PWA state reset');
  }

  /// Dispose of resources
  void dispose() {
    _installabilityController.close();
    _installationController.close();
    installPromptAvailable.dispose();
    shouldShowDialog.dispose();
  }
}
