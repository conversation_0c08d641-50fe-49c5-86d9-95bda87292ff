import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

/// Optimized image service for production-grade performance
/// Handles caching, memory management, and device-specific optimizations
class OptimizedImageService {
  static final OptimizedImageService _instance = OptimizedImageService._internal();
  factory OptimizedImageService() => _instance;
  OptimizedImageService._internal();

  // Memory cache with size limits
  static const int _maxMemoryCacheSize = 50 * 1024 * 1024; // 50MB for high-end devices
  static const int _maxMemoryCacheSizeLowEnd = 20 * 1024 * 1024; // 20MB for low-end devices
  static const int _maxCacheEntries = 200;
  static const int _maxCacheEntriesLowEnd = 100;

  final Map<String, Uint8List> _memoryCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  int _currentCacheSize = 0;
  bool _isLowEndDevice = false;

  /// Initialize the service with device-specific optimizations
  Future<void> initialize() async {
    await _detectDeviceCapabilities();
    await _cleanupOldCache();
    debugPrint('🖼️ OptimizedImageService initialized for ${_isLowEndDevice ? "low-end" : "high-end"} device');
  }

  /// Detect if device is low-end based on available memory
  Future<void> _detectDeviceCapabilities() async {
    try {
      // Get device info to determine capabilities
      final info = await _getDeviceMemoryInfo();
      _isLowEndDevice = info['totalMemory'] < 3.0; // Less than 3GB RAM
      debugPrint('📱 Device memory: ${info['totalMemory']}GB, Low-end: $_isLowEndDevice');
    } catch (e) {
      // Default to low-end for safety
      _isLowEndDevice = true;
      debugPrint('⚠️ Could not detect device capabilities, defaulting to low-end mode');
    }
  }

  /// Get device memory information
  Future<Map<String, dynamic>> _getDeviceMemoryInfo() async {
    try {
      if (Platform.isAndroid) {
        // Use method channel to get Android memory info
        const platform = MethodChannel('com.kft.fitness/device_info');
        final result = await platform.invokeMethod('getMemoryInfo');
        return {
          'totalMemory': (result['totalMemory'] ?? 2048) / 1024.0, // Convert MB to GB
          'availableMemory': (result['availableMemory'] ?? 1024) / 1024.0,
        };
      } else if (Platform.isIOS) {
        // iOS memory detection would go here
        return {'totalMemory': 4.0, 'availableMemory': 2.0}; // Default for iOS
      }
    } catch (e) {
      debugPrint('❌ Error getting device memory info: $e');
    }
    return {'totalMemory': 2.0, 'availableMemory': 1.0}; // Safe defaults
  }

  /// Get device-specific cache limits
  int get _maxCacheSizeForDevice => _isLowEndDevice ? _maxMemoryCacheSizeLowEnd : _maxMemoryCacheSize;
  int get _maxEntriesForDevice => _isLowEndDevice ? _maxCacheEntriesLowEnd : _maxCacheEntries;

  /// Load and cache an image with optimizations
  Future<ImageProvider> loadOptimizedImage(
    String url, {
    int? targetWidth,
    int? targetHeight,
    bool enableMemoryCache = true,
    bool enableDiskCache = true,
  }) async {
    try {
      final cacheKey = _generateCacheKey(url, targetWidth, targetHeight);

      // Check memory cache first
      if (enableMemoryCache && _memoryCache.containsKey(cacheKey)) {
        _updateCacheTimestamp(cacheKey);
        return MemoryImage(_memoryCache[cacheKey]!);
      }

      // Check disk cache
      if (enableDiskCache) {
        final cachedBytes = await _loadFromDiskCache(cacheKey);
        if (cachedBytes != null) {
          if (enableMemoryCache) {
            await _addToMemoryCache(cacheKey, cachedBytes);
          }
          return MemoryImage(cachedBytes);
        }
      }

      // Load from network with optimizations
      final bytes = await _loadFromNetwork(url, targetWidth, targetHeight);

      // Cache the result
      if (enableDiskCache) {
        await _saveToDiskCache(cacheKey, bytes);
      }
      if (enableMemoryCache) {
        await _addToMemoryCache(cacheKey, bytes);
      }

      return MemoryImage(bytes);
    } catch (e) {
      debugPrint('❌ Error loading optimized image: $e');
      // Return a fallback image
      return const AssetImage('assets/images/placeholder.png');
    }
  }

  /// Consolidate HTTP response bytes
  Future<Uint8List> _consolidateHttpClientResponseBytes(HttpClientResponse response) async {
    final completer = Completer<Uint8List>();
    final sink = BytesBuilder();

    response.listen(
      (List<int> chunk) {
        sink.add(chunk);
      },
      onDone: () {
        completer.complete(sink.takeBytes());
      },
      onError: (error) {
        completer.completeError(error);
      },
    );

    return completer.future;
  }

  /// Load image from network with device-specific optimizations
  Future<Uint8List> _loadFromNetwork(String url, int? targetWidth, int? targetHeight) async {
    final client = HttpClient();
    try {
      // Set timeouts based on device capabilities
      client.connectionTimeout = Duration(seconds: _isLowEndDevice ? 15 : 10);
      client.idleTimeout = Duration(seconds: _isLowEndDevice ? 30 : 20);

      final request = await client.getUrl(Uri.parse(url));
      final response = await request.close();

      if (response.statusCode == 200) {
        final bytes = await _consolidateHttpClientResponseBytes(response);

        // Resize image if target dimensions are provided and device can handle it
        if ((targetWidth != null || targetHeight != null) && !_isLowEndDevice) {
          return await _resizeImage(bytes, targetWidth, targetHeight);
        }

        return bytes;
      } else {
        throw Exception('Failed to load image: ${response.statusCode}');
      }
    } finally {
      client.close();
    }
  }

  /// Resize image to target dimensions
  Future<Uint8List> _resizeImage(Uint8List bytes, int? targetWidth, int? targetHeight) async {
    try {
      final codec = await ui.instantiateImageCodec(
        bytes,
        targetWidth: targetWidth,
        targetHeight: targetHeight,
      );
      final frame = await codec.getNextFrame();
      final data = await frame.image.toByteData(format: ui.ImageByteFormat.png);
      return data!.buffer.asUint8List();
    } catch (e) {
      debugPrint('⚠️ Image resize failed, using original: $e');
      return bytes;
    }
  }

  /// Generate cache key for image
  String _generateCacheKey(String url, int? width, int? height) {
    final key = '$url:${width ?? 0}:${height ?? 0}';
    return md5.convert(utf8.encode(key)).toString();
  }

  /// Add image to memory cache with size management
  Future<void> _addToMemoryCache(String key, Uint8List bytes) async {
    if (bytes.length > _maxCacheSizeForDevice ~/ 4) {
      // Don't cache images that are too large
      return;
    }

    // Remove old entries if cache is full
    while (_memoryCache.length >= _maxEntriesForDevice ||
           _currentCacheSize + bytes.length > _maxCacheSizeForDevice) {
      await _removeOldestCacheEntry();
    }

    _memoryCache[key] = bytes;
    _cacheTimestamps[key] = DateTime.now();
    _currentCacheSize += bytes.length;
  }

  /// Remove oldest cache entry
  Future<void> _removeOldestCacheEntry() async {
    if (_cacheTimestamps.isEmpty) return;

    final oldestKey = _cacheTimestamps.entries
        .reduce((a, b) => a.value.isBefore(b.value) ? a : b)
        .key;

    final bytes = _memoryCache.remove(oldestKey);
    _cacheTimestamps.remove(oldestKey);
    if (bytes != null) {
      _currentCacheSize -= bytes.length;
    }
  }

  /// Update cache timestamp for LRU
  void _updateCacheTimestamp(String key) {
    _cacheTimestamps[key] = DateTime.now();
  }

  /// Load image from disk cache
  Future<Uint8List?> _loadFromDiskCache(String key) async {
    try {
      final cacheDir = await _getCacheDirectory();
      final file = File('${cacheDir.path}/$key.cache');

      if (await file.exists()) {
        final stat = await file.stat();
        // Check if cache is not too old (7 days)
        if (DateTime.now().difference(stat.modified).inDays < 7) {
          return await file.readAsBytes();
        } else {
          // Delete old cache file
          await file.delete();
        }
      }
    } catch (e) {
      debugPrint('⚠️ Error loading from disk cache: $e');
    }
    return null;
  }

  /// Save image to disk cache
  Future<void> _saveToDiskCache(String key, Uint8List bytes) async {
    try {
      final cacheDir = await _getCacheDirectory();
      final file = File('${cacheDir.path}/$key.cache');
      await file.writeAsBytes(bytes);
    } catch (e) {
      debugPrint('⚠️ Error saving to disk cache: $e');
    }
  }

  /// Get cache directory
  Future<Directory> _getCacheDirectory() async {
    final appDir = await getApplicationCacheDirectory();
    final cacheDir = Directory('${appDir.path}/optimized_images');
    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
    }
    return cacheDir;
  }

  /// Clean up old cache files
  Future<void> _cleanupOldCache() async {
    try {
      final cacheDir = await _getCacheDirectory();
      final files = await cacheDir.list().toList();

      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          if (DateTime.now().difference(stat.modified).inDays > 7) {
            await file.delete();
          }
        }
      }
    } catch (e) {
      debugPrint('⚠️ Error cleaning up cache: $e');
    }
  }

  /// Clear all caches
  Future<void> clearCache() async {
    // Clear memory cache
    _memoryCache.clear();
    _cacheTimestamps.clear();
    _currentCacheSize = 0;

    // Clear disk cache
    try {
      final cacheDir = await _getCacheDirectory();
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
      }
    } catch (e) {
      debugPrint('⚠️ Error clearing disk cache: $e');
    }
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'memoryEntries': _memoryCache.length,
      'memorySizeMB': (_currentCacheSize / (1024 * 1024)).toStringAsFixed(2),
      'maxSizeMB': (_maxCacheSizeForDevice / (1024 * 1024)).toStringAsFixed(2),
      'isLowEndDevice': _isLowEndDevice,
      'maxEntries': _maxEntriesForDevice,
    };
  }

  /// Dispose resources
  void dispose() {
    _memoryCache.clear();
    _cacheTimestamps.clear();
    _currentCacheSize = 0;
  }
}
