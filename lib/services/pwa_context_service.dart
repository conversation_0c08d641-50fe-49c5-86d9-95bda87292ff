import 'package:flutter/foundation.dart';
// ignore: avoid_web_libraries_in_flutter
import 'dart:html' as html show window;

/// PWA Context Detection Service
/// Ensures video functionality only works within PWA context
/// Provides fallback messages when accessed outside PWA environment
class PWAContextService {
  static final PWAContextService _instance = PWAContextService._internal();
  factory PWAContextService() => _instance;
  PWAContextService._internal();

  // PWA context state
  bool _isPWAContext = false;
  bool _isStandalone = false;
  bool _isFullscreen = false;
  bool _isMinimalUI = false;
  String _displayMode = 'browser';
  
  // Initialization state
  bool _isInitialized = false;

  /// Initialize PWA context detection
  Future<void> initialize() async {
    if (!kIsWeb || _isInitialized) return;

    try {
      debugPrint('🔍 Initializing PWA Context Detection...');
      
      // Detect PWA display modes
      _detectDisplayModes();
      
      // Check for PWA indicators
      _checkPWAIndicators();
      
      // Determine overall PWA context
      _determinePWAContext();
      
      _isInitialized = true;
      
      debugPrint('✅ PWA Context Detection initialized');
      debugPrint('📱 PWA Context: $_isPWAContext');
      debugPrint('🖥️ Display Mode: $_displayMode');
      debugPrint('📺 Standalone: $_isStandalone');
      
    } catch (e) {
      debugPrint('❌ Error initializing PWA Context Detection: $e');
      // Default to non-PWA context for safety
      _isPWAContext = false;
    }
  }

  /// Detect PWA display modes
  void _detectDisplayModes() {
    try {
      // Check for standalone mode (primary PWA indicator)
      final standaloneQuery = html.window.matchMedia('(display-mode: standalone)');
      _isStandalone = standaloneQuery.matches;
      
      // Check for fullscreen mode
      final fullscreenQuery = html.window.matchMedia('(display-mode: fullscreen)');
      _isFullscreen = fullscreenQuery.matches;
      
      // Check for minimal-ui mode
      final minimalUIQuery = html.window.matchMedia('(display-mode: minimal-ui)');
      _isMinimalUI = minimalUIQuery.matches;
      
      // Determine display mode
      if (_isStandalone) {
        _displayMode = 'standalone';
      } else if (_isFullscreen) {
        _displayMode = 'fullscreen';
      } else if (_isMinimalUI) {
        _displayMode = 'minimal-ui';
      } else {
        _displayMode = 'browser';
      }
      
      debugPrint('📱 Display mode detection: $_displayMode');
    } catch (e) {
      debugPrint('⚠️ Display mode detection failed: $e');
    }
  }

  /// Check for additional PWA indicators
  void _checkPWAIndicators() {
    try {
      // Check if launched from home screen (iOS)
      final navigator = html.window.navigator;
      final userAgent = navigator.userAgent.toLowerCase();
      
      // iOS PWA detection
      if (userAgent.contains('iphone') || userAgent.contains('ipad')) {
        // iOS PWA runs in standalone mode without browser UI
        final isIOSPWA = _isStandalone && !userAgent.contains('safari');
        if (isIOSPWA) {
          debugPrint('📱 iOS PWA detected');
        }
      }
      
      // Android PWA detection
      if (userAgent.contains('android')) {
        // Android PWA typically runs in standalone mode
        if (_isStandalone) {
          debugPrint('🤖 Android PWA detected');
        }
      }
      
      // Check for PWA-specific features
      final hasPWAFeatures = _checkPWAFeatures();
      debugPrint('🔧 PWA features available: $hasPWAFeatures');
      
    } catch (e) {
      debugPrint('⚠️ PWA indicator check failed: $e');
    }
  }

  /// Check for PWA-specific features
  bool _checkPWAFeatures() {
    try {
      // Check for service worker support
      final hasServiceWorker = html.window.navigator.serviceWorker != null;
      
      // Check for manifest support
      final hasManifest = html.window.document.querySelector('link[rel="manifest"]') != null;
      
      // Check for app-like behavior indicators
      final hasAppBehavior = _isStandalone || _isFullscreen || _isMinimalUI;
      
      return hasServiceWorker && hasManifest && hasAppBehavior;
    } catch (e) {
      debugPrint('⚠️ PWA feature check failed: $e');
      return false;
    }
  }

  /// Determine overall PWA context
  void _determinePWAContext() {
    // PWA context is true if running in any PWA display mode
    _isPWAContext = _isStandalone || _isFullscreen || _isMinimalUI;
    
    // Additional validation for edge cases
    if (!_isPWAContext) {
      // Check for forced PWA mode (development/testing)
      final url = html.window.location.href;
      if (url.contains('pwa=true') || url.contains('standalone=true')) {
        _isPWAContext = true;
        _displayMode = 'forced-pwa';
        debugPrint('🔧 Forced PWA mode detected');
      }
    }
  }

  /// Check if currently running in PWA context
  bool get isPWAContext => _isPWAContext;

  /// Check if running in standalone mode
  bool get isStandalone => _isStandalone;

  /// Get current display mode
  String get displayMode => _displayMode;

  /// Check if video functionality should be available
  bool get isVideoAllowed => _isPWAContext;

  /// Get appropriate error message for non-PWA context
  String get nonPWAMessage {
    return '''
🚀 KFT Fitness App Required

Video content is only available in the KFT Fitness app for the best experience.

📱 Install the app:
• Tap the share button in your browser
• Select "Add to Home Screen"
• Open KFT Fitness from your home screen

💡 Why use the app?
• Lightning-fast performance
• Offline access to content
• Native app experience
• Enhanced video quality
• Better security

Install now for instant access to all features!
''';
  }

  /// Get HTML fallback message for non-PWA context
  String get nonPWAHTMLMessage {
    return '''
<!DOCTYPE html>
<html>
<head>
    <title>KFT Fitness - App Required</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #3D5AFE 0%, #2196F3 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            max-width: 400px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        .icon {
            font-size: 64px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        h1 {
            margin: 0 0 20px 0;
            font-size: 28px;
            font-weight: 700;
        }
        p {
            margin: 15px 0;
            line-height: 1.6;
            opacity: 0.9;
        }
        .install-btn {
            background: white;
            color: #3D5AFE;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 20px;
            transition: transform 0.2s;
        }
        .install-btn:hover {
            transform: translateY(-2px);
        }
        .features {
            text-align: left;
            margin-top: 30px;
        }
        .feature {
            margin: 10px 0;
            display: flex;
            align-items: center;
        }
        .feature-icon {
            margin-right: 10px;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🚀</div>
        <h1>KFT Fitness App Required</h1>
        <p>Video content is only available in the KFT Fitness app for the best experience.</p>
        
        <div class="features">
            <div class="feature">
                <span class="feature-icon">⚡</span>
                <span>Lightning-fast performance</span>
            </div>
            <div class="feature">
                <span class="feature-icon">📱</span>
                <span>Native app experience</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🔒</span>
                <span>Enhanced security</span>
            </div>
            <div class="feature">
                <span class="feature-icon">📺</span>
                <span>Premium video quality</span>
            </div>
        </div>
        
        <button class="install-btn" onclick="showInstallInstructions()">
            Install KFT Fitness App
        </button>
    </div>
    
    <script>
        function showInstallInstructions() {
            alert('📱 To install KFT Fitness:\\n\\n1. Tap the share button in your browser\\n2. Select "Add to Home Screen"\\n3. Open KFT Fitness from your home screen\\n\\nEnjoy the full app experience!');
        }
    </script>
</body>
</html>
''';
  }

  /// Force PWA context (for development/testing)
  void forcePWAContext(bool force) {
    if (kDebugMode) {
      _isPWAContext = force;
      debugPrint('🔧 PWA context forced to: $force');
    }
  }

  /// Reset PWA context detection
  void reset() {
    _isPWAContext = false;
    _isStandalone = false;
    _isFullscreen = false;
    _isMinimalUI = false;
    _displayMode = 'browser';
    _isInitialized = false;
  }
}
