import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:http/http.dart' as http;

/// Production-grade connectivity service with offline support
/// Monitors network status and provides intelligent retry mechanisms
class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  // Connectivity state
  bool _isConnected = true;
  bool _isInitialized = false;
  List<ConnectivityResult> _connectionTypes = [ConnectivityResult.none];

  // Stream controllers
  final StreamController<bool> _connectivityController = StreamController<bool>.broadcast();
  final StreamController<List<ConnectivityResult>> _connectionTypeController = StreamController<List<ConnectivityResult>>.broadcast();

  // Subscriptions
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  Timer? _connectivityCheckTimer;

  // Network quality tracking
  NetworkQuality _networkQuality = NetworkQuality.unknown;
  final List<NetworkSpeedTest> _speedTests = [];

  // Retry configuration
  static const int _maxRetries = 3;
  static const Duration _retryDelay = Duration(seconds: 2);
  static const Duration _connectivityCheckInterval = Duration(seconds: 30);

  /// Initialize connectivity monitoring
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Check initial connectivity
      await _checkInitialConnectivity();

      // Start monitoring connectivity changes
      _startConnectivityMonitoring();

      // Start periodic connectivity checks
      _startPeriodicChecks();

      _isInitialized = true;
      debugPrint('🌐 ConnectivityService initialized - Connected: $_isConnected, Types: $_connectionTypes');
    } catch (e) {
      debugPrint('❌ Error initializing ConnectivityService: $e');
      _isInitialized = true; // Mark as initialized even on error
    }
  }

  /// Check initial connectivity status
  Future<void> _checkInitialConnectivity() async {
    try {
      final connectivity = Connectivity();
      _connectionTypes = await connectivity.checkConnectivity();
      _isConnected = !_connectionTypes.contains(ConnectivityResult.none);

      if (_isConnected) {
        // Verify actual internet connectivity
        _isConnected = await _verifyInternetConnectivity();
      }
    } catch (e) {
      debugPrint('⚠️ Error checking initial connectivity: $e');
      _isConnected = false;
    }
  }

  /// Start monitoring connectivity changes
  void _startConnectivityMonitoring() {
    final connectivity = Connectivity();
    _connectivitySubscription = connectivity.onConnectivityChanged.listen(
      _onConnectivityChanged,
      onError: (error) {
        debugPrint('❌ Connectivity monitoring error: $error');
      },
    );
  }

  /// Handle connectivity changes
  Future<void> _onConnectivityChanged(List<ConnectivityResult> results) async {
    _connectionTypes = results;
    final wasConnected = _isConnected;

    if (results.contains(ConnectivityResult.none) || results.isEmpty) {
      _isConnected = false;
      _networkQuality = NetworkQuality.offline;
    } else {
      // Verify actual internet connectivity
      _isConnected = await _verifyInternetConnectivity();
      if (_isConnected) {
        await _testNetworkQuality();
      }
    }

    // Notify listeners if connectivity status changed
    if (wasConnected != _isConnected) {
      _connectivityController.add(_isConnected);
      debugPrint('🌐 Connectivity changed: ${_isConnected ? "Connected" : "Disconnected"} ($_connectionTypes)');
    }

    _connectionTypeController.add(_connectionTypes);
  }

  /// Start periodic connectivity checks
  void _startPeriodicChecks() {
    _connectivityCheckTimer = Timer.periodic(_connectivityCheckInterval, (_) {
      _performPeriodicCheck();
    });
  }

  /// Perform periodic connectivity check
  Future<void> _performPeriodicCheck() async {
    if (!_connectionTypes.contains(ConnectivityResult.none)) {
      final wasConnected = _isConnected;
      _isConnected = await _verifyInternetConnectivity();

      if (wasConnected != _isConnected) {
        _connectivityController.add(_isConnected);
        debugPrint('🌐 Periodic check - Connectivity: ${_isConnected ? "Connected" : "Disconnected"}');
      }
    }
  }

  /// Verify actual internet connectivity
  Future<bool> _verifyInternetConnectivity() async {
    try {
      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 10);

      // Test multiple endpoints for reliability
      final testUrls = [
        'https://www.google.com',
        'https://www.cloudflare.com',
        'https://httpbin.org/status/200',
      ];

      for (final url in testUrls) {
        try {
          final request = await client.getUrl(Uri.parse(url));
          final response = await request.close();
          client.close();

          if (response.statusCode == 200) {
            return true;
          }
        } catch (e) {
          // Continue to next URL
          continue;
        }
      }

      client.close();
      return false;
    } catch (e) {
      debugPrint('⚠️ Error verifying internet connectivity: $e');
      return false;
    }
  }

  /// Test network quality
  Future<void> _testNetworkQuality() async {
    try {
      final stopwatch = Stopwatch()..start();

      // Test with a small file download
      final response = await http.get(
        Uri.parse('https://httpbin.org/bytes/1024'), // 1KB test
        headers: {'Cache-Control': 'no-cache'},
      ).timeout(const Duration(seconds: 10));

      stopwatch.stop();

      if (response.statusCode == 200) {
        final latencyMs = stopwatch.elapsedMilliseconds;
        final speedTest = NetworkSpeedTest(
          timestamp: DateTime.now(),
          latencyMs: latencyMs,
          downloadSpeedKbps: (1024 * 8) / (latencyMs / 1000), // Rough estimate
          connectionTypes: _connectionTypes,
        );

        _speedTests.add(speedTest);
        if (_speedTests.length > 10) {
          _speedTests.removeAt(0);
        }

        _networkQuality = _calculateNetworkQuality(latencyMs);
        debugPrint('🌐 Network quality test: ${_networkQuality.name} (${latencyMs}ms)');
      }
    } catch (e) {
      debugPrint('⚠️ Network quality test failed: $e');
      _networkQuality = NetworkQuality.poor;
    }
  }

  /// Calculate network quality based on latency
  NetworkQuality _calculateNetworkQuality(int latencyMs) {
    if (latencyMs < 100) return NetworkQuality.excellent;
    if (latencyMs < 300) return NetworkQuality.good;
    if (latencyMs < 1000) return NetworkQuality.fair;
    return NetworkQuality.poor;
  }

  /// Execute network request with retry logic
  Future<T> executeWithRetry<T>(
    Future<T> Function() networkCall, {
    int maxRetries = _maxRetries,
    Duration retryDelay = _retryDelay,
    bool requiresInternet = true,
  }) async {
    if (requiresInternet && !_isConnected) {
      throw NetworkException('No internet connection available');
    }

    Exception? lastException;

    for (int attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await networkCall();
      } catch (e) {
        lastException = e is Exception ? e : Exception(e.toString());

        if (attempt == maxRetries) {
          break; // Don't delay after the last attempt
        }

        // Check if we should retry based on error type
        if (!_shouldRetry(e)) {
          break;
        }

        // Wait before retrying
        await Future.delayed(retryDelay * (attempt + 1));

        // Check connectivity before retrying
        if (requiresInternet) {
          await _verifyInternetConnectivity();
          if (!_isConnected) {
            throw NetworkException('Lost internet connection during retry');
          }
        }
      }
    }

    throw lastException ?? NetworkException('Network request failed after $maxRetries retries');
  }

  /// Determine if an error should trigger a retry
  bool _shouldRetry(dynamic error) {
    final errorString = error.toString().toLowerCase();

    // Retry on network-related errors
    if (errorString.contains('socketexception') ||
        errorString.contains('timeout') ||
        errorString.contains('connection refused') ||
        errorString.contains('network error') ||
        errorString.contains('handshake')) {
      return true;
    }

    // Don't retry on authentication or client errors
    if (errorString.contains('401') ||
        errorString.contains('403') ||
        errorString.contains('404') ||
        errorString.contains('400')) {
      return false;
    }

    // Retry on server errors
    if (errorString.contains('500') ||
        errorString.contains('502') ||
        errorString.contains('503') ||
        errorString.contains('504')) {
      return true;
    }

    return false;
  }

  /// Wait for connectivity to be restored
  Future<void> waitForConnectivity({Duration? timeout}) async {
    if (_isConnected) return;

    final completer = Completer<void>();
    late StreamSubscription subscription;

    subscription = _connectivityController.stream.listen((isConnected) {
      if (isConnected) {
        subscription.cancel();
        completer.complete();
      }
    });

    if (timeout != null) {
      Timer(timeout, () {
        if (!completer.isCompleted) {
          subscription.cancel();
          completer.completeError(TimeoutException('Connectivity timeout', timeout));
        }
      });
    }

    return completer.future;
  }

  /// Get connectivity status
  bool get isConnected => _isConnected;

  /// Get connection types
  List<ConnectivityResult> get connectionTypes => _connectionTypes;

  /// Get network quality
  NetworkQuality get networkQuality => _networkQuality;

  /// Get connectivity stream
  Stream<bool> get connectivityStream => _connectivityController.stream;

  /// Get connection type stream
  Stream<List<ConnectivityResult>> get connectionTypeStream => _connectionTypeController.stream;

  /// Get network statistics
  NetworkStats getNetworkStats() {
    return NetworkStats(
      isConnected: _isConnected,
      connectionTypes: _connectionTypes,
      networkQuality: _networkQuality,
      speedTests: List.from(_speedTests),
      averageLatencyMs: _speedTests.isNotEmpty
          ? _speedTests.map((t) => t.latencyMs).reduce((a, b) => a + b) / _speedTests.length
          : 0,
    );
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivityCheckTimer?.cancel();
    _connectivityController.close();
    _connectionTypeController.close();
  }
}

/// Network quality enumeration
enum NetworkQuality {
  unknown,
  offline,
  poor,
  fair,
  good,
  excellent,
}

/// Network speed test result
class NetworkSpeedTest {
  final DateTime timestamp;
  final int latencyMs;
  final double downloadSpeedKbps;
  final List<ConnectivityResult> connectionTypes;

  NetworkSpeedTest({
    required this.timestamp,
    required this.latencyMs,
    required this.downloadSpeedKbps,
    required this.connectionTypes,
  });
}

/// Network statistics
class NetworkStats {
  final bool isConnected;
  final List<ConnectivityResult> connectionTypes;
  final NetworkQuality networkQuality;
  final List<NetworkSpeedTest> speedTests;
  final double averageLatencyMs;

  NetworkStats({
    required this.isConnected,
    required this.connectionTypes,
    required this.networkQuality,
    required this.speedTests,
    required this.averageLatencyMs,
  });
}

/// Network exception
class NetworkException implements Exception {
  final String message;

  NetworkException(this.message);

  @override
  String toString() => 'NetworkException: $message';
}
