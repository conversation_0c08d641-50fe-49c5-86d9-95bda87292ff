import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'session_manager.dart';

/// Enhanced authentication service with persistent login capabilities
class PersistentAuthService {
  static final PersistentAuthService _instance = PersistentAuthService._internal();
  factory PersistentAuthService() => _instance;
  PersistentAuthService._internal();

  // Storage keys
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userIdKey = 'user_id';
  static const String _userNameKey = 'user_name';
  static const String _rememberMeKey = 'remember_me';
  static const String _lastTokenRefreshKey = 'last_token_refresh';
  static const String _loginCredentialsKey = 'login_credentials';
  static const String _explicitLogoutKey = 'explicit_logout_flag'; // Flag to track explicit logout
  static const String _forcedLogoutKey = 'forced_logout_flag'; // New flag for forced logout

  // Secure storage configuration
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      sharedPreferencesName: 'kft_secure_prefs',
      preferencesKeyPrefix: 'kft_',
    ),
    iOptions: IOSOptions(
      groupId: 'group.com.example.kft',
      accountName: 'KFT_Auth',
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // State management
  bool _isAuthenticated = false;
  bool _isInitialized = false;
  bool _isLoggingOut = false; // Flag to prevent auto-login during logout
  String? _currentToken;
  String? _currentRefreshToken;
  Timer? _tokenRefreshTimer;
  Timer? _backgroundValidationTimer;

  // Stream controllers
  final StreamController<bool> _authStateController = StreamController<bool>.broadcast();
  final StreamController<String> _tokenController = StreamController<String>.broadcast();

  // Getters
  bool get isAuthenticated => _isAuthenticated;
  bool get isInitialized => _isInitialized;
  Stream<bool> get authStateStream => _authStateController.stream;
  Stream<String> get tokenStream => _tokenController.stream;

  /// Initialize the authentication service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🔐 Initializing PersistentAuthService...');

      // Quick initialization - load stored data only
      await _loadStoredAuthData();

      // Check explicit logout flag quickly
      final wasExplicitlyLoggedOut = await _wasExplicitlyLoggedOut();
      if (wasExplicitlyLoggedOut) {
        debugPrint('🚪 User was explicitly logged out - skipping auto-authentication');
        _isAuthenticated = false;
      }

      _isInitialized = true;
      debugPrint('✅ PersistentAuthService initialized successfully');

      // Perform heavy operations in background
      if (_isAuthenticated && !wasExplicitlyLoggedOut) {
        _initializeBackgroundServices();
      }
    } catch (e) {
      debugPrint('❌ Error initializing PersistentAuthService: $e');
      _isInitialized = true; // Mark as initialized even on error
    }
  }

  /// Initialize background services after quick startup
  Future<void> _initializeBackgroundServices() async {
    try {
      // Validate token in background
      await _validateStoredToken();

      // Start background validation
      _startBackgroundValidation();

      // Listen to app lifecycle changes
      _setupAppLifecycleListener();
    } catch (e) {
      debugPrint('❌ Error initializing background services: $e');
    }
  }

  /// Load stored authentication data from secure storage
  Future<void> _loadStoredAuthData() async {
    try {
      _currentToken = await _secureStorage.read(key: _tokenKey);
      _currentRefreshToken = await _secureStorage.read(key: _refreshTokenKey);

      if (_currentToken != null) {
        _isAuthenticated = true;
        debugPrint('🔑 Loaded stored authentication token');
      }
    } catch (e) {
      debugPrint('❌ Error loading stored auth data: $e');
      await _clearAllAuthData();
    }
  }

  /// Validate the stored token
  Future<bool> _validateStoredToken() async {
    if (_currentToken == null) return false;

    try {
      // Check if token is expired locally first
      if (_isTokenExpiredLocally(_currentToken!)) {
        debugPrint('🕐 Token expired locally, attempting refresh...');
        return await _refreshTokenIfNeeded();
      }

      // Validate with server
      final isValid = await _validateTokenWithServer(_currentToken!);
      if (!isValid) {
        debugPrint('❌ Token validation failed, attempting refresh...');
        return await _refreshTokenIfNeeded();
      }

      debugPrint('✅ Token validation successful');
      return true;
    } catch (e) {
      debugPrint('❌ Error validating token: $e');
      return await _refreshTokenIfNeeded();
    }
  }

  /// Check if token is expired locally (without server call)
  bool _isTokenExpiredLocally(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return true;

      // Decode payload
      String payload = parts[1];
      payload = payload.replaceAll('-', '+').replaceAll('_', '/');

      // Add padding if needed
      while (payload.length % 4 != 0) {
        payload += '=';
      }

      final decoded = utf8.decode(base64.decode(payload));
      final Map<String, dynamic> data = jsonDecode(decoded);

      if (data['exp'] != null) {
        final expiry = DateTime.fromMillisecondsSinceEpoch(data['exp'] * 1000);
        final now = DateTime.now();

        // Consider token expired if it expires within the next 5 minutes
        final bufferTime = const Duration(minutes: 5);
        return expiry.isBefore(now.add(bufferTime));
      }
    } catch (e) {
      debugPrint('❌ Error parsing token expiry: $e');
    }
    return true; // Assume expired if we can't parse
  }

  /// Validate token with server
  Future<bool> _validateTokenWithServer(String token) async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.defaultApiBaseUrl}/profile.php'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('❌ Server token validation failed: $e');
      return false;
    }
  }

  /// Refresh token if needed
  Future<bool> _refreshTokenIfNeeded() async {
    if (_currentRefreshToken == null) {
      debugPrint('❌ No refresh token available');
      return await _attemptSilentReauth();
    }
    try {
      debugPrint('🔄 Attempting token refresh...');
      final response = await http.post(
        Uri.parse('${AppConfig.defaultApiBaseUrl}/refresh_token.php'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'refresh_token': _currentRefreshToken,
        }),
      ).timeout(const Duration(seconds: 15));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true && data['token'] != null) {
          await _saveNewToken(data['token'], data['refresh_token']);
          debugPrint('✅ Token refreshed successfully');
          return true;
        }
        // Only clear tokens if backend says user deleted/inactive
        if (data['error'] == 'user_deleted' || data['error'] == 'inactive') {
          await logout();
          return false;
        }
      }
    } catch (e) {
      debugPrint('❌ Token refresh failed: $e');
      // Do NOT clear tokens on network/backend errors
      return false;
    }
    // If backend did not say user deleted/inactive, do NOT clear tokens
    return false;
  }

  /// Attempt silent re-authentication using stored credentials
  Future<bool> _attemptSilentReauth() async {
    if (_isLoggingOut) {
      debugPrint('❌ Cannot perform silent reauth during logout');
      return false;
    }
    try {
      final rememberMe = await _getRememberMeStatus();
      if (!rememberMe) {
        debugPrint('❌ Remember me disabled, cannot silent reauth');
        await logout();
        return false;
      }
      final credentials = await _getStoredCredentials();
      if (credentials == null) {
        debugPrint('❌ No stored credentials for silent reauth');
        await logout();
        return false;
      }
      debugPrint('🔄 Attempting silent re-authentication...');
      final response = await http.post(
        Uri.parse('${AppConfig.defaultApiBaseUrl}/login.php'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'username': credentials['username'],
          'pin': credentials['pin'],
          'device_id': credentials['device_id'],
          'silent_reauth': true,
        }),
      ).timeout(const Duration(seconds: 15));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true && data['token'] != null) {
          await _saveNewToken(data['token'], data['refresh_token']);
          debugPrint('✅ Silent re-authentication successful');
          return true;
        }
        // Only clear tokens if backend says user deleted/inactive
        if (data['error'] == 'user_deleted' || data['error'] == 'inactive') {
          await logout();
          return false;
        }
      }
    } catch (e) {
      debugPrint('❌ Silent re-authentication failed: $e');
      // Do NOT clear tokens on network/backend errors
      return false;
    }
    // If backend did not say user deleted/inactive, do NOT clear tokens
    return false;
  }

  /// Save new token and refresh token
  Future<void> _saveNewToken(String? token, String? refreshToken) async {
    if (token == null) {
      debugPrint('❌ Cannot save null token');
      throw ArgumentError('Token cannot be null');
    }

    _currentToken = token;
    _currentRefreshToken = refreshToken;

    await _secureStorage.write(key: _tokenKey, value: token);
    if (refreshToken != null) {
      await _secureStorage.write(key: _refreshTokenKey, value: refreshToken);
    }

    await _secureStorage.write(key: _lastTokenRefreshKey, value: DateTime.now().toIso8601String());

    _tokenController.add(token);
    _scheduleTokenRefresh();
  }

  /// Schedule automatic token refresh
  void _scheduleTokenRefresh() {
    _tokenRefreshTimer?.cancel();

    if (_currentToken == null) return;

    try {
      final parts = _currentToken!.split('.');
      if (parts.length != 3) return;

      String payload = parts[1];
      payload = payload.replaceAll('-', '+').replaceAll('_', '/');
      while (payload.length % 4 != 0) {
        payload += '=';
      }

      final decoded = utf8.decode(base64.decode(payload));
      final Map<String, dynamic> data = jsonDecode(decoded);

      if (data['exp'] != null) {
        final expiry = DateTime.fromMillisecondsSinceEpoch(data['exp'] * 1000);
        final now = DateTime.now();

        // Schedule refresh 10 minutes before expiry
        final refreshTime = expiry.subtract(const Duration(minutes: 10));
        final delay = refreshTime.difference(now);

        if (delay.isNegative) {
          // Token expires soon, refresh immediately
          _refreshTokenIfNeeded();
        } else {
          debugPrint('⏰ Scheduling token refresh in ${delay.inMinutes} minutes');
          _tokenRefreshTimer = Timer(delay, () => _refreshTokenIfNeeded());
        }
      }
    } catch (e) {
      debugPrint('❌ Error scheduling token refresh: $e');
    }
  }

  /// Start background validation timer
  void _startBackgroundValidation() {
    _backgroundValidationTimer?.cancel();

    // Validate token every 30 minutes
    _backgroundValidationTimer = Timer.periodic(
      const Duration(minutes: 30),
      (_) => _validateStoredToken(),
    );
  }

  /// Setup app lifecycle listener
  void _setupAppLifecycleListener() {
    SystemChannels.lifecycle.setMessageHandler((message) async {
      if (message == AppLifecycleState.resumed.toString() && _isAuthenticated) {
        debugPrint('📱 App resumed, validating authentication...');
        await _validateStoredToken();
      }
      return null;
    });
  }

  /// Login with persistent session support
  Future<Map<String, dynamic>> login({
    String? username,
    String? phoneNumber,
    required String pin,
    bool rememberMe = true,
    String? deviceId,
  }) async {
    try {
      debugPrint('🔐 Attempting login with persistent session...');

      // Ensure either username or phoneNumber is provided
      if (username == null && phoneNumber == null) {
        throw Exception('Either username or phone number must be provided');
      }

      final response = await http.post(
        Uri.parse('${AppConfig.defaultApiBaseUrl}/login.php'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          if (username != null) 'username': username,
          if (phoneNumber != null) 'phone_number': phoneNumber,
          'pin': pin,
          'device_id': deviceId ?? await _getDeviceId(),
          'remember_me': rememberMe,
          'extended_session': true, // Request extended session
        }),
      ).timeout(const Duration(seconds: 30));

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        // Validate required fields - check for both 'token' and 'access_token'
        String? token = data['access_token'] ?? data['token'];
        if (token == null) {
          debugPrint('❌ Login failed: Missing token from server');
          debugPrint('❌ Response data: $data');
          return {
            'success': false,
            'error': 'Server error: Missing authentication token'
          };
        }

        // Save authentication data
        await _saveNewToken(token, data['refresh_token']);

        // Save user data if available
        if (data['user'] != null) {
          await _saveUserData(data['user']);
        }

        // Save remember me preference
        await _setRememberMeStatus(rememberMe);

        // Save credentials for silent reauth if remember me is enabled
        if (rememberMe) {
          await _saveCredentials(username, pin, deviceId);
        }

        // Clear explicit logout flag on successful login
        await _setExplicitLogoutFlag(false);

        // Handle forced logout flag
        if (data['forced_logout'] == true) {
          await _setForcedLogoutFlag(true);
        } else {
          await _clearForcedLogoutFlag();
        }

        _isAuthenticated = true;
        _authStateController.add(true);

        // Start background services
        _startBackgroundValidation();
        _scheduleTokenRefresh();

        // Notify session manager of successful login
        try {
          final sessionManager = SessionManager();
          if (sessionManager.isInitialized) {
            await sessionManager.onLoginSuccess(
              userId: data['user']['id'].toString(),
              token: data['token'],
              serverResponse: data,
            );
          }
        } catch (e) {
          debugPrint('⚠️ Session manager notification failed: $e');
          // Don't fail login if session manager has issues
        }

        debugPrint('✅ Login successful with persistent session');
        return {'success': true, 'user': data['user']};
      } else {
        debugPrint('❌ Login failed: [31m${data['error'] ?? 'Unknown error'}[0m');
        return {'success': false, 'error': data['error'] ?? 'Login failed'};
      }
    } catch (e) {
      debugPrint('❌ Login error: $e');
      return {'success': false, 'error': 'Network error occurred'};
    }
  }

  /// Logout and clear all authentication data
  Future<void> logout({bool clearRememberMe = false}) async {
    try {
      debugPrint('🚪 Logging out...');

      // Set logout flag to prevent auto-login
      _isLoggingOut = true;

      // Set explicit logout flag to prevent future auto-authentication
      await _setExplicitLogoutFlag(true);

      // Cancel timers
      _tokenRefreshTimer?.cancel();
      _backgroundValidationTimer?.cancel();

      // Clear authentication state
      _isAuthenticated = false;
      _currentToken = null;
      _currentRefreshToken = null;

      // Clear stored data
      await _clearAllAuthData(clearRememberMe: clearRememberMe);

      // Notify listeners
      _authStateController.add(false);

      // Notify session manager of logout
      try {
        final sessionManager = SessionManager();
        if (sessionManager.isInitialized) {
          await sessionManager.handleExternalLogout();
        }
      } catch (e) {
        debugPrint('⚠️ Session manager logout notification failed: $e');
      }

      // Keep logout flag active for a longer period to prevent immediate re-auth
      Timer(const Duration(seconds: 5), () {
        _isLoggingOut = false;
      });

      debugPrint('✅ Logout completed - explicit logout flag set');
    } catch (e) {
      debugPrint('❌ Error during logout: $e');
      _isLoggingOut = false; // Reset flag on error
    }
  }

  /// Get current authentication token
  Future<String?> getToken() async {
    if (!_isAuthenticated || _currentToken == null) {
      return null;
    }

    // Check if token needs refresh
    if (_isTokenExpiredLocally(_currentToken!)) {
      final refreshed = await _refreshTokenIfNeeded();
      if (!refreshed) {
        return null;
      }
    }

    return _currentToken;
  }

  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    // Don't perform validation if we're logging out
    if (_isLoggingOut) {
      return false;
    }

    // Check if user was explicitly logged out
    final wasExplicitlyLoggedOut = await _wasExplicitlyLoggedOut();
    if (wasExplicitlyLoggedOut) {
      return false;
    }

    if (!_isAuthenticated || _currentToken == null) {
      return false;
    }

    // Validate token if needed
    return await _validateStoredToken();
  }

  /// Get stored user data
  Future<Map<String, dynamic>?> getUserData() async {
    try {
      final userId = await _secureStorage.read(key: _userIdKey);
      final userName = await _secureStorage.read(key: _userNameKey);

      if (userId != null && userName != null) {
        return {
          'id': int.tryParse(userId),
          'name': userName,
        };
      }
    } catch (e) {
      debugPrint('❌ Error getting user data: $e');
    }
    return null;
  }

  /// Save user data
  Future<void> _saveUserData(Map<String, dynamic> userData) async {
    try {
      if (userData['id'] != null) {
        await _secureStorage.write(key: _userIdKey, value: userData['id'].toString());
      }
      if (userData['name'] != null) {
        await _secureStorage.write(key: _userNameKey, value: userData['name']);
      }
    } catch (e) {
      debugPrint('❌ Error saving user data: $e');
    }
  }

  /// Save login credentials for silent reauth
  Future<void> _saveCredentials(String username, String pin, String? deviceId) async {
    try {
      final credentials = {
        'username': username,
        'pin': pin,
        'device_id': deviceId ?? await _getDeviceId(),
        'saved_at': DateTime.now().toIso8601String(),
      };

      await _secureStorage.write(
        key: _loginCredentialsKey,
        value: jsonEncode(credentials),
      );
    } catch (e) {
      debugPrint('❌ Error saving credentials: $e');
    }
  }

  /// Get stored credentials
  Future<Map<String, dynamic>?> _getStoredCredentials() async {
    try {
      final credentialsJson = await _secureStorage.read(key: _loginCredentialsKey);
      if (credentialsJson != null) {
        return jsonDecode(credentialsJson);
      }
    } catch (e) {
      debugPrint('❌ Error getting stored credentials: $e');
    }
    return null;
  }

  /// Set remember me status
  Future<void> _setRememberMeStatus(bool rememberMe) async {
    try {
      await _secureStorage.write(key: _rememberMeKey, value: rememberMe.toString());
    } catch (e) {
      debugPrint('❌ Error setting remember me status: $e');
    }
  }

  /// Get remember me status
  Future<bool> _getRememberMeStatus() async {
    try {
      final status = await _secureStorage.read(key: _rememberMeKey);
      return status == 'true';
    } catch (e) {
      debugPrint('❌ Error getting remember me status: $e');
      return false;
    }
  }

  /// Get device ID
  Future<String> _getDeviceId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? deviceId = prefs.getString('device_id');

      if (deviceId == null) {
        deviceId = DateTime.now().millisecondsSinceEpoch.toString();
        await prefs.setString('device_id', deviceId);
      }

      return deviceId;
    } catch (e) {
      debugPrint('❌ Error getting device ID: $e');
      return DateTime.now().millisecondsSinceEpoch.toString();
    }
  }

  /// Set explicit logout flag
  Future<void> _setExplicitLogoutFlag(bool value) async {
    try {
      await _secureStorage.write(key: _explicitLogoutKey, value: value.toString());
    } catch (e) {
      debugPrint('❌ Error setting explicit logout flag: $e');
    }
  }

  /// Check if user was explicitly logged out
  Future<bool> _wasExplicitlyLoggedOut() async {
    try {
      final flag = await _secureStorage.read(key: _explicitLogoutKey);
      return flag == 'true';
    } catch (e) {
      debugPrint('❌ Error checking explicit logout flag: $e');
      return false;
    }
  }

  /// Clear all authentication data
  Future<void> _clearAllAuthData({bool clearRememberMe = false}) async {
    try {
      await _secureStorage.delete(key: _tokenKey);
      await _secureStorage.delete(key: _refreshTokenKey);
      await _secureStorage.delete(key: _userIdKey);
      await _secureStorage.delete(key: _userNameKey);
      await _secureStorage.delete(key: _lastTokenRefreshKey);

      if (clearRememberMe) {
        await _secureStorage.delete(key: _rememberMeKey);
        await _secureStorage.delete(key: _loginCredentialsKey);
      }

      // Note: We don't clear the explicit logout flag here as it should persist
      // until the user manually logs in again
    } catch (e) {
      debugPrint('❌ Error clearing auth data: $e');
    }
  }

  /// Set forced logout flag
  Future<void> _setForcedLogoutFlag(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_forcedLogoutKey, value);
  }

  /// Get forced logout flag
  Future<bool> _getForcedLogoutFlag() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_forcedLogoutKey) ?? false;
  }

  /// Clear forced logout flag
  Future<void> _clearForcedLogoutFlag() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_forcedLogoutKey);
  }

  /// Dispose resources
  void dispose() {
    _tokenRefreshTimer?.cancel();
    _backgroundValidationTimer?.cancel();
    _authStateController.close();
    _tokenController.close();
  }
}

/// Call this from a widget (e.g., in your main app or auth wrapper)
Future<void> showForcedLogoutDialogIfNeeded(BuildContext context, PersistentAuthService authService) async {
  final forcedLogout = await authService._getForcedLogoutFlag();
  if (forcedLogout) {
    var connectivityResult = await (Connectivity().checkConnectivity());
    if (connectivityResult != ConnectivityResult.none) {
      await showDialog(
        context: context,
        builder: (_) => AlertDialog(
          title: Text('Logged Out'),
          content: Text('You were logged out due to a new login on another device.'),
          actions: [
            TextButton(
              onPressed: () async {
                await authService._clearForcedLogoutFlag();
                Navigator.of(context).pop();
                await authService.logout();
              },
              child: Text('OK'),
            ),
          ],
        ),
      );
    }
  }
}
