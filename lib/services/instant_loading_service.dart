import 'dart:async';
import 'dart:html' as html;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// ⚡ Instant Loading Service ⚡
/// Optimizes app startup for lightning-fast first load
class InstantLoadingService {
  static final InstantLoadingService _instance = InstantLoadingService._internal();
  factory InstantLoadingService() => _instance;
  InstantLoadingService._internal();

  bool _isInitialized = false;
  bool _isFirstLoad = true;
  final Completer<void> _initCompleter = Completer<void>();

  /// Initialize instant loading optimizations
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    debugPrint('⚡ Initializing Instant Loading Service...');
    
    try {
      // Start all optimizations in parallel
      await Future.wait([
        _optimizeWebPerformance(),
        _preloadCriticalAssets(),
        _optimizeSystemUI(),
        _setupPerformanceMonitoring(),
      ]);
      
      _isInitialized = true;
      _initCompleter.complete();
      
      debugPrint('✅ Instant Loading Service initialized');
    } catch (e) {
      debugPrint('❌ Error initializing Instant Loading Service: $e');
      _initCompleter.completeError(e);
    }
  }

  /// Wait for initialization to complete
  Future<void> waitForInitialization() => _initCompleter.future;

  /// Optimize web performance
  Future<void> _optimizeWebPerformance() async {
    if (!kIsWeb) return;

    try {
      // Hide splash screen when Flutter is ready
      html.window.dispatchEvent(html.CustomEvent('flutter-first-frame'));
      
      // Optimize viewport
      final viewport = html.document.querySelector('meta[name="viewport"]');
      if (viewport != null) {
        viewport.setAttribute('content', 
          'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover');
      }
      
      // Preconnect to critical domains (HTTP is sufficient for loading)
      _addPreconnectLink('http://player.vimeo.com');
      _addPreconnectLink('http://vimeo.com');
      _addPreconnectLink('http://i.vimeocdn.com');
      
      debugPrint('✅ Web performance optimized');
    } catch (e) {
      debugPrint('⚠️ Web performance optimization failed: $e');
    }
  }

  /// Add preconnect link for faster domain connections
  void _addPreconnectLink(String href) {
    if (!kIsWeb) return;
    
    final link = html.LinkElement()
      ..rel = 'preconnect'
      ..href = href
      ..setAttribute('crossorigin', '');
    html.document.head?.append(link);
  }

  /// Preload critical assets
  Future<void> _preloadCriticalAssets() async {
    try {
      // Preload critical images
      final criticalImages = [
        'assets/assets/images/logo.webp',
        'icons/Icon-192.png',
        'favicon.png',
      ];
      
      for (final imagePath in criticalImages) {
        _preloadImage(imagePath);
      }
      
      debugPrint('✅ Critical assets preloaded');
    } catch (e) {
      debugPrint('⚠️ Asset preloading failed: $e');
    }
  }

  /// Preload image for instant display
  void _preloadImage(String path) {
    if (!kIsWeb) return;
    
    final img = html.ImageElement()..src = path;
    // Image will be cached by browser
  }

  /// Optimize system UI for instant display
  Future<void> _optimizeSystemUI() async {
    try {
      // Set system UI overlay style for instant display
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.light,
        ),
      );
      
      // Set preferred orientations
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
      
      debugPrint('✅ System UI optimized');
    } catch (e) {
      debugPrint('⚠️ System UI optimization failed: $e');
    }
  }

  /// Setup performance monitoring
  Future<void> _setupPerformanceMonitoring() async {
    try {
      // Monitor frame rendering performance
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _measureFramePerformance();
      });
      
      debugPrint('✅ Performance monitoring setup');
    } catch (e) {
      debugPrint('⚠️ Performance monitoring setup failed: $e');
    }
  }

  /// Measure frame rendering performance
  void _measureFramePerformance() {
    if (!kDebugMode) return;
    
    final stopwatch = Stopwatch()..start();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      stopwatch.stop();
      final frameTime = stopwatch.elapsedMilliseconds;
      
      if (frameTime > 16) { // 60fps = 16.67ms per frame
        debugPrint('⚠️ Slow frame detected: ${frameTime}ms');
      }
    });
  }

  /// Mark first load as complete
  void markFirstLoadComplete() {
    _isFirstLoad = false;
    debugPrint('✅ First load completed');
  }

  /// Check if this is the first load
  bool get isFirstLoad => _isFirstLoad;

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Optimize widget build performance
  Widget optimizeWidget(Widget child) {
    return RepaintBoundary(
      child: child,
    );
  }

  /// Create optimized material app
  Widget createOptimizedApp({
    required Widget home,
    required ThemeData theme,
    ThemeData? darkTheme,
    String title = 'KFT Fitness',
  }) {
    return MaterialApp(
      title: title,
      theme: theme,
      darkTheme: darkTheme,
      home: optimizeWidget(home),
      debugShowCheckedModeBanner: false,
      
      // Performance optimizations
      builder: (context, child) {
        return MediaQuery(
          // Prevent font scaling for consistent UI
          data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
          child: child!,
        );
      },
      
      // Optimize route transitions
      onGenerateRoute: (settings) {
        return PageRouteBuilder(
          settings: settings,
          pageBuilder: (context, animation, secondaryAnimation) => home,
          transitionDuration: const Duration(milliseconds: 200),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
        );
      },
    );
  }

  /// Dispose resources
  void dispose() {
    debugPrint('🧹 Instant Loading Service disposed');
  }
}

/// Extension for instant loading optimizations
extension InstantLoadingExtension on Widget {
  /// Wrap widget with instant loading optimizations
  Widget withInstantLoading() {
    return InstantLoadingService().optimizeWidget(this);
  }
}

/// Instant loading mixin for StatefulWidgets
mixin InstantLoadingMixin<T extends StatefulWidget> on State<T> {
  @override
  void initState() {
    super.initState();
    
    // Optimize widget for instant loading
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _optimizeForInstantLoading();
      }
    });
  }

  void _optimizeForInstantLoading() {
    // Override in subclasses for specific optimizations
  }
}
