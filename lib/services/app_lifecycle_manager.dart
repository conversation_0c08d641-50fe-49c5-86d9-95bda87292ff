import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'performance_monitor.dart';
import 'connectivity_service.dart';
import 'optimized_image_service.dart';
import 'optimized_video_service.dart';
import 'persistent_auth_service.dart';

/// Production-grade app lifecycle manager
/// Handles app state changes, background tasks, and resource optimization
class AppLifecycleManager with WidgetsBindingObserver {
  static final AppLifecycleManager _instance = AppLifecycleManager._internal();
  factory AppLifecycleManager() => _instance;
  AppLifecycleManager._internal();

  // Services
  final PerformanceMonitor _performanceMonitor = PerformanceMonitor();
  final ConnectivityService _connectivityService = ConnectivityService();
  final OptimizedImageService _imageService = OptimizedImageService();
  final OptimizedVideoService _videoService = OptimizedVideoService();
  final PersistentAuthService _authService = PersistentAuthService();

  // State tracking
  AppLifecycleState _currentState = AppLifecycleState.resumed;
  DateTime? _backgroundTime;
  Timer? _backgroundTimer;
  bool _isInitialized = false;

  // Configuration
  static const Duration _backgroundTimeout = Duration(minutes: 30);
  static const Duration _memoryCleanupDelay = Duration(seconds: 5);

  /// Initialize lifecycle manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Register as lifecycle observer
      WidgetsBinding.instance.addObserver(this);

      // Initialize services
      await _initializeServices();

      _isInitialized = true;
      debugPrint('🔄 AppLifecycleManager initialized');
    } catch (e) {
      debugPrint('❌ Error initializing AppLifecycleManager: $e');
    }
  }

  /// Initialize all services
  Future<void> _initializeServices() async {
    await Future.wait([
      _performanceMonitor.initialize(),
      _connectivityService.initialize(),
      _imageService.initialize(),
      _videoService.initialize(),
    ]);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    final previousState = _currentState;
    _currentState = state;

    debugPrint('🔄 App lifecycle changed: ${previousState.name} → ${state.name}');

    switch (state) {
      case AppLifecycleState.resumed:
        _handleAppResumed(previousState);
        break;
      case AppLifecycleState.paused:
        _handleAppPaused();
        break;
      case AppLifecycleState.inactive:
        _handleAppInactive();
        break;
      case AppLifecycleState.detached:
        _handleAppDetached();
        break;
      case AppLifecycleState.hidden:
        _handleAppHidden();
        break;
    }
  }

  /// Handle app resumed state
  Future<void> _handleAppResumed(AppLifecycleState previousState) async {
    _performanceMonitor.logEvent('app_resumed', {
      'previous_state': previousState.name,
      'background_duration_seconds': _backgroundTime != null
          ? DateTime.now().difference(_backgroundTime!).inSeconds
          : 0,
    });

    // Cancel background timer
    _backgroundTimer?.cancel();
    _backgroundTimer = null;

    // Check if app was in background for a long time
    if (_backgroundTime != null) {
      final backgroundDuration = DateTime.now().difference(_backgroundTime!);

      if (backgroundDuration > _backgroundTimeout) {
        await _handleLongBackgroundReturn();
      } else {
        await _handleShortBackgroundReturn();
      }
    }

    _backgroundTime = null;

    // Refresh authentication if needed
    await _refreshAuthenticationIfNeeded();

    // Resume connectivity monitoring
    if (!_connectivityService.isConnected) {
      await _connectivityService.initialize();
    }
  }

  /// Handle app paused state
  Future<void> _handleAppPaused() async {
    _backgroundTime = DateTime.now();

    _performanceMonitor.logEvent('app_paused', {
      'timestamp': _backgroundTime!.millisecondsSinceEpoch,
    });

    // Start background timer for cleanup
    _startBackgroundTimer();

    // Pause video playback
    await _pauseAllVideos();

    // Optimize memory after a delay
    Timer(_memoryCleanupDelay, () {
      _optimizeMemoryForBackground();
    });
  }

  /// Handle app inactive state
  void _handleAppInactive() {
    _performanceMonitor.logEvent('app_inactive', {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });

    // Prepare for potential backgrounding
    _prepareForBackground();
  }

  /// Handle app hidden state
  void _handleAppHidden() {
    _performanceMonitor.logEvent('app_hidden', {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  /// Handle app detached state
  Future<void> _handleAppDetached() async {
    _performanceMonitor.logEvent('app_detached', {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });

    // Cleanup all resources
    await _cleanupResources();
  }

  /// Handle return from long background
  Future<void> _handleLongBackgroundReturn() async {
    debugPrint('🔄 App returned from long background, performing full refresh');

    // Clear caches to free memory
    await _imageService.clearCache();

    // Reinitialize services
    await _initializeServices();

    // Trigger memory optimization
    _performanceMonitor.logEvent('long_background_return', {
      'background_duration_minutes': DateTime.now().difference(_backgroundTime!).inMinutes,
    });
  }

  /// Handle return from short background
  Future<void> _handleShortBackgroundReturn() async {
    debugPrint('🔄 App returned from short background, performing light refresh');

    // Light refresh - just check connectivity
    await _connectivityService.initialize();

    _performanceMonitor.logEvent('short_background_return', {
      'background_duration_seconds': DateTime.now().difference(_backgroundTime!).inSeconds,
    });
  }

  /// Start background timer for cleanup
  void _startBackgroundTimer() {
    _backgroundTimer = Timer(_backgroundTimeout, () {
      debugPrint('🔄 Background timeout reached, performing cleanup');
      _performBackgroundCleanup();
    });
  }

  /// Perform background cleanup
  Future<void> _performBackgroundCleanup() async {
    try {
      // Clear image caches
      await _imageService.clearCache();

      // Dispose unused video controllers
      await _videoService.dispose();

      // Clear performance metrics
      _performanceMonitor.logEvent('background_cleanup', {
        'cleanup_time': DateTime.now().millisecondsSinceEpoch,
      });

      debugPrint('🧹 Background cleanup completed');
    } catch (e) {
      debugPrint('❌ Error during background cleanup: $e');
    }
  }

  /// Prepare for background
  void _prepareForBackground() {
    // Pause any ongoing operations
    _pauseNonEssentialOperations();
  }

  /// Pause all video playback
  Future<void> _pauseAllVideos() async {
    try {
      // This would pause all active video controllers
      // Implementation depends on video service design
      debugPrint('⏸️ Pausing all video playback');
    } catch (e) {
      debugPrint('⚠️ Error pausing videos: $e');
    }
  }

  /// Pause non-essential operations
  void _pauseNonEssentialOperations() {
    // Pause background tasks, animations, etc.
    debugPrint('⏸️ Pausing non-essential operations');
  }

  /// Optimize memory for background
  void _optimizeMemoryForBackground() {
    try {
      // Clear image cache
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();

      // Trigger garbage collection
      _triggerGarbageCollection();

      _performanceMonitor.logEvent('background_memory_optimization', {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });

      debugPrint('🧹 Memory optimized for background');
    } catch (e) {
      debugPrint('⚠️ Error optimizing memory: $e');
    }
  }

  /// Trigger garbage collection
  void _triggerGarbageCollection() {
    try {
      const platform = MethodChannel('com.kft.fitness/performance');
      platform.invokeMethod('triggerGC');
    } catch (e) {
      // Fallback to Dart GC
      debugPrint('⚠️ Native GC failed, using Dart GC: $e');
    }
  }

  /// Refresh authentication if needed
  Future<void> _refreshAuthenticationIfNeeded() async {
    try {
      final isLoggedIn = await _authService.isLoggedIn();
      if (isLoggedIn) {
        // Check if token needs refresh
        // Note: refreshTokenIfNeeded method would need to be implemented in PersistentAuthService
        // For now, we'll just check if logged in
        debugPrint('🔐 Checking authentication status');
      }
    } catch (e) {
      debugPrint('⚠️ Error refreshing authentication: $e');
    }
  }

  /// Cleanup all resources
  Future<void> _cleanupResources() async {
    try {
      await Future.wait([
        _imageService.clearCache(),
        _videoService.dispose(),
      ]);

      _performanceMonitor.dispose();
      _connectivityService.dispose();

      debugPrint('🧹 All resources cleaned up');
    } catch (e) {
      debugPrint('❌ Error cleaning up resources: $e');
    }
  }

  /// Get current app state
  AppLifecycleState get currentState => _currentState;

  /// Check if app is in foreground
  bool get isInForeground => _currentState == AppLifecycleState.resumed;

  /// Check if app is in background
  bool get isInBackground => _currentState == AppLifecycleState.paused;

  /// Get time spent in background
  Duration? get backgroundDuration {
    if (_backgroundTime != null) {
      return DateTime.now().difference(_backgroundTime!);
    }
    return null;
  }

  /// Get lifecycle statistics
  Map<String, dynamic> getLifecycleStats() {
    return {
      'currentState': _currentState.name,
      'isInForeground': isInForeground,
      'isInBackground': isInBackground,
      'backgroundDuration': backgroundDuration?.inSeconds,
      'isInitialized': _isInitialized,
    };
  }

  /// Dispose lifecycle manager
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _backgroundTimer?.cancel();
    _cleanupResources();
    _isInitialized = false;
    debugPrint('🔄 AppLifecycleManager disposed');
  }
}
