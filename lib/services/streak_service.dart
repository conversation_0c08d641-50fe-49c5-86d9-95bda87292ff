import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/timezone.dart' as tz;
import 'dart:convert';
import '../services/api_service.dart';
import '../services/real_notification_service.dart';

class StreakService {
  static final StreakService _instance = StreakService._internal();
  factory StreakService() => _instance;
  StreakService._internal();

  static const String _streakDataKey = 'streak_data';
  static const String _lastCompletionKey = 'last_completion_date';

  Map<String, DayCompletion> _completions = {};
  int _currentStreak = 0;
  DateTime? _lastCompletionDate;

  // Getters
  Map<String, DayCompletion> get completions => _completions;
  int get currentStreak => _currentStreak;
  DateTime? get lastCompletionDate => _lastCompletionDate;

  // Initialize streak service
  Future<void> initialize() async {
    await _loadStreakData();
    await _calculateCurrentStreak();
  }

  // Load streak data from local storage
  Future<void> _loadStreakData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final streakDataJson = prefs.getString(_streakDataKey);
      final lastCompletionMillis = prefs.getInt(_lastCompletionKey);

      if (streakDataJson != null) {
        final Map<String, dynamic> data = jsonDecode(streakDataJson);
        _completions = data.map((key, value) =>
            MapEntry(key, DayCompletion.fromJson(value)));
      }

      if (lastCompletionMillis != null) {
        _lastCompletionDate = DateTime.fromMillisecondsSinceEpoch(lastCompletionMillis);
      }
    } catch (e) {
      print('Error loading streak data: $e');
      _completions = {};
    }
  }

  // Save streak data to local storage
  Future<void> _saveStreakData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final streakDataJson = jsonEncode(
        _completions.map((key, value) => MapEntry(key, value.toJson())),
      );
      await prefs.setString(_streakDataKey, streakDataJson);

      if (_lastCompletionDate != null) {
        await prefs.setInt(_lastCompletionKey, _lastCompletionDate!.millisecondsSinceEpoch);
      }
    } catch (e) {
      print('Error saving streak data: $e');
    }
  }

  // Get device timezone aware current date
  DateTime get _currentDate {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day);
  }

  // Format date as string key
  String _formatDateKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // Get 30-day calendar data
  List<StreakDay> get30DayCalendar() {
    final today = _currentDate;
    final List<StreakDay> days = [];

    // Generate last 29 days + today (30 total)
    for (int i = 29; i >= 0; i--) {
      final date = today.subtract(Duration(days: i));
      final dateKey = _formatDateKey(date);
      final completion = _completions[dateKey];
      final isToday = i == 0;

      days.add(StreakDay(
        date: date,
        isToday: isToday,
        isCompleted: completion?.isCompleted ?? false,
        completionType: completion?.completionType,
        completedAt: completion?.completedAt,
        activities: completion?.activities ?? [],
      ));
    }

    return days;
  }

  // Mark today as completed with specific activity
  Future<bool> markTodayCompleted(CompletionType type, String activity) async {
    final today = _currentDate;
    final todayKey = _formatDateKey(today);

    // Check if already completed today
    if (_completions[todayKey]?.isCompleted == true) {
      // Add activity to existing completion
      _completions[todayKey]!.activities.add(activity);
    } else {
      // Create new completion
      _completions[todayKey] = DayCompletion(
        date: today,
        isCompleted: true,
        completionType: type,
        completedAt: DateTime.now(),
        activities: [activity],
      );
    }

    _lastCompletionDate = DateTime.now();
    await _saveStreakData();
    await _calculateCurrentStreak();

    // Sync with backend
    await _syncWithBackend();

    return true;
  }

  // Calculate current streak
  Future<void> _calculateCurrentStreak() async {
    final today = _currentDate;
    int streak = 0;

    // Count consecutive days from today backwards
    for (int i = 0; i < 365; i++) { // Max 365 days to prevent infinite loop
      final date = today.subtract(Duration(days: i));
      final dateKey = _formatDateKey(date);

      if (_completions[dateKey]?.isCompleted == true) {
        streak++;
      } else {
        break;
      }
    }

    _currentStreak = streak;
  }

  // Check if today is completed
  bool get isTodayCompleted {
    final todayKey = _formatDateKey(_currentDate);
    return _completions[todayKey]?.isCompleted ?? false;
  }

  // Get completion for specific date
  DayCompletion? getCompletionForDate(DateTime date) {
    final dateKey = _formatDateKey(date);
    return _completions[dateKey];
  }

  // Sync with backend API
  Future<void> _syncWithBackend() async {
    try {
      final apiService = ApiService();

      // Prepare streak data for API
      final streakData = {
        'current_streak': _currentStreak,
        'last_completion_date': _lastCompletionDate?.toIso8601String(),
        'completions': _completions.map((key, value) => MapEntry(key, value.toJson())),
      };

      // Send to backend
      await apiService.makeApiRequest(
        'streak.php',
        method: 'POST',
        data: streakData,
      );
    } catch (e) {
      // Do NOT clear tokens or logout on streak sync errors
      // Streak syncing is for analytics only and should not affect authentication
      print('Error syncing streak with backend: $e');
      // Continue without backend sync for now
    }
  }

  // Get motivational message for completion
  String getMotivationalMessage() {
    final messages = [
      "Great job! 'Your only competition is who you were yesterday.' - Keep the streak alive!",
      "Amazing! 'Success is the sum of small efforts repeated day in and day out.' - Keep going!",
      "Fantastic! 'The groundwork for all happiness is good health.' - You're building it!",
      "Excellent! 'Take care of your body. It's the only place you have to live.' - Well done!",
      "Outstanding! 'A healthy outside starts from the inside.' - Keep up the great work!",
      "Incredible! 'Health is not about the weight you lose, but about the life you gain.' - Amazing progress!",
      "Wonderful! 'Your body can do it. It's your mind you need to convince.' - You're proving it!",
      "Superb! 'Fitness is not about being better than someone else. It's about being better than you used to be.'",
      "Brilliant! 'The only bad workout is the one that didn't happen.' - You made it happen!",
      "Perfect! 'Strong is the new beautiful.' - You're getting stronger every day!",
    ];

    final today = DateTime.now();
    final index = (today.day + _currentStreak) % messages.length;
    return messages[index];
  }

  // Reset streak (for testing or admin purposes)
  Future<void> resetStreak() async {
    _completions.clear();
    _currentStreak = 0;
    _lastCompletionDate = null;
    await _saveStreakData();
  }

  // Get streak statistics
  StreakStats getStreakStats() {
    final calendar = get30DayCalendar();
    final completedDays = calendar.where((day) => day.isCompleted).length;
    final completionRate = (completedDays / 30.0 * 100).round();

    return StreakStats(
      currentStreak: _currentStreak,
      completedDaysLast30: completedDays,
      completionRate: completionRate,
      totalActivities: _completions.values
          .expand((completion) => completion.activities)
          .length,
    );
  }
}

// Data models
class StreakDay {
  final DateTime date;
  final bool isToday;
  final bool isCompleted;
  final CompletionType? completionType;
  final DateTime? completedAt;
  final List<String> activities;

  StreakDay({
    required this.date,
    required this.isToday,
    required this.isCompleted,
    this.completionType,
    this.completedAt,
    required this.activities,
  });
}

class DayCompletion {
  final DateTime date;
  final bool isCompleted;
  final CompletionType? completionType;
  final DateTime? completedAt;
  final List<String> activities;

  DayCompletion({
    required this.date,
    required this.isCompleted,
    this.completionType,
    this.completedAt,
    required this.activities,
  });

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'isCompleted': isCompleted,
      'completionType': completionType?.toString(),
      'completedAt': completedAt?.toIso8601String(),
      'activities': activities,
    };
  }

  factory DayCompletion.fromJson(Map<String, dynamic> json) {
    return DayCompletion(
      date: DateTime.parse(json['date']),
      isCompleted: json['isCompleted'] ?? false,
      completionType: json['completionType'] != null
          ? CompletionType.values.firstWhere(
              (e) => e.toString() == json['completionType'],
              orElse: () => CompletionType.workout,
            )
          : null,
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'])
          : null,
      activities: List<String>.from(json['activities'] ?? []),
    );
  }
}

class StreakStats {
  final int currentStreak;
  final int completedDaysLast30;
  final int completionRate;
  final int totalActivities;

  StreakStats({
    required this.currentStreak,
    required this.completedDaysLast30,
    required this.completionRate,
    required this.totalActivities,
  });
}

enum CompletionType {
  workout,
  water,
  course,
  nutrition,
  general,
}
