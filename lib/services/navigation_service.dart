import 'package:flutter/material.dart';

class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  DateTime? _lastNavigationTime;
  static const Duration _navigationCooldown = Duration(milliseconds: 500);

  // Route names as constants to prevent typos
  static const String login = '/login';
  static const String home = '/home';
  static const String settings = '/settings';
  static const String profile = '/profile';
  static const String progress = '/progress';
  // Removed notification and water reminder routes
  static const String systemHealth = '/system-health';

  bool _canNavigate() {
    final now = DateTime.now();
    if (_lastNavigationTime == null) {
      _lastNavigationTime = now;
      return true;
    }

    final timeSinceLastNavigation = now.difference(_lastNavigationTime!);
    if (timeSinceLastNavigation < _navigationCooldown) {
      return false;
    }

    _lastNavigationTime = now;
    return true;
  }

  Future<T?> pushNamed<T>(String routeName, {Object? arguments}) async {
    if (!_canNavigate()) return null;
    return navigatorKey.currentState?.pushNamed<T>(routeName, arguments: arguments);
  }

  Future<T?> pushReplacementNamed<T, TO>(String routeName, {Object? arguments, TO? result}) async {
    if (!_canNavigate()) return null;
    return navigatorKey.currentState?.pushReplacementNamed<T, TO>(routeName, arguments: arguments, result: result);
  }

  Future<T?> pushNamedAndRemoveUntil<T>(String routeName, RoutePredicate predicate, {Object? arguments}) async {
    if (!_canNavigate()) return null;
    return navigatorKey.currentState?.pushNamedAndRemoveUntil<T>(routeName, predicate, arguments: arguments);
  }

  void pop<T>([T? result]) {
    if (!_canNavigate()) return;
    navigatorKey.currentState?.pop<T>(result);
  }

  bool canPop() {
    return navigatorKey.currentState?.canPop() ?? false;
  }

  Future<bool> maybePop<T>([T? result]) async {
    if (!_canNavigate()) return false;
    return navigatorKey.currentState?.maybePop<T>(result) ?? false;
  }

  void popUntil(RoutePredicate predicate) {
    if (!_canNavigate()) return;
    navigatorKey.currentState?.popUntil(predicate);
  }

  Future<T?> push<T>(Route<T> route) async {
    if (!_canNavigate()) return null;
    return navigatorKey.currentState?.push<T>(route);
  }

  Future<T?> pushReplacement<T, TO>(Route<T> route, {TO? result}) async {
    if (!_canNavigate()) return null;
    return navigatorKey.currentState?.pushReplacement<T, TO>(route, result: result);
  }

  Future<T?> pushAndRemoveUntil<T>(Route<T> route, RoutePredicate predicate) async {
    if (!_canNavigate()) return null;
    return navigatorKey.currentState?.pushAndRemoveUntil<T>(route, predicate);
  }
} 