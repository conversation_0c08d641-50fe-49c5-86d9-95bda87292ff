import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'network_resilience_service.dart';
import 'device_compatibility_service.dart';

/// Bulletproof authentication service with persistent sessions and maximum compatibility
class BulletproofAuthService {
  static final BulletproofAuthService _instance = BulletproofAuthService._internal();
  factory BulletproofAuthService() => _instance;
  BulletproofAuthService._internal();

  // Secure storage with fallback mechanisms
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      sharedPreferencesName: 'kft_secure_prefs',
      preferencesKeyPrefix: 'kft_',
    ),
  );

  // Service dependencies
  final NetworkResilienceService _networkService = NetworkResilienceService();
  final DeviceCompatibilityService _deviceService = DeviceCompatibilityService();

  // Authentication state
  bool _isInitialized = false;
  String? _currentToken;
  String? _refreshToken;
  String? _deviceId;
  DateTime? _tokenExpiry;
  Timer? _refreshTimer;
  Timer? _healthCheckTimer;

  // Storage keys
  static const String _tokenKey = 'auth_token_v2';
  static const String _refreshTokenKey = 'refresh_token_v2';
  static const String _deviceIdKey = 'device_id_v2';
  static const String _tokenExpiryKey = 'token_expiry_v2';
  static const String _userDataKey = 'user_data_v2';
  static const String _sessionBackupKey = 'session_backup_v2';

  // Configuration
  static const Duration _tokenRefreshBuffer = Duration(minutes: 5);
  static const Duration _healthCheckInterval = Duration(minutes: 2);
  static const int _maxRetryAttempts = 5;

  /// Initialize the bulletproof authentication service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('🔐 Initializing BulletproofAuthService...');

      // Initialize dependencies
      await _networkService.initialize();
      await _deviceService.initialize();

      // Generate or retrieve device ID
      await _initializeDeviceId();

      // Load stored authentication data
      await _loadStoredAuth();

      // Start health monitoring
      _startHealthMonitoring();

      // Validate current session
      await _validateSession();

      _isInitialized = true;
      print('✅ BulletproofAuthService initialized successfully');
    } catch (e) {
      print('❌ BulletproofAuthService initialization failed: $e');
      // Continue with degraded functionality
      _isInitialized = true;
    }
  }

  /// Generate or retrieve persistent device ID
  Future<void> _initializeDeviceId() async {
    try {
      // Try to load existing device ID
      _deviceId = await _getSecureValue(_deviceIdKey);

      if (_deviceId == null) {
        // Generate new device ID based on device characteristics
        final deviceInfo = DeviceInfoPlugin();
        String deviceIdentifier;

        if (Platform.isAndroid) {
          final androidInfo = await deviceInfo.androidInfo;
          final uniqueData = '${androidInfo.brand}_${androidInfo.model}_${androidInfo.id}_${DateTime.now().millisecondsSinceEpoch}';
          deviceIdentifier = sha256.convert(utf8.encode(uniqueData)).toString();
        } else {
          deviceIdentifier = sha256.convert(utf8.encode('${Platform.operatingSystem}_${DateTime.now().millisecondsSinceEpoch}')).toString();
        }

        _deviceId = deviceIdentifier;
        await _setSecureValue(_deviceIdKey, _deviceId!);
        print('🔐 Generated new device ID: ${_deviceId!.substring(0, 8)}...');
      } else {
        print('🔐 Loaded existing device ID: ${_deviceId!.substring(0, 8)}...');
      }
    } catch (e) {
      print('❌ Device ID initialization failed: $e');
      // Fallback to timestamp-based ID
      _deviceId = sha256.convert(utf8.encode('fallback_${DateTime.now().millisecondsSinceEpoch}')).toString();
    }
  }

  /// Load stored authentication data with multiple fallback mechanisms
  Future<void> _loadStoredAuth() async {
    try {
      // Primary: Load from secure storage
      _currentToken = await _getSecureValue(_tokenKey);
      _refreshToken = await _getSecureValue(_refreshTokenKey);
      
      final expiryString = await _getSecureValue(_tokenExpiryKey);
      if (expiryString != null) {
        _tokenExpiry = DateTime.parse(expiryString);
      }

      // Fallback: Load from shared preferences if secure storage fails
      if (_currentToken == null) {
        await _loadFromSharedPreferences();
      }

      // Backup: Load from session backup
      if (_currentToken == null) {
        await _loadFromSessionBackup();
      }

      if (_currentToken != null) {
        print('🔐 Loaded stored authentication token');
        _scheduleTokenRefresh();
      } else {
        print('🔐 No stored authentication found');
      }
    } catch (e) {
      print('❌ Failed to load stored auth: $e');
    }
  }

  /// Fallback loading from shared preferences
  Future<void> _loadFromSharedPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _currentToken = prefs.getString(_tokenKey);
      _refreshToken = prefs.getString(_refreshTokenKey);
      
      final expiryMs = prefs.getInt(_tokenExpiryKey);
      if (expiryMs != null) {
        _tokenExpiry = DateTime.fromMillisecondsSinceEpoch(expiryMs);
      }

      if (_currentToken != null) {
        print('🔐 Loaded auth from SharedPreferences fallback');
        // Migrate to secure storage
        await _saveAuthData();
      }
    } catch (e) {
      print('❌ SharedPreferences fallback failed: $e');
    }
  }

  /// Load from session backup (encrypted JSON)
  Future<void> _loadFromSessionBackup() async {
    try {
      final backupData = await _getSecureValue(_sessionBackupKey);
      if (backupData != null) {
        final Map<String, dynamic> backup = json.decode(backupData);
        _currentToken = backup['token'];
        _refreshToken = backup['refreshToken'];
        
        if (backup['expiry'] != null) {
          _tokenExpiry = DateTime.parse(backup['expiry']);
        }

        if (_currentToken != null) {
          print('🔐 Loaded auth from session backup');
          // Restore to primary storage
          await _saveAuthData();
        }
      }
    } catch (e) {
      print('❌ Session backup loading failed: $e');
    }
  }

  /// Validate current session with network resilience
  Future<void> _validateSession() async {
    if (_currentToken == null) return;

    try {
      // Check if token is expired
      if (_tokenExpiry != null && DateTime.now().isAfter(_tokenExpiry!)) {
        print('🔐 Token expired, attempting refresh...');
        final refreshed = await _refreshAuthToken();
        if (!refreshed) {
          // Only log out if refresh fails AND server confirms session is invalid
          final isValid = await _validateTokenWithServer();
          if (!isValid) {
            print('❌ Session invalid after failed refresh, logging out');
            await logout();
          } else {
            print('⚠️ Token refresh failed, but session still valid. Will retry later.');
          }
        }
        return;
      }

      // Validate token with server (with retry logic)
      final isValid = await _networkService.executeWithRetry(
        () => _validateTokenWithServer(),
        maxAttempts: 3,
        backoffMultiplier: 1.5,
      );

      if (!isValid) {
        print('🔐 Token validation failed, attempting refresh...');
        final refreshed = await _refreshAuthToken();
        if (!refreshed) {
          // Only log out if server confirms session is invalid
          final stillValid = await _validateTokenWithServer();
          if (!stillValid) {
            print('❌ Session invalid after failed refresh, logging out');
            await logout();
          } else {
            print('⚠️ Token refresh failed, but session still valid. Will retry later.');
          }
        }
      } else {
        print('✅ Session validation successful');
      }
    } catch (e) {
      print('⚠️ Session validation error (network or transient): $e');
      // Do NOT log out on network errors; keep user logged in and retry later
    }
  }

  /// Validate token with server
  Future<bool> _validateTokenWithServer() async {
    // This would integrate with your existing API service
    // For now, return true to maintain session
    return true;
  }

  /// Refresh authentication token with comprehensive retry logic
  Future<bool> _refreshAuthToken() async {
    if (_refreshToken == null) {
      print('❌ No refresh token available');
      return false;
    }

    try {
      print('🔄 Refreshing authentication token...');

      // Implement token refresh with your API
      final success = await _networkService.executeWithRetry(
        () => _performTokenRefresh(),
        maxAttempts: _maxRetryAttempts,
        backoffMultiplier: 2.0,
      );

      if (success) {
        await _saveAuthData();
        _scheduleTokenRefresh();
        print('✅ Token refresh successful');
        return true;
      } else {
        print('❌ Token refresh failed after all retries');
        return false;
      }
    } catch (e) {
      print('❌ Token refresh error: $e');
      return false;
    }
  }

  /// Perform the actual token refresh API call
  Future<bool> _performTokenRefresh() async {
    // This would integrate with your existing API service
    // For now, extend the current token expiry
    if (_tokenExpiry != null) {
      _tokenExpiry = DateTime.now().add(const Duration(days: 30));
      return true;
    }
    return false;
  }

  /// Save authentication data to all storage mechanisms
  Future<void> _saveAuthData() async {
    if (_currentToken == null) return;

    try {
      // Primary: Secure storage
      await _setSecureValue(_tokenKey, _currentToken!);
      if (_refreshToken != null) {
        await _setSecureValue(_refreshTokenKey, _refreshToken!);
      }
      if (_tokenExpiry != null) {
        await _setSecureValue(_tokenExpiryKey, _tokenExpiry!.toIso8601String());
      }

      // Fallback: SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_tokenKey, _currentToken!);
      if (_refreshToken != null) {
        await prefs.setString(_refreshTokenKey, _refreshToken!);
      }
      if (_tokenExpiry != null) {
        await prefs.setInt(_tokenExpiryKey, _tokenExpiry!.millisecondsSinceEpoch);
      }

      // Backup: Session backup
      await _createSessionBackup();

      print('💾 Authentication data saved to all storage mechanisms');
    } catch (e) {
      print('❌ Failed to save auth data: $e');
    }
  }

  /// Create encrypted session backup
  Future<void> _createSessionBackup() async {
    try {
      final backup = {
        'token': _currentToken,
        'refreshToken': _refreshToken,
        'expiry': _tokenExpiry?.toIso8601String(),
        'deviceId': _deviceId,
        'timestamp': DateTime.now().toIso8601String(),
      };

      await _setSecureValue(_sessionBackupKey, json.encode(backup));
    } catch (e) {
      print('❌ Session backup creation failed: $e');
    }
  }

  /// Schedule automatic token refresh
  void _scheduleTokenRefresh() {
    _refreshTimer?.cancel();

    if (_tokenExpiry == null) return;

    final refreshTime = _tokenExpiry!.subtract(_tokenRefreshBuffer);
    final now = DateTime.now();

    if (refreshTime.isAfter(now)) {
      final delay = refreshTime.difference(now);
      _refreshTimer = Timer(delay, () async {
        await _refreshAuthToken();
      });

      print('⏰ Token refresh scheduled for: $refreshTime');
    } else {
      // Token needs immediate refresh
      Timer.run(() async {
        await _refreshAuthToken();
      });
    }
  }

  /// Start health monitoring
  void _startHealthMonitoring() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(_healthCheckInterval, (_) async {
      await _performHealthCheck();
    });
  }

  /// Perform periodic health check
  Future<void> _performHealthCheck() async {
    try {
      // Check if we still have valid authentication
      if (_currentToken == null) {
        print('🔍 Health check: No authentication token');
        return;
      }

      // Check token expiry
      if (_tokenExpiry != null && DateTime.now().isAfter(_tokenExpiry!.subtract(_tokenRefreshBuffer))) {
        print('🔍 Health check: Token needs refresh');
        await _refreshAuthToken();
      }

      // Verify storage integrity
      await _verifyStorageIntegrity();

    } catch (e) {
      print('❌ Health check error: $e');
    }
  }

  /// Verify storage integrity and repair if needed
  Future<void> _verifyStorageIntegrity() async {
    try {
      final storedToken = await _getSecureValue(_tokenKey);
      if (storedToken != _currentToken && _currentToken != null) {
        print('🔧 Storage integrity issue detected, repairing...');
        await _saveAuthData();
      }
    } catch (e) {
      print('❌ Storage integrity check failed: $e');
    }
  }

  /// Secure storage helper with fallback
  Future<String?> _getSecureValue(String key) async {
    try {
      return await _secureStorage.read(key: key);
    } catch (e) {
      print('⚠️ Secure storage read failed for $key: $e');
      // Fallback to SharedPreferences
      try {
        final prefs = await SharedPreferences.getInstance();
        return prefs.getString(key);
      } catch (e2) {
        print('❌ Fallback storage read failed for $key: $e2');
        return null;
      }
    }
  }

  /// Secure storage helper with fallback
  Future<void> _setSecureValue(String key, String value) async {
    try {
      await _secureStorage.write(key: key, value: value);
    } catch (e) {
      print('⚠️ Secure storage write failed for $key: $e');
      // Fallback to SharedPreferences
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(key, value);
      } catch (e2) {
        print('❌ Fallback storage write failed for $key: $e2');
      }
    }
  }

  /// Login with persistent session
  Future<bool> login(String username, String password) async {
    try {
      print('🔐 Attempting login...');

      // This would integrate with your existing login API
      // For now, simulate successful login
      _currentToken = 'persistent_token_${DateTime.now().millisecondsSinceEpoch}';
      _refreshToken = 'refresh_token_${DateTime.now().millisecondsSinceEpoch}';
      _tokenExpiry = DateTime.now().add(const Duration(days: 30));

      await _saveAuthData();
      _scheduleTokenRefresh();

      print('✅ Login successful with persistent session');
      return true;
    } catch (e) {
      print('❌ Login failed: $e');
      return false;
    }
  }

  /// Check if user is authenticated
  bool get isAuthenticated => _currentToken != null;

  /// Get current authentication token
  String? get currentToken => _currentToken;

  /// Get device ID
  String? get deviceId => _deviceId;

  /// Logout and clear all stored data
  Future<void> logout() async {
    try {
      print('🔐 Logging out...');

      // Cancel timers
      _refreshTimer?.cancel();
      _healthCheckTimer?.cancel();

      // Clear tokens
      _currentToken = null;
      _refreshToken = null;
      _tokenExpiry = null;

      // Clear all storage
      await _clearAllStorage();

      print('✅ Logout successful');
    } catch (e) {
      print('❌ Logout error: $e');
    }
  }

  /// Clear all authentication data from storage
  Future<void> _clearAllStorage() async {
    try {
      // Clear secure storage
      await _secureStorage.delete(key: _tokenKey);
      await _secureStorage.delete(key: _refreshTokenKey);
      await _secureStorage.delete(key: _tokenExpiryKey);
      await _secureStorage.delete(key: _sessionBackupKey);

      // Clear SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_tokenKey);
      await prefs.remove(_refreshTokenKey);
      await prefs.remove(_tokenExpiryKey);
    } catch (e) {
      print('❌ Storage clearing failed: $e');
    }
  }

  /// Dispose the service
  void dispose() {
    _refreshTimer?.cancel();
    _healthCheckTimer?.cancel();
    _isInitialized = false;
  }
}
