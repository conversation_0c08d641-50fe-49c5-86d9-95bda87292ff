import 'dart:async';
import 'dart:html' as html;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Optional PWA installation service
/// Provides configurable PWA installation prompts that can be enabled/disabled
/// No banner on full screen, only optional dialog prompts
class OptionalPWAService {
  static final OptionalPWAService _instance = OptionalPWAService._internal();
  factory OptionalPWAService() => _instance;
  OptionalPWAService._internal();

  // Event storage
  dynamic _beforeInstallPromptEvent;

  // State tracking
  bool _isInstalled = false;
  bool _isInstallable = false;
  bool _isEnabled = true; // Can be toggled by user
  bool _hasShownInitialPrompt = false;
  DateTime? _lastPromptTime;

  // Mobile detection
  bool _isMobile = false;
  bool _isIOS = false;
  bool _isAndroid = false;
  String _browserType = 'unknown';

  // Configuration keys
  static const String _enabledKey = 'pwa_install_enabled';
  static const String _lastPromptKey = 'pwa_last_prompt_time';
  static const String _initialPromptShownKey = 'pwa_initial_prompt_shown';
  static const Duration promptCooldown = Duration(hours: 24);

  // Notifiers
  final ValueNotifier<bool> installPromptAvailable = ValueNotifier<bool>(false);
  final ValueNotifier<bool> isEnabled = ValueNotifier<bool>(true);

  // Stream controllers
  final StreamController<bool> _installabilityController = StreamController<bool>.broadcast();
  final StreamController<bool> _installationController = StreamController<bool>.broadcast();

  /// Stream that emits when PWA installability changes
  Stream<bool> get installabilityStream => _installabilityController.stream;

  /// Stream that emits when PWA installation status changes
  Stream<bool> get installationStream => _installationController.stream;

  /// Get current enabled state
  bool get enabled => _isEnabled;

  /// Get if PWA is installable
  bool get isInstallable => _isInstallable && !_isInstalled;

  /// Get if PWA is installed
  bool get isInstalled => _isInstalled;

  /// Get if mobile device
  bool get isMobile => _isMobile;

  /// Initialize the service
  Future<void> initialize() async {
    if (!kIsWeb) return;

    debugPrint('🔧 Initializing OptionalPWAService...');

    try {
      await _loadSettings();
      _detectPlatform();
      _setupEventListeners();
      await _checkInstallationStatus();
      
      debugPrint('✅ OptionalPWAService initialized successfully');
      debugPrint('📱 Platform: $_browserType, Mobile: $_isMobile, iOS: $_isIOS, Android: $_isAndroid');
      debugPrint('⚙️ PWA Install enabled: $_isEnabled');
    } catch (e) {
      debugPrint('❌ Error initializing OptionalPWAService: $e');
    }
  }

  /// Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isEnabled = prefs.getBool(_enabledKey) ?? true;
      _hasShownInitialPrompt = prefs.getBool(_initialPromptShownKey) ?? false;
      
      final lastPromptStr = prefs.getString(_lastPromptKey);
      if (lastPromptStr != null) {
        _lastPromptTime = DateTime.tryParse(lastPromptStr);
      }

      isEnabled.value = _isEnabled;
    } catch (e) {
      debugPrint('⚠️ Error loading PWA settings: $e');
    }
  }

  /// Save settings to SharedPreferences
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_enabledKey, _isEnabled);
      await prefs.setBool(_initialPromptShownKey, _hasShownInitialPrompt);
      
      if (_lastPromptTime != null) {
        await prefs.setString(_lastPromptKey, _lastPromptTime!.toIso8601String());
      }
    } catch (e) {
      debugPrint('⚠️ Error saving PWA settings: $e');
    }
  }

  /// Detect platform and browser
  void _detectPlatform() {
    final userAgent = html.window.navigator.userAgent.toLowerCase();
    
    _isIOS = userAgent.contains('iphone') || userAgent.contains('ipad');
    _isAndroid = userAgent.contains('android');
    _isMobile = _isIOS || _isAndroid || userAgent.contains('mobile');
    
    if (userAgent.contains('chrome')) {
      _browserType = 'chrome';
    } else if (userAgent.contains('firefox')) {
      _browserType = 'firefox';
    } else if (userAgent.contains('safari')) {
      _browserType = 'safari';
    } else if (userAgent.contains('edge')) {
      _browserType = 'edge';
    }
  }

  /// Setup event listeners for PWA events
  void _setupEventListeners() {
    // Listen for beforeinstallprompt event
    html.window.addEventListener('beforeinstallprompt', (event) {
      debugPrint('📋 PWA: beforeinstallprompt event fired');
      event.preventDefault();
      _beforeInstallPromptEvent = event;
      _isInstallable = true;
      installPromptAvailable.value = true;
      _installabilityController.add(true);
    });

    // Listen for appinstalled event
    html.window.addEventListener('appinstalled', (event) {
      debugPrint('✅ PWA: App installed successfully');
      _isInstalled = true;
      _isInstallable = false;
      installPromptAvailable.value = false;
      _installabilityController.add(false);
      _installationController.add(true);
    });
  }

  /// Check if PWA is already installed
  Future<void> _checkInstallationStatus() async {
    try {
      // Check if running in standalone mode (PWA installed)
      final isStandalone = html.window.matchMedia('(display-mode: standalone)').matches;
      final isFullscreen = html.window.matchMedia('(display-mode: fullscreen)').matches;
      
      _isInstalled = isStandalone || isFullscreen;
      
      if (_isInstalled) {
        debugPrint('✅ PWA is already installed');
        _installationController.add(true);
      }
    } catch (e) {
      debugPrint('⚠️ Error checking installation status: $e');
    }
  }

  /// Enable or disable PWA install prompts
  Future<void> setEnabled(bool enabled) async {
    _isEnabled = enabled;
    isEnabled.value = enabled;
    await _saveSettings();
    debugPrint('⚙️ PWA install prompts ${enabled ? 'enabled' : 'disabled'}');
  }

  /// Check if we should show the initial prompt
  bool shouldShowInitialPrompt() {
    if (!_isEnabled || _isInstalled || !_isInstallable) return false;
    if (_hasShownInitialPrompt) return false;
    
    // Check cooldown
    if (_lastPromptTime != null) {
      final timeSinceLastPrompt = DateTime.now().difference(_lastPromptTime!);
      if (timeSinceLastPrompt < promptCooldown) return false;
    }
    
    return true;
  }

  /// Mark initial prompt as shown
  Future<void> markInitialPromptShown() async {
    _hasShownInitialPrompt = true;
    _lastPromptTime = DateTime.now();
    await _saveSettings();
  }

  /// Prompt for PWA installation
  Future<bool> promptInstall() async {
    if (!_isEnabled || _beforeInstallPromptEvent == null) {
      debugPrint('⚠️ PWA install not available or disabled');
      return false;
    }

    try {
      debugPrint('🚀 Showing PWA install prompt...');
      
      // Show the native install prompt
      final result = await _beforeInstallPromptEvent.prompt();
      debugPrint('📋 PWA install prompt result: ${result?.outcome}');
      
      if (result?.outcome == 'accepted') {
        debugPrint('✅ User accepted PWA installation');
        _lastPromptTime = DateTime.now();
        await _saveSettings();
        return true;
      } else {
        debugPrint('❌ User dismissed PWA installation');
        _lastPromptTime = DateTime.now();
        await _saveSettings();
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error showing PWA install prompt: $e');
      return false;
    }
  }

  /// Get installation instructions for mobile devices
  String getInstallInstructions() {
    if (_isIOS) {
      return 'Tap the Share button and select "Add to Home Screen"';
    } else if (_isAndroid) {
      return 'Tap the menu button and select "Add to Home Screen" or "Install App"';
    } else {
      return 'Look for the install button in your browser\'s address bar';
    }
  }

  /// Dispose resources
  void dispose() {
    _installabilityController.close();
    _installationController.close();
  }
}
