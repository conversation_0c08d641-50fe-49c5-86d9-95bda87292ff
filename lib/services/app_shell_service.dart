import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:async';

/// App Shell Service for Lightning-Fast Loading
/// Implements app shell architecture with instant skeleton screens
/// Provides progressive content loading for native app-like experience
class AppShellService {
  static final AppShellService _instance = AppShellService._internal();
  factory AppShellService() => _instance;
  AppShellService._internal();

  // Shell state management
  bool _isShellReady = false;
  bool _isContentLoaded = false;
  final Map<String, bool> _pageShellStates = {};
  final Map<String, Widget> _skeletonCache = {};
  
  // Loading state controllers
  final StreamController<bool> _shellReadyController = StreamController<bool>.broadcast();
  final StreamController<String> _contentLoadController = StreamController<String>.broadcast();
  
  // Performance tracking
  DateTime? _shellStartTime;
  DateTime? _shellReadyTime;
  DateTime? _contentReadyTime;

  /// Initialize app shell service
  Future<void> initialize() async {
    try {
      _shellStartTime = DateTime.now();
      debugPrint('🏗️ Initializing App Shell Service...');
      
      // Pre-build skeleton screens
      await _prebuildSkeletons();
      
      // Mark shell as ready
      _isShellReady = true;
      _shellReadyTime = DateTime.now();
      _shellReadyController.add(true);
      
      final loadTime = _shellReadyTime!.difference(_shellStartTime!).inMilliseconds;
      debugPrint('✅ App Shell ready in ${loadTime}ms');
      
    } catch (e) {
      debugPrint('❌ App Shell initialization failed: $e');
    }
  }

  /// Pre-build skeleton screens for instant display
  Future<void> _prebuildSkeletons() async {
    // Build common skeleton screens
    _skeletonCache['home'] = _buildHomeSkeleton();
    _skeletonCache['courses'] = _buildCoursesSkeleton();
    _skeletonCache['profile'] = _buildProfileSkeleton();
    _skeletonCache['video'] = _buildVideoSkeleton();
    _skeletonCache['progress'] = _buildProgressSkeleton();
    
    debugPrint('📦 Pre-built ${_skeletonCache.length} skeleton screens');
  }

  /// Build home page skeleton
  Widget _buildHomeSkeleton() {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: const Color(0xFF3D5AFE),
        elevation: 0,
        title: _buildShimmerBox(120, 20),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: _buildShimmerCircle(32),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome section
            _buildShimmerBox(double.infinity, 60),
            const SizedBox(height: 24),
            
            // Quick stats
            Row(
              children: [
                Expanded(child: _buildStatCardSkeleton()),
                const SizedBox(width: 12),
                Expanded(child: _buildStatCardSkeleton()),
                const SizedBox(width: 12),
                Expanded(child: _buildStatCardSkeleton()),
              ],
            ),
            const SizedBox(height: 24),
            
            // Recent activity
            _buildShimmerBox(150, 20),
            const SizedBox(height: 16),
            ...List.generate(3, (index) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: _buildActivityCardSkeleton(),
            )),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavSkeleton(),
    );
  }

  /// Build courses page skeleton
  Widget _buildCoursesSkeleton() {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: const Color(0xFF3D5AFE),
        elevation: 0,
        title: _buildShimmerBox(100, 20),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search bar
            _buildShimmerBox(double.infinity, 48),
            const SizedBox(height: 20),
            
            // Categories
            _buildShimmerBox(120, 20),
            const SizedBox(height: 12),
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: 5,
                itemBuilder: (context, index) => Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: _buildShimmerBox(80, 40),
                ),
              ),
            ),
            const SizedBox(height: 24),
            
            // Course grid
            Expanded(
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: 0.8,
                ),
                itemCount: 6,
                itemBuilder: (context, index) => _buildCourseCardSkeleton(),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavSkeleton(),
    );
  }

  /// Build profile page skeleton
  Widget _buildProfileSkeleton() {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: const Color(0xFF3D5AFE),
        elevation: 0,
        title: _buildShimmerBox(80, 20),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile header
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  _buildShimmerCircle(80),
                  const SizedBox(height: 16),
                  _buildShimmerBox(120, 20),
                  const SizedBox(height: 8),
                  _buildShimmerBox(80, 16),
                ],
              ),
            ),
            const SizedBox(height: 20),
            
            // Settings options
            ...List.generate(6, (index) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: _buildSettingsItemSkeleton(),
            )),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavSkeleton(),
    );
  }

  /// Build video page skeleton
  Widget _buildVideoSkeleton() {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: _buildShimmerBox(24, 24),
        title: _buildShimmerBox(150, 20),
      ),
      body: Column(
        children: [
          // Video player area
          Container(
            width: double.infinity,
            height: 200,
            color: Colors.grey[900],
            child: Center(
              child: _buildShimmerBox(60, 60),
            ),
          ),
          
          // Video details
          Expanded(
            child: Container(
              color: Colors.white,
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildShimmerBox(double.infinity, 24),
                  const SizedBox(height: 12),
                  _buildShimmerBox(200, 16),
                  const SizedBox(height: 20),
                  _buildShimmerBox(double.infinity, 48),
                  const SizedBox(height: 20),
                  _buildShimmerBox(double.infinity, 100),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build progress page skeleton
  Widget _buildProgressSkeleton() {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: const Color(0xFF3D5AFE),
        elevation: 0,
        title: _buildShimmerBox(100, 20),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Progress overview
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  _buildShimmerCircle(100),
                  const SizedBox(height: 16),
                  _buildShimmerBox(150, 20),
                  const SizedBox(height: 8),
                  _buildShimmerBox(100, 16),
                ],
              ),
            ),
            const SizedBox(height: 24),
            
            // Stats grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.5,
              children: List.generate(4, (index) => _buildStatCardSkeleton()),
            ),
            const SizedBox(height: 24),
            
            // Recent achievements
            _buildShimmerBox(180, 20),
            const SizedBox(height: 16),
            ...List.generate(3, (index) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: _buildAchievementSkeleton(),
            )),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavSkeleton(),
    );
  }

  /// Build shimmer box
  Widget _buildShimmerBox(double width, double height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const ShimmerEffect(),
    );
  }

  /// Build shimmer circle
  Widget _buildShimmerCircle(double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        shape: BoxShape.circle,
      ),
      child: const ShimmerEffect(),
    );
  }

  /// Build stat card skeleton
  Widget _buildStatCardSkeleton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildShimmerBox(40, 16),
          const SizedBox(height: 8),
          _buildShimmerBox(60, 24),
        ],
      ),
    );
  }

  /// Build activity card skeleton
  Widget _buildActivityCardSkeleton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          _buildShimmerCircle(40),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildShimmerBox(double.infinity, 16),
                const SizedBox(height: 4),
                _buildShimmerBox(100, 14),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build course card skeleton
  Widget _buildCourseCardSkeleton() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: const ShimmerEffect(),
            ),
          ),
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildShimmerBox(double.infinity, 16),
                  const SizedBox(height: 8),
                  _buildShimmerBox(80, 14),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build settings item skeleton
  Widget _buildSettingsItemSkeleton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          _buildShimmerBox(24, 24),
          const SizedBox(width: 16),
          Expanded(child: _buildShimmerBox(double.infinity, 16)),
          _buildShimmerBox(16, 16),
        ],
      ),
    );
  }

  /// Build achievement skeleton
  Widget _buildAchievementSkeleton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          _buildShimmerBox(48, 48),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildShimmerBox(double.infinity, 16),
                const SizedBox(height: 4),
                _buildShimmerBox(120, 14),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build bottom navigation skeleton
  Widget _buildBottomNavSkeleton() {
    return Container(
      height: 80,
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey, width: 0.5)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: List.generate(5, (index) => Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildShimmerBox(24, 24),
            const SizedBox(height: 4),
            _buildShimmerBox(40, 12),
          ],
        )),
      ),
    );
  }

  /// Get skeleton for page
  Widget? getSkeletonForPage(String pageName) {
    return _skeletonCache[pageName];
  }

  /// Check if shell is ready
  bool get isShellReady => _isShellReady;

  /// Get shell ready stream
  Stream<bool> get shellReadyStream => _shellReadyController.stream;

  /// Mark content as loaded for page
  void markContentLoaded(String pageName) {
    _pageShellStates[pageName] = true;
    _contentLoadController.add(pageName);
    
    if (!_isContentLoaded) {
      _isContentLoaded = true;
      _contentReadyTime = DateTime.now();
      
      if (_shellStartTime != null && _contentReadyTime != null) {
        final totalTime = _contentReadyTime!.difference(_shellStartTime!).inMilliseconds;
        debugPrint('🎯 Content loaded in ${totalTime}ms total');
      }
    }
  }

  /// Get content load stream
  Stream<String> get contentLoadStream => _contentLoadController.stream;

  /// Get performance metrics
  Map<String, int> getPerformanceMetrics() {
    final now = DateTime.now();
    return {
      'shell_load_time': _shellReadyTime != null && _shellStartTime != null
          ? _shellReadyTime!.difference(_shellStartTime!).inMilliseconds
          : 0,
      'content_load_time': _contentReadyTime != null && _shellStartTime != null
          ? _contentReadyTime!.difference(_shellStartTime!).inMilliseconds
          : 0,
      'total_uptime': _shellStartTime != null
          ? now.difference(_shellStartTime!).inMilliseconds
          : 0,
    };
  }

  /// Dispose resources
  void dispose() {
    _shellReadyController.close();
    _contentLoadController.close();
  }
}

/// Shimmer effect widget for skeleton screens
class ShimmerEffect extends StatefulWidget {
  const ShimmerEffect({Key? key}) : super(key: key);

  @override
  _ShimmerEffectState createState() => _ShimmerEffectState();
}

class _ShimmerEffectState extends State<ShimmerEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              stops: [
                _animation.value - 0.3,
                _animation.value,
                _animation.value + 0.3,
              ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
              colors: const [
                Colors.transparent,
                Colors.white54,
                Colors.transparent,
              ],
            ),
          ),
        );
      },
    );
  }
}
