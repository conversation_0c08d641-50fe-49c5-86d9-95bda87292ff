import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:async';
// ignore: avoid_web_libraries_in_flutter
import 'dart:html' as html show window, document, ImageElement, ScriptElement;

/// Lazy Loading Service for Non-Critical Assets
/// Implements progressive loading to reduce initial load time
/// Loads assets only when needed for optimal performance
class LazyLoadingService {
  static final LazyLoadingService _instance = LazyLoadingService._internal();
  factory LazyLoadingService() => _instance;
  LazyLoadingService._internal();

  // Loading state tracking
  final Set<String> _loadedAssets = <String>{};
  final Set<String> _loadingAssets = <String>{};
  final Map<String, List<Function()>> _loadingCallbacks = {};
  
  // Asset categories for prioritized loading
  final List<String> _criticalAssets = [
    'assets/fonts/MaterialIcons-Regular.otf',
    'assets/packages/cupertino_icons/assets/CupertinoIcons.ttf',
    'assets/assets/images/logo.webp',
  ];
  
  final List<String> _secondaryAssets = [
    'assets/packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf',
    'assets/packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf',
    'assets/packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf',
  ];
  
  final List<String> _backgroundAssets = [
    'splash/img/light-3x.png',
    'splash/img/light-4x.png',
    'splash/img/dark-3x.png',
    'splash/img/dark-4x.png',
  ];

  bool _isInitialized = false;

  /// Initialize lazy loading service
  Future<void> initialize() async {
    if (!kIsWeb || _isInitialized) return;

    try {
      debugPrint('🚀 Initializing Lazy Loading Service...');
      
      // Start progressive loading
      _startProgressiveLoading();
      
      _isInitialized = true;
      debugPrint('✅ Lazy Loading Service initialized');
      
    } catch (e) {
      debugPrint('❌ Error initializing Lazy Loading Service: $e');
    }
  }

  /// Start progressive loading of assets
  void _startProgressiveLoading() {
    // Load critical assets immediately (already preloaded in HTML)
    _markAssetsAsLoaded(_criticalAssets);
    
    // Load secondary assets after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      _loadAssetBatch(_secondaryAssets, 'secondary');
    });
    
    // Load background assets when idle
    Future.delayed(const Duration(seconds: 2), () {
      _loadAssetBatch(_backgroundAssets, 'background');
    });
  }

  /// Mark assets as already loaded (for preloaded assets)
  void _markAssetsAsLoaded(List<String> assets) {
    for (final asset in assets) {
      _loadedAssets.add(asset);
    }
    debugPrint('📦 Marked ${assets.length} critical assets as loaded');
  }

  /// Load a batch of assets
  Future<void> _loadAssetBatch(List<String> assets, String batchName) async {
    debugPrint('📦 Loading $batchName asset batch (${assets.length} assets)');
    
    final futures = assets.map((asset) => _loadAssetLazy(asset));
    final results = await Future.wait(futures, eagerError: false);
    
    final successCount = results.where((result) => result).length;
    debugPrint('✅ Loaded $successCount/${assets.length} $batchName assets');
  }

  /// Load a single asset lazily
  Future<bool> _loadAssetLazy(String assetPath) async {
    if (_loadedAssets.contains(assetPath)) {
      return true; // Already loaded
    }
    
    if (_loadingAssets.contains(assetPath)) {
      // Already loading, wait for completion
      return _waitForAssetLoad(assetPath);
    }
    
    _loadingAssets.add(assetPath);
    
    try {
      bool success = false;
      
      if (assetPath.endsWith('.ttf') || assetPath.endsWith('.otf')) {
        success = await _loadFont(assetPath);
      } else if (assetPath.endsWith('.png') || assetPath.endsWith('.jpg') || 
                 assetPath.endsWith('.jpeg') || assetPath.endsWith('.webp')) {
        success = await _loadImage(assetPath);
      } else if (assetPath.endsWith('.js')) {
        success = await _loadScript(assetPath);
      } else {
        success = await _loadGenericAsset(assetPath);
      }
      
      if (success) {
        _loadedAssets.add(assetPath);
        _notifyLoadingCallbacks(assetPath);
      }
      
      return success;
    } catch (e) {
      debugPrint('⚠️ Failed to load asset $assetPath: $e');
      return false;
    } finally {
      _loadingAssets.remove(assetPath);
    }
  }

  /// Load font asset
  Future<bool> _loadFont(String fontPath) async {
    try {
      // For web, fonts are loaded via CSS @font-face or preload
      // We just need to trigger the load
      final fontFace = html.document.createElement('link');
      fontFace.setAttribute('rel', 'preload');
      fontFace.setAttribute('href', fontPath);
      fontFace.setAttribute('as', 'font');
      fontFace.setAttribute('type', _getFontMimeType(fontPath));
      fontFace.setAttribute('crossorigin', '');
      
      html.document.head?.children.add(fontFace);
      
      // Wait a bit for the font to load
      await Future.delayed(const Duration(milliseconds: 100));
      return true;
    } catch (e) {
      debugPrint('⚠️ Font loading failed for $fontPath: $e');
      return false;
    }
  }

  /// Load image asset
  Future<bool> _loadImage(String imagePath) async {
    try {
      final img = html.ImageElement();
      img.src = imagePath;
      
      // Wait for image to load
      await img.onLoad.first.timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw TimeoutException('Image load timeout'),
      );
      
      return true;
    } catch (e) {
      debugPrint('⚠️ Image loading failed for $imagePath: $e');
      return false;
    }
  }

  /// Load script asset
  Future<bool> _loadScript(String scriptPath) async {
    try {
      final script = html.ScriptElement();
      script.src = scriptPath;
      script.async = true;
      
      html.document.head?.children.add(script);
      
      // Wait for script to load
      await script.onLoad.first.timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw TimeoutException('Script load timeout'),
      );
      
      return true;
    } catch (e) {
      debugPrint('⚠️ Script loading failed for $scriptPath: $e');
      return false;
    }
  }

  /// Load generic asset
  Future<bool> _loadGenericAsset(String assetPath) async {
    try {
      // Use fetch to load the asset
      final response = await html.window.fetch(assetPath);
      return response.ok;
    } catch (e) {
      debugPrint('⚠️ Generic asset loading failed for $assetPath: $e');
      return false;
    }
  }

  /// Wait for asset to finish loading
  Future<bool> _waitForAssetLoad(String assetPath) async {
    final completer = Completer<bool>();
    
    _loadingCallbacks.putIfAbsent(assetPath, () => []).add(() {
      completer.complete(_loadedAssets.contains(assetPath));
    });
    
    return completer.future;
  }

  /// Notify callbacks when asset finishes loading
  void _notifyLoadingCallbacks(String assetPath) {
    final callbacks = _loadingCallbacks.remove(assetPath);
    if (callbacks != null) {
      for (final callback in callbacks) {
        try {
          callback();
        } catch (e) {
          debugPrint('⚠️ Loading callback error: $e');
        }
      }
    }
  }

  /// Get font MIME type
  String _getFontMimeType(String fontPath) {
    if (fontPath.endsWith('.ttf')) return 'font/ttf';
    if (fontPath.endsWith('.otf')) return 'font/otf';
    if (fontPath.endsWith('.woff')) return 'font/woff';
    if (fontPath.endsWith('.woff2')) return 'font/woff2';
    return 'font/ttf';
  }

  /// Check if asset is loaded
  bool isAssetLoaded(String assetPath) {
    return _loadedAssets.contains(assetPath);
  }

  /// Load asset on demand
  Future<bool> loadAssetOnDemand(String assetPath) async {
    return await _loadAssetLazy(assetPath);
  }

  /// Preload assets for a specific feature
  Future<void> preloadFeatureAssets(List<String> assets) async {
    debugPrint('🎯 Preloading ${assets.length} feature assets');
    await _loadAssetBatch(assets, 'feature');
  }

  /// Get loading statistics
  Map<String, dynamic> getLoadingStats() {
    return {
      'loaded': _loadedAssets.length,
      'loading': _loadingAssets.length,
      'total_tracked': _loadedAssets.length + _loadingAssets.length,
      'loaded_assets': _loadedAssets.toList(),
      'loading_assets': _loadingAssets.toList(),
    };
  }

  /// Reset service state
  void reset() {
    _loadedAssets.clear();
    _loadingAssets.clear();
    _loadingCallbacks.clear();
    _isInitialized = false;
  }
}

class TimeoutException implements Exception {
  final String message;
  TimeoutException(this.message);
  
  @override
  String toString() => 'TimeoutException: $message';
}
