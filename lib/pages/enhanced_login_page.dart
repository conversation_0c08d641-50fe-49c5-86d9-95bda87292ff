import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import '../services/api_service.dart';
import '../services/user_service.dart';
import '../services/auth_service.dart';
import '../services/persistent_auth_service.dart';
import '../config/app_config.dart';
import '../design_system/kft_design_system.dart';
import '../widgets/kft_button.dart';
import '../widgets/kft_text_field.dart';
import '../widgets/mobile_number_input.dart';
import '../widgets/pin_input_widget.dart';
import '../widgets/premium_animated_logo.dart';
import '../widgets/network_error_dialog.dart';
import '../widgets/enhanced_glass_card.dart';
import '../utils/animations.dart';
import '../utils/phone_utils.dart';
import 'dart:math';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io' show Platform;
import '../utils/platform_storage.dart';
import '../theme/app_theme.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart' as provider;
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import '../providers/auth_provider.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../widgets/simple_pwa_prompt.dart';


class EnhancedLoginPage extends riverpod.ConsumerStatefulWidget {
  const EnhancedLoginPage({Key? key}) : super(key: key);

  @override
  riverpod.ConsumerState<EnhancedLoginPage> createState() => _EnhancedLoginPageState();
}

class _EnhancedLoginPageState extends riverpod.ConsumerState<EnhancedLoginPage> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _pinController = TextEditingController();
  bool _showRegistrationForm = false;
  final _nameController = TextEditingController();

  bool _isLoading = false;
  String _errorMessage = '';
  bool _showWhatsAppSupport = false;
  String _whatsappNumber = '+************';

  final ApiService _apiService = ApiService();
  final UserService _userService = UserService();
  final AuthService _authService = AuthService();

  // Enhanced persistent auth service
  late final PersistentAuthService _persistentAuthService;

  bool _showThankYou = false;

  // Two-step login state
  bool _phoneStepComplete = false;

  // Country code selection
  String _selectedCountryCode = '+91'; // Default to India

  bool _isKeyboardVisible = false;
  final FocusNode _phoneFocusNode = FocusNode();

  // Track if the user is typing in the phone field
  bool _isTypingPhone = false;

  // Remember me checkbox state - default to true for persistent login
  bool _rememberMe = true;

  bool _showUnregisteredContact = false;

  @override
  void initState() {
    super.initState();
    _showRegistrationForm = false;

    // Initialize persistent auth service
    _persistentAuthService = PersistentAuthService();
    _initializePersistentAuth();

    // Listen for focus changes to detect keyboard
    _usernameFocusNode.addListener(() {
      setState(() {
        _isKeyboardVisible = _usernameFocusNode.hasFocus;
      });
    });

    // Listen for typing in the username field
    _usernameController.addListener(() {
      setState(() {
        _isTypingUsername = _usernameController.text.isNotEmpty;
      });
    });
  }

  Future<void> _initializePersistentAuth() async {
    try {
      await _persistentAuthService.initialize();
      debugPrint('🔐 PersistentAuthService initialized in login page');
      // Note: AuthWrapper handles the authentication check and navigation
      // No need to check login status here as it would conflict with AuthWrapper
    } catch (e) {
      debugPrint('❌ Error initializing persistent auth: $e');
    }
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _pinController.dispose();
    _usernameFocusNode.dispose();
    super.dispose();
  }

  Future<String> getDeviceId() async {
    if (kIsWeb) {
      return 'web_device';
    }
    final deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      return androidInfo.id;
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      return iosInfo.identifierForVendor ?? 'unknown_ios_device';
    } else {
      return 'unsupported_platform';
    }
  }

  Future<dynamic> checkPhoneRegistered(String phoneNumber, String deviceId) async {
    try {
      // Get the base URL and construct the correct endpoint
      String baseUrl = ApiService.baseUrl;

      debugPrint('🔍 Original base URL: $baseUrl');

      // Handle different URL formats
      String checkUrl;
      if (baseUrl.contains('/admin/api')) {
        // Replace /admin/api with /admin for the phone check endpoint
        checkUrl = baseUrl.replaceAll('/admin/api/', '/admin/').replaceAll('/admin/api', '/admin/');
        if (!checkUrl.endsWith('/')) checkUrl += '/';
        checkUrl += 'phone_check.php';
      } else {
        // Fallback: assume we need to add the path
        if (!baseUrl.endsWith('/')) baseUrl += '/';
        checkUrl = '${baseUrl}admin/phone_check.php';
      }

      final url = Uri.parse(checkUrl);

      // Get the best phone format for backend checking
      final phoneVariations = PhoneUtils.getPhoneNumberVariations(phoneNumber);
      final primaryPhone = phoneVariations.isNotEmpty ? phoneVariations.first : phoneNumber;

      debugPrint('🔍 Checking phone at: $url');
      debugPrint('📝 Phone: $primaryPhone, Device ID: $deviceId');

      // First try an OPTIONS request to check CORS
      try {
        final optionsResponse = await http.Request('OPTIONS', url).send();
        debugPrint('🔍 OPTIONS response status: ${optionsResponse.statusCode}');
      } catch (optionsError) {
        debugPrint('⚠️ OPTIONS request failed: $optionsError');
      }

      final response = await http.post(
        url,
        body: {
          'phone_number': primaryPhone,
          'device_id': deviceId,
        },
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
          'Origin': 'http://localhost:8080',
        },
      ).timeout(const Duration(seconds: 15));

      debugPrint('📡 Response status: ${response.statusCode}');
      debugPrint('📡 Response body: ${response.body}');

      if (response.statusCode == 200) {
        try {
          final data = jsonDecode(response.body);

          if (data.containsKey('error')) {
            if (data['error'] == 'DEVICE_ALREADY_REGISTERED') {
              return 'DEVICE_ALREADY_REGISTERED';
            }
            debugPrint('❌ Server error: ${data['error']}');
            throw Exception(data['error']);
          }

          return data['exists'] == true;
        } catch (jsonError) {
          debugPrint('❌ JSON parsing error: $jsonError');
          debugPrint('❌ Raw response: ${response.body}');
          throw Exception('Invalid server response format');
        }
      } else {
        debugPrint('❌ HTTP Error ${response.statusCode}: ${response.body}');
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ Username check error: $e');

      // Try alternative approach with different headers
      if (e.toString().contains('ClientException') || e.toString().contains('Failed to fetch')) {
        debugPrint('🔄 Trying alternative request approach...');
        try {
          // Reconstruct URL for alternative request
          String baseUrl = ApiService.baseUrl;
          String checkUrl;
          if (baseUrl.contains('/admin/api')) {
            checkUrl = baseUrl.replaceAll('/admin/api/', '/admin/').replaceAll('/admin/api', '/admin/');
            if (!checkUrl.endsWith('/')) checkUrl += '/';
            checkUrl += 'phone_check.php';
          } else {
            if (!baseUrl.endsWith('/')) baseUrl += '/';
            checkUrl = '${baseUrl}admin/phone_check.php';
          }

          // Get the best phone format for backend checking
          final phoneVariations = PhoneUtils.getPhoneNumberVariations(phoneNumber);
          final primaryPhone = phoneVariations.isNotEmpty ? phoneVariations.first : phoneNumber;

          final alternativeUrl = Uri.parse(checkUrl);
          final alternativeResponse = await http.post(
            alternativeUrl,
            body: 'phone_number=$primaryPhone&device_id=$deviceId',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
              'Accept': '*/*',
            },
          ).timeout(const Duration(seconds: 10));

          debugPrint('📡 Alternative response status: ${alternativeResponse.statusCode}');
          debugPrint('📡 Alternative response body: ${alternativeResponse.body}');

          if (alternativeResponse.statusCode == 200) {
            try {
              final data = jsonDecode(alternativeResponse.body);

              if (data.containsKey('error')) {
                if (data['error'] == 'DEVICE_ALREADY_REGISTERED') {
                  return 'DEVICE_ALREADY_REGISTERED';
                }
                debugPrint('❌ Server error: ${data['error']}');
                throw Exception(data['error']);
              }

              return data['exists'] == true;
            } catch (jsonError) {
              debugPrint('❌ JSON parsing error: $jsonError');
              throw Exception('Invalid server response format');
            }
          } else {
            throw Exception('Server error: ${alternativeResponse.statusCode}');
          }
        } catch (alternativeError) {
          debugPrint('❌ Alternative request also failed: $alternativeError');
        }
      }

      if (e.toString().contains('TimeoutException')) {
        throw Exception('Connection timeout. Please check your internet connection.');
      } else if (e.toString().contains('SocketException')) {
        throw Exception('Cannot connect to server. Please check if the server is running.');
      } else if (e.toString().contains('ClientException') || e.toString().contains('Failed to fetch')) {
        throw Exception('Network error. Please check if the PHP server is running on localhost:9001');
      } else {
        throw Exception('Failed to check phone number: ${e.toString()}');
      }
    }
  }

  Future<void> _checkPhone() async {
    final phoneNumber = _phoneController.text.trim();

    // Phone number validation
    if (phoneNumber.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter your phone number';
        _showUnregisteredContact = false;
      });
      return;
    }

    final validationResult = PhoneUtils.validatePhoneNumber(phoneNumber);
    if (!validationResult.isValid) {
      setState(() {
        _errorMessage = validationResult.errorMessage ?? 'Please enter a valid phone number';
        _showUnregisteredContact = false;
      });
      return;
    }
    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _showWhatsAppSupport = false;
      _showUnregisteredContact = false;
    });
    try {
      final deviceId = await getDeviceId();
      final exists = await checkPhoneRegistered(phoneNumber, deviceId);
      if (exists == 'DEVICE_ALREADY_REGISTERED') {
        setState(() {
          _errorMessage = 'Already logged in on another device. Please contact admin.';
          _showWhatsAppSupport = true;
          _isLoading = false;
          _showUnregisteredContact = false;
        });
        return;
      }
      if (exists) {
        setState(() {
          _phoneStepComplete = true;
          _errorMessage = '';
          _showUnregisteredContact = false;
        });
      } else {
        setState(() {
          _showRegistrationForm = false;
          _showUnregisteredContact = true;
          _errorMessage = 'This phone number is not registered. Please contact admin to create your account.';
          _showWhatsAppSupport = true;
        });
      }
    } catch (e) {
      final errorStr = e.toString().toLowerCase();
      debugPrint('❌ Phone check failed: $e');

      // Check for specific error types and provide user-friendly messages
      if (errorStr.contains('socketexception') ||
          errorStr.contains('connection refused') ||
          errorStr.contains('cannot connect to server')) {
        setState(() {
          _errorMessage = 'Cannot connect to server. Please check if the server is running on localhost:9001';
          _showUnregisteredContact = false;
        });

        // Show network error dialog with retry option
        if (mounted) {
          _showConnectionErrorDialog();
        }
      } else if (errorStr.contains('timeout')) {
        setState(() {
          _errorMessage = 'Connection timeout. Please try again.';
          _showUnregisteredContact = false;
        });
      } else if (errorStr.contains('invalid server response')) {
        setState(() {
          _errorMessage = 'Server response error. Please contact support.';
          _showUnregisteredContact = false;
        });
      } else {
        setState(() {
          _errorMessage = 'Unable to verify phone number. Please try again or contact support.';
          _showUnregisteredContact = false;
        });
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Show connection error dialog with helpful information
  void _showConnectionErrorDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Row(
            children: [
              Icon(Icons.wifi_off, color: Colors.red[600], size: 28),
              const SizedBox(width: 12),
              const Text('Connection Error', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Unable to connect to the server. Please check:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 16),
              _buildCheckItem('PHP server is running on localhost:9001'),
              _buildCheckItem('Internet connection is stable'),
              _buildCheckItem('Firewall is not blocking the connection'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Quick Fix:',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.blue[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Run: ./run_php_server.sh\nThen try again.',
                      style: TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _checkPhone();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
              child: const Text('Retry'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCheckItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(Icons.check_circle_outline, color: Colors.grey[600], size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  /// Enhanced login with persistent authentication
  Future<void> _login() async {
    final phoneNumber = _phoneController.text.trim();
    final pin = _pinController.text.trim();

    debugPrint('🔐 Attempting persistent login with phone: $phoneNumber, remember me: $_rememberMe');

    if (pin.isEmpty || pin.length != 4 || !RegExp(r'^\d{4}').hasMatch(pin)) {
      setState(() {
        _errorMessage = 'Please enter a valid 4-digit PIN to continue';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final deviceId = await getDeviceId();
      debugPrint('🔐 Device ID: $deviceId');

      // Use the new persistent auth service for login
      final result = await _persistentAuthService.login(
        phoneNumber: phoneNumber,
        pin: pin,
        rememberMe: _rememberMe,
        deviceId: deviceId,
      );

      debugPrint('✅ Persistent login result: $result');

      if (result['success'] == true) {
        if (mounted) {
          // Get the current token from persistent auth service
          final token = await _persistentAuthService.getToken();
          if (token != null) {
            await ref.read(authProvider.notifier).login(token);
          }
          debugPrint('🏠 Navigation to /home with persistent session');
          Navigator.of(context).pushReplacementNamed('/home');

          // Fetch and save user profile in the background after navigation
          _loadUserProfileInBackground();
        }
      } else {
        setState(() {
          _errorMessage = result['error'] ?? 'Login failed. Please try again.';
          _showWhatsAppSupport = false;
        });
      }
    } catch (e, stack) {
      debugPrint('❌ Persistent login error: $e');
      debugPrint('📍 Stack trace: $stack');

      final errorStr = e.toString().toLowerCase();

      // Device restriction logic
      bool isDeviceRestriction = false;
      try {
        final errorJson = jsonDecode(e.toString());
        if (errorJson is Map && (errorJson['error'] == 'already_logged_in' ||
            errorJson['error'] == 'already_logged_in ' ||
            errorJson['error'] == 'device_mismatch')) {
          isDeviceRestriction = true;
        }
      } catch (_) {}

      if (isDeviceRestriction) {
        setState(() {
          _errorMessage = 'Already logged in on another device. Please contact admin.';
          _showWhatsAppSupport = true;
        });
        return;
      }

      // Check for network-related errors
      if (errorStr.contains('socketexception') ||
          errorStr.contains('connection refused') ||
          errorStr.contains('certificate_verify_failed') ||
          errorStr.contains('handshakeexception') ||
          errorStr.contains('network error') ||
          errorStr.contains('timeout')) {

        // Show network error dialog with retry option
        if (mounted) {
          context.showNetworkError(
            e.toString(),
            onRetry: () => _login(),
          );
        }
      } else {
        setState(() {
          _errorMessage = 'Login failed. Please try again.';
          _showWhatsAppSupport = false;
        });
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _launchWhatsAppSupport() async {
    final url = 'https://wa.me/$_whatsappNumber?text=Hello, I need help with my KFT Fitness account.';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      setState(() {
        _errorMessage = 'Could not open WhatsApp. Please contact support directly.';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    _isKeyboardVisible = keyboardHeight > 0;

    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: Stack(
        children: [
          // Enhanced Animated Gradient Background
          Positioned.fill(
            child: _buildAnimatedGradientBackground(context),
          ),
          // Main Content
          SafeArea(
            child: LayoutBuilder(
              builder: (context, constraints) {
                final availableHeight = constraints.maxHeight;
                return SingleChildScrollView(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(minHeight: availableHeight),
                    child: IntrinsicHeight(
                      child: Column(
                        children: [
                          // Logo and App Name
                          if (!_isKeyboardVisible || !_phoneStepComplete)
                            _buildLogoSection(),

                          // Fitness Icons
                          if (!_phoneStepComplete && !_showRegistrationForm && !_isTypingPhone)
                            _buildFitnessIcons(),

                          // Main Form Container
                          Expanded(
                            child: _buildMainFormContainer(theme, keyboardHeight),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedGradientBackground(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 2000),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              stops: const [0.0, 0.4, 0.7, 1.0],
              colors: isDarkMode
                  ? [
                      Color.lerp(const Color(0xFF121212), const Color(0xFF1E1E1E), value)!,
                      Color.lerp(const Color(0xFF1E1E1E), const Color(0xFF2C2C2C), value)!,
                      Color.lerp(const Color(0xFF2C2C2C), const Color(0xFF3D5AFE).withOpacity(0.3), value)!,
                      Color.lerp(const Color(0xFF3D5AFE).withOpacity(0.2), const Color(0xFF8187FF).withOpacity(0.2), value)!,
                    ]
                  : [
                      Color.lerp(const Color(0xFF3D5AFE), const Color(0xFF6366F1), value)!,
                      Color.lerp(const Color(0xFF5C6BC0), const Color(0xFF7C3AED), value)!,
                      Color.lerp(const Color(0xFF8187FF), const Color(0xFF9333EA), value)!,
                      Color.lerp(const Color(0xFF9C88FF), const Color(0xFFEC4899), value)!,
                    ],
            ),
          ),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 16, sigmaY: 16),
            child: Container(
              color: Colors.black.withOpacity(isDarkMode ? 0.3 : 0.08),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLogoSection() {
    return Padding(
      padding: const EdgeInsets.only(top: 40, bottom: 16),
      child: Center(
        child: EnhancedGlassCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Logo Container
              Container(
                width: _isKeyboardVisible ? 64 : 100,
                height: _isKeyboardVisible ? 64 : 100,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(_isKeyboardVisible ? 20 : 32),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.white.withOpacity(0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 30,
                      offset: const Offset(0, 15),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(_isKeyboardVisible ? 20 : 32),
                  child: Padding(
                    padding: EdgeInsets.all(_isKeyboardVisible ? 12 : 20),
                    child: Image.asset(
                      'assets/images/logo.webp',
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ),
              SizedBox(height: _isKeyboardVisible ? 12 : 20),
              // App Title
              Text(
                'KFT Fitness',
                style: TextStyle(
                  fontSize: _isKeyboardVisible ? 20 : 28,
                  fontWeight: FontWeight.w800,
                  color: Colors.white,
                  letterSpacing: 1.5,
                  shadows: [
                    Shadow(
                      color: Colors.black.withOpacity(0.3),
                      offset: const Offset(0, 2),
                      blurRadius: 4,
                    ),
                  ],
                ),
              ),
              SizedBox(height: _isKeyboardVisible ? 4 : 8),
              // Subtitle
              Text(
                'Transform Your Body & Mind',
                style: TextStyle(
                  fontSize: _isKeyboardVisible ? 12 : 16,
                  color: Colors.white.withOpacity(0.95),
                  letterSpacing: 0.8,
                  fontWeight: FontWeight.w500,
                  shadows: [
                    Shadow(
                      color: Colors.black.withOpacity(0.2),
                      offset: const Offset(0, 1),
                      blurRadius: 2,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFitnessIcons() {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 400),
      child: Padding(
        padding: const EdgeInsets.only(bottom: 20),
        child: SizedBox(
          height: 70,
          child: Center(
            child: LayoutBuilder(
              builder: (context, constraints) {
                // Check if screen is narrow
                final isNarrow = constraints.maxWidth < 400;
                
                if (isNarrow) {
                  // For narrow screens, show only 2 icons per row
                  return Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _buildFitnessIcon(Icons.fitness_center, 'Strength'),
                          const SizedBox(width: 16),
                          _buildFitnessIcon(Icons.directions_run, 'Cardio'),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _buildFitnessIcon(Icons.restaurant_menu, 'Nutrition'),
                          const SizedBox(width: 16),
                          _buildFitnessIcon(Icons.trending_down, 'Weight Loss'),
                        ],
                      ),
                    ],
                  );
                } else {
                  // For wider screens, show all 4 icons in a row
                  return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildFitnessIcon(Icons.fitness_center, 'Strength'),
                const SizedBox(width: 20),
                _buildFitnessIcon(Icons.directions_run, 'Cardio'),
                const SizedBox(width: 20),
                _buildFitnessIcon(Icons.restaurant_menu, 'Nutrition'),
                const SizedBox(width: 20),
                _buildFitnessIcon(Icons.trending_down, 'Weight Loss'),
              ],
                  );
                }
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFitnessIcon(IconData icon, String label) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.15),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Icon(
            icon,
            color: Colors.white.withOpacity(0.9),
            size: 20,
          ),
        ),
        const SizedBox(height: 6),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 10,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildMainFormContainer(ThemeData theme, double keyboardHeight) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 600),
      curve: Curves.easeOutCubic,
      margin: EdgeInsets.only(
        top: _isKeyboardVisible ? 12 : 20,
        left: 20,
        right: 20,
        bottom: keyboardHeight > 0 ? 0 : 20,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(36),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 40,
            offset: const Offset(0, 20),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 80,
            offset: const Offset(0, 40),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 28),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Form Header
            if (!_isKeyboardVisible || !_phoneStepComplete)
              _buildFormHeader(theme),

            // Form Content
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 400),
              child: _showThankYou
                  ? _buildThankYou(theme)
                  : _showUnregisteredContact
                      ? SizedBox.shrink() // No form, just error + WhatsApp
                      : _showRegistrationForm
                          ? _buildRegistrationForm(theme)
                          : _phoneStepComplete
                              ? _buildPinForm(theme)
                              : _buildPhoneForm(theme),
            ),

            // Error Message
            if (_errorMessage.isNotEmpty)
              _buildErrorMessage(),

            // WhatsApp Support
            if (_showWhatsAppSupport && !_showRegistrationForm && !_isKeyboardVisible)
              _buildWhatsAppSupport(),
          ],
        ),
      ),
    );
  }

  Widget _buildFormHeader(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        children: [
          Text(
            _phoneStepComplete ? 'Enter Your PIN' : 'Welcome Back',
            style: TextStyle(
              fontSize: _isKeyboardVisible ? 20 : 24,
              fontWeight: FontWeight.w700,
              color: const Color(0xFF1F2937),
              letterSpacing: -0.5,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _phoneStepComplete
                ? 'Please enter your 4-digit PIN to continue'
                : 'Sign in with your mobile number to continue your fitness journey',
            style: TextStyle(
              fontSize: _isKeyboardVisible ? 13 : 15,
              color: const Color(0xFF6B7280),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPhoneForm(ThemeData theme) {
    return Column(
      key: const ValueKey('phone_form'),
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Mobile number input with country code picker
        MobileNumberInput(
          controller: _phoneController,
          focusNode: _phoneFocusNode,
          label: 'Mobile Number',
          hint: 'Enter your mobile number',
          initialCountryCode: _selectedCountryCode,
          autofocus: true,
          onChanged: (value) {
            setState(() {
              _isTypingPhone = value.isNotEmpty;
              if (_errorMessage.isNotEmpty) {
                _errorMessage = '';
              }
            });
          },
          onSubmitted: (value) {
            if (!_isLoading) {
              _checkPhone();
            }
          },
        ),

        const SizedBox(height: 24),

        // Continue button with enhanced design
        Container(
          height: 52,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: theme.primaryColor.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: KFTButton(
            label: _isLoading ? 'Checking...' : 'Continue',
            onPressed: _isLoading ? null : _checkPhone,
            isLoading: _isLoading,
            type: KFTButtonType.primary,
          ),
        ),

        const SizedBox(height: 16),

        // Connection status indicator
        if (_isLoading)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(theme.primaryColor),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Verifying phone number...',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildPinForm(ThemeData theme) {
    return Column(
      key: const ValueKey('pin_form'),
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        PinInputWidget(
          onPinCompleted: (pin) {
            if (pin.length == 4) {
              _pinController.text = pin;
              _login();
            }
          },
          onPinChanged: (pin) {
            _pinController.text = pin;
          },
        ),
        const SizedBox(height: 20),

        // Remember Me Checkbox
        Row(
          children: [
            Checkbox(
              value: _rememberMe,
              onChanged: (value) {
                setState(() {
                  _rememberMe = value ?? true;
                });
              },
              activeColor: const Color(0xFF6366F1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Keep me signed in',
                style: TextStyle(
                  fontSize: 14,
                  color: const Color(0xFF6B7280),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            // Info icon with tooltip
            Tooltip(
              message: 'Stay logged in for up to 300 days without re-entering your PIN',
              child: Icon(
                Icons.info_outline,
                size: 16,
                color: const Color(0xFF9CA3AF),
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),
        KFTButton(
          label: 'Sign In',
          onPressed: _isLoading ? null : _login,
          isLoading: _isLoading,
          type: KFTButtonType.primary,
        ),
        const SizedBox(height: 16),
        TextButton(
          onPressed: () {
            setState(() {
              _phoneStepComplete = false;
              _pinController.clear();
              _errorMessage = '';
            });
          },
          child: const Text(
            'Change Username',
            style: TextStyle(
              color: Color(0xFF6366F1),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRegistrationForm(ThemeData theme) {
    return Column(
      key: const ValueKey('registration_form'),
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        KFTTextField(
          controller: _nameController,
          label: 'Full Name',
          hint: 'Enter your full name',
          prefixIcon: Icons.person,
        ),
        const SizedBox(height: 16),
        KFTTextField(
          controller: _usernameController,
          label: 'Username',
          hint: 'Enter your username',
          type: KFTTextFieldType.text,
          prefixIcon: Icons.person,
          isEnabled: false,
        ),
        const SizedBox(height: 24),
        KFTButton(
          label: 'Register',
          onPressed: _isLoading ? null : () {
            // Registration logic would go here
            setState(() {
              _showThankYou = true;
            });
          },
          isLoading: _isLoading,
          type: KFTButtonType.primary,
        ),
      ],
    );
  }

  Widget _buildThankYou(ThemeData theme) {
    return Column(
      key: const ValueKey('thank_you'),
      children: [
        Icon(
          Icons.check_circle,
          size: 64,
          color: Colors.green,
        ),
        const SizedBox(height: 16),
        Text(
          'Thank You!',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: const Color(0xFF1F2937),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Your registration request has been submitted. We will contact you soon.',
          style: TextStyle(
            fontSize: 15,
            color: const Color(0xFF6B7280),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildErrorMessage() {
    return Padding(
      padding: const EdgeInsets.only(top: 16),
      child: Column(
        children: [
          ColoredCard(
            color: const Color(0xFFFEF2F2),
            borderColor: const Color(0xFFFECACA),
            icon: Icons.error_outline,
            iconColor: const Color(0xFFDC2626),
            text: _errorMessage,
            textColor: const Color(0xFFDC2626),
          ),
          if (_errorMessage == 'Already logged in on another device. Please contact admin.' &&
              _showWhatsAppSupport && !_showRegistrationForm)
            Padding(
              padding: const EdgeInsets.only(top: 12),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _launchWhatsAppSupport,
                  icon: const FaIcon(FontAwesomeIcons.whatsapp, color: Colors.white),
                  label: const Text('Contact Admin on WhatsApp'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF25D366),
                    foregroundColor: Colors.white,
                    textStyle: const TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildWhatsAppSupport() {
    return Padding(
      padding: const EdgeInsets.only(top: 16),
      child: TextButton.icon(
        onPressed: _launchWhatsAppSupport,
        icon: const Icon(Icons.chat, size: 16),
        label: const Text('Contact Support'),
        style: TextButton.styleFrom(
          foregroundColor: const Color(0xFF6366F1),
          textStyle: const TextStyle(fontSize: 13),
        ),
      ),
    );
  }

  Future<void> _loadUserProfileInBackground() async {
    try {
      final userProfile = await _apiService.getUserProfile();
      debugPrint('👤 User profile response: $userProfile');

      if (userProfile == null) {
        setState(() {
          _errorMessage = 'Failed to load user profile. Please try again or contact support.';
          _showWhatsAppSupport = true;
        });
        return;
      }

      await _userService.saveUserProfile(userProfile);
    } catch (e) {
      debugPrint('❌ Error loading user profile: $e');
      setState(() {
        _errorMessage = 'Failed to load user profile. Please try again or contact support.';
        _showWhatsAppSupport = true;
      });
    }
  }
}
