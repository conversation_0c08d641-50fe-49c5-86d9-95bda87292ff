import 'package:flutter/material.dart';
import '../widgets/mobile_number_input.dart';
import '../widgets/country_code_picker.dart';
import '../design_system/kft_design_system.dart';

/// Test page for mobile number login functionality
class MobileLoginTest extends StatefulWidget {
  const MobileLoginTest({Key? key}) : super(key: key);

  @override
  State<MobileLoginTest> createState() => _MobileLoginTestState();
}

class _MobileLoginTestState extends State<MobileLoginTest> {
  final _phoneController = TextEditingController();
  String _selectedCountryCode = '+91';
  String _fullPhoneNumber = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mobile Login Test'),
        backgroundColor: KFTDesignSystem.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Test Mobile Number Input',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            // Country Code Picker Test
            const Text(
              'Country Code Picker:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 10),
            CountryCodePicker(
              selectedCountryCode: _selectedCountryCode,
              onCountryCodeChanged: (code) {
                setState(() {
                  _selectedCountryCode = code;
                });
              },
            ),
            
            const SizedBox(height: 30),
            
            // Mobile Number Input Test
            const Text(
              'Mobile Number Input:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 10),
            MobileNumberInput(
              controller: _phoneController,
              label: 'Mobile Number',
              hint: 'Enter your mobile number',
              initialCountryCode: _selectedCountryCode,
              onChanged: (value) {
                setState(() {
                  _fullPhoneNumber = value;
                });
              },
            ),
            
            const SizedBox(height: 30),
            
            // Display Results
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Results:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 10),
                  Text('Selected Country Code: $_selectedCountryCode'),
                  Text('Phone Number: ${_phoneController.text}'),
                  Text('Full Phone Number: $_fullPhoneNumber'),
                ],
              ),
            ),
            
            const SizedBox(height: 30),
            
            // Test Button
            ElevatedButton(
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Full Phone Number: $_fullPhoneNumber'),
                    backgroundColor: KFTDesignSystem.primaryColor,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: KFTDesignSystem.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Test Phone Number',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }
}
