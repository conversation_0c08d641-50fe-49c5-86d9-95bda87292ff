import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'dart:io' show Platform;
import '../models/course.dart';
import '../models/purchasable_course.dart';
import '../services/api_service.dart';
import '../widgets/course_videos_page.dart';
import '../design_system/kft_design_system.dart';
import '../widgets/kft_button.dart';
import '../utils/animations.dart';
import '../widgets/hydrated_progress_widgets.dart';

class CoursesPage extends StatefulWidget {
  const CoursesPage({Key? key}) : super(key: key);

  @override
  _CoursesPageState createState() => _CoursesPageState();
}

class _CoursesPageState extends State<CoursesPage> with SingleTickerProviderStateMixin {
  final ApiService _apiService = ApiService();
  bool _isLoading = true;
  List<Course> _enrolledCourses = [];
  List<PurchasableCourse> _availableCourses = [];
  String _errorMessage = '';
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isScreenBlocked = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadAllCourses();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadAllCourses() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    // Load enrolled and available courses separately to handle different error cases
    await _loadEnrolledCourses();
    await _loadAvailableCourses();

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _loadEnrolledCourses() async {
    try {
      final response = await _apiService.getUserCourses();

      if (response['success'] == true && response['courses'] != null) {
        final List<Course> courses = [];
        for (var courseData in response['courses']) {
          courses.add(Course.fromJson(courseData));
        }

        setState(() {
          _enrolledCourses = courses;
        });
      } else {
        setState(() {
          _enrolledCourses = [];
        });
      }
    } catch (e) {
      print('Error loading enrolled courses: ${e.toString()}');
      // Don't set global error message, just show empty state for enrolled courses
      setState(() {
        _enrolledCourses = [];
      });
    }
  }

  Future<void> _loadAvailableCourses() async {
    try {
      final response = await _apiService.getAvailableCourses();

      if (response['success'] == true && response['courses'] != null) {
        final List<PurchasableCourse> courses = [];
        for (var courseData in response['courses']) {
          courses.add(PurchasableCourse.fromJson(courseData));
        }

        setState(() {
          _availableCourses = courses;
        });
      } else {
        setState(() {
          _availableCourses = [];
        });
      }
    } catch (e) {
      print('Error loading available courses: ${e.toString()}');
      // Don't set global error message, just show empty state for available courses
      setState(() {
        _availableCourses = [];
      });
    }
  }

  // Launch WhatsApp with course details
  Future<void> _launchWhatsAppWithCourse(PurchasableCourse course) async {
    // Get user's phone number from API service
    String userPhone = '';
    String userName = '';
    try {
      final apiService = ApiService();
      if (await apiService.isLoggedIn()) {
        final userProfile = await apiService.getUserProfile();
        userPhone = userProfile.phoneNumber;
        userName = userProfile.name;
        print('Using user phone number: $userPhone');
      } else {
        print('User not logged in, no phone number available');
      }
    } catch (e) {
      print('Error getting user profile: $e');
    }

    // Use course-specific WhatsApp number if available
    final phone = course.whatsappNumber.isNotEmpty
        ? course.whatsappNumber
        : '+1234567890'; // Replace with your default WhatsApp number

    String message;
    if (course.isPurchased) {
      message = 'Hi, I\'m already enrolled in the course: *${course.title}* (ID: ${course.id})\n'
          'I need assistance with this course.\n'
          'Course duration: ${course.durationWeeks} weeks\n'
          'Please help me with my query.';
    } else {
      message = 'Hi, I\'m interested in enrolling in the course: *${course.title}* (ID: ${course.id})\n'
          'Price: ₹${course.price.final_.toStringAsFixed(2)}\n'
          'Duration: ${course.durationWeeks} weeks\n'
          'Please provide more information about enrollment.';
    }

    // Add user details if available
    if (userPhone.isNotEmpty || userName.isNotEmpty) {
      message += '\n\nMy Details:';
      if (userName.isNotEmpty) {
        message += '\nName: $userName';
      }
      if (userPhone.isNotEmpty) {
        message += '\nPhone: $userPhone';
      }
    }

    // Add device info
    final deviceInfo = 'Flutter App';
    message += '\nDevice: $deviceInfo';

    final encodedMessage = Uri.encodeComponent(message);
    final whatsappUrl = 'https://wa.me/$phone?text=$encodedMessage';

    try {
      final uri = Uri.parse(whatsappUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
        print('Launched WhatsApp with message containing user phone number');
      } else {
        print('Could not launch WhatsApp URL: $whatsappUrl');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not launch WhatsApp. Please make sure it\'s installed.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      print('Error launching WhatsApp: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error launching WhatsApp: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: KFTDesignSystem.getBackgroundColor(context),
      appBar: AppBar(
        title: const Text('Courses', style: TextStyle(fontWeight: FontWeight.bold)),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAllCourses,
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48),
          child: Container(
            alignment: Alignment.centerLeft,
            child: TabBar(
              controller: _tabController,
              indicator: UnderlineTabIndicator(
                borderSide: BorderSide(
                  color: theme.colorScheme.primary,
                  width: 2.5,
                ),
                insets: EdgeInsets.symmetric(horizontal: 24),
              ),
              labelColor: theme.colorScheme.primary,
              unselectedLabelColor: theme.colorScheme.onSurface.withOpacity(0.6),
              labelStyle: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.normal, fontSize: 16),
              overlayColor: MaterialStateProperty.all(Colors.transparent),
              tabs: const [
                Tab(text: 'My Courses'),
                Tab(text: 'All Courses'),
              ],
            ),
          ),
        ),
      ),
      body: Stack(
        children: [
          _buildBody(),
          if (_isScreenBlocked)
            Positioned.fill(
              child: Container(
                color: Colors.black,
                child: const Center(
                  child: Text(
                    'Screen capture is blocked',
                    style: TextStyle(color: Colors.white, fontSize: 20),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_errorMessage.isNotEmpty) {
      // Check if this is an authentication error
      bool isAuthError = _errorMessage.contains('log in') ||
                         _errorMessage.contains('session has expired') ||
                         _errorMessage.contains('401');

      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isAuthError ? Icons.lock_outline : Icons.error_outline,
              color: isAuthError ? Colors.orange : Colors.red,
              size: 48
            ),
            const SizedBox(height: 16),
            Text(
              'Oops! $_errorMessage',
              style: TextStyle(
                color: isAuthError ? Colors.orange : Colors.red,
                fontSize: 16
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            if (isAuthError)
              ElevatedButton(
                onPressed: () {
                  // Navigate to login page
                  Navigator.of(context).pushReplacementNamed('/login');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                ),
                child: const Text('Go to Login'),
              )
            else
              ElevatedButton(
                onPressed: _loadAllCourses,
                child: const Text('Retry'),
              ),
          ],
        ),
      );
    }
    return TabBarView(
      controller: _tabController,
      children: [
        _buildEnrolledCoursesTab(),
        _buildAvailableCoursesTab(),
      ],
    );
  }

  Widget _buildEnrolledCoursesTab() {
    final filtered = _enrolledCourses.where((c) => c.title.toLowerCase().contains(_searchQuery.toLowerCase())).toList();
    if (filtered.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.school_outlined, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            const Text('No enrolled courses found', style: TextStyle(fontSize: 16)),
            const SizedBox(height: 8),
            const Text(
              'Courses assigned by your trainer will appear here',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            KFTButton(
              label: 'Browse All Courses',
              onPressed: () => _tabController.animateTo(1),
              type: KFTButtonType.primary,
            ),
          ],
        ),
      );
    }
    return RefreshIndicator(
      onRefresh: _loadAllCourses,
      child: ListView.separated(
        padding: const EdgeInsets.all(20),
        itemCount: filtered.length,
        separatorBuilder: (_, __) => const SizedBox(height: 18),
        itemBuilder: (context, index) {
          final course = filtered[index];
          return _AnimatedCourseCard(
            child: _buildEnrolledCourseCard(course),
          );
        },
      ),
    );
  }

  Widget _buildAvailableCoursesTab() {
    final filtered = _availableCourses.where((c) => c.title.toLowerCase().contains(_searchQuery.toLowerCase())).toList();
    if (filtered.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.school_outlined, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            const Text('No available courses found', style: TextStyle(fontSize: 16)),
            const SizedBox(height: 16),
            KFTButton(
              label: 'Refresh',
              onPressed: _loadAllCourses,
              type: KFTButtonType.primary,
            ),
          ],
        ),
      );
    }
    return RefreshIndicator(
      onRefresh: _loadAllCourses,
      child: ListView.separated(
        padding: const EdgeInsets.all(20),
        itemCount: filtered.length,
        separatorBuilder: (_, __) => const SizedBox(height: 18),
        itemBuilder: (context, index) {
          final course = filtered[index];
          return _AnimatedCourseCard(
            child: _buildAvailableCourseCard(course),
          );
        },
      ),
    );
  }

  Widget _buildEnrolledCourseCard(Course course) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return _AnimatedCourseCard(
      child: Card(
        margin: const EdgeInsets.only(bottom: 20),
        elevation: 0,
        shadowColor: isDarkMode ? Colors.black : Colors.black.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: isDarkMode
              ? BorderSide(color: KFTDesignSystem.getBorderColor(context), width: 0.5)
              : BorderSide.none,
        ),
        color: KFTDesignSystem.getCardColor(context),
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => CourseVideosPage(courseId: course.id),
              ),
            ).then((_) => _loadAllCourses());
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Course thumbnail
              if (course.thumbnailUrl != null && course.thumbnailUrl!.isNotEmpty)
                ClipRRect(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                  child: Image.network(
                    getFullImageUrl(course.thumbnailUrl),
                    height: 160,
                    fit: BoxFit.cover,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Container(
                        height: 160,
                        color: KFTDesignSystem.getCardColor(context),
                        child: Center(
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              KFTDesignSystem.primaryColor.withOpacity(0.6),
                            ),
                          ),
                        ),
                      );
                    },
                    errorBuilder: (context, error, stackTrace) {
                      print('Course thumbnail loading error: $error');
                      return Container(
                        height: 160,
                        color: KFTDesignSystem.getCardColor(context),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.fitness_center,
                              size: 48,
                              color: KFTDesignSystem.getTextSecondaryColor(context),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Course',
                              style: TextStyle(
                                color: KFTDesignSystem.getTextSecondaryColor(context),
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                )
              else
                Container(
                  height: 160,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withOpacity(theme.brightness == Brightness.dark ? 0.15 : 0.07),
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                  ),
                  child: Icon(
                    Icons.school,
                    size: 64,
                    color: theme.colorScheme.primary,
                  ),
                ),
              // Course details
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            course.title,
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: KFTDesignSystem.getTextPrimaryColor(context),
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        _buildStatusBadge(course.status),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      course.description,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: KFTDesignSystem.getTextSecondaryColor(context),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 16,
                          color: isDarkMode ? theme.colorScheme.onSurface.withOpacity(0.5) : KFTDesignSystem.getTextTertiaryColor(context),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${course.durationWeeks} weeks',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: isDarkMode ? theme.colorScheme.onSurface.withOpacity(0.6) : KFTDesignSystem.getTextSecondaryColor(context),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Icon(
                          Icons.video_library,
                          size: 16,
                          color: isDarkMode ? theme.colorScheme.onSurface.withOpacity(0.5) : KFTDesignSystem.getTextTertiaryColor(context),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${course.unlockedVideos}/${course.totalVideos} videos',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: isDarkMode ? theme.colorScheme.onSurface.withOpacity(0.6) : KFTDesignSystem.getTextSecondaryColor(context),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // Progress bar
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Progress',
                              style: theme.textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.w500,
                                color: isDarkMode ? theme.colorScheme.onSurface.withOpacity(0.7) : KFTDesignSystem.getTextSecondaryColor(context),
                              ),
                            ),
                            Text(
                              '${course.progressPercentage}%',
                              style: theme.textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.w500,
                                color: isDarkMode ? theme.colorScheme.onSurface.withOpacity(0.7) : KFTDesignSystem.getTextSecondaryColor(context),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        HydratedProgressBar(
                          progress: course.progressPercentage / 100,
                          height: 6,
                          animated: true,
                          showShimmer: true,
                          progressGradient: course.progressPercentage >= 100
                              ? const LinearGradient(
                                  colors: [Color(0xFF10B981), Color(0xFF14B8A6)],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                )
                              : course.progressPercentage >= 70
                                  ? const LinearGradient(
                                      colors: [Color(0xFF06B6D4), Color(0xFF0891B2)],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    )
                                  : const LinearGradient(
                                      colors: [Color(0xFF22D3EE), Color(0xFF06B6D4)],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvailableCourseCard(PurchasableCourse course) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return _AnimatedCourseCard(
      child: Card(
        margin: const EdgeInsets.only(bottom: 20),
        elevation: 0,
        shadowColor: isDarkMode ? Colors.black : Colors.black.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: isDarkMode
              ? BorderSide(color: KFTDesignSystem.getBorderColor(context), width: 0.5)
              : BorderSide.none,
        ),
        color: KFTDesignSystem.getCardColor(context),
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            _launchWhatsAppWithCourse(course);
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Course thumbnail
              Stack(
                children: [
                  if (course.thumbnailUrl != null && course.thumbnailUrl!.isNotEmpty)
                    ClipRRect(
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                      child: Image.network(
                        getFullImageUrl(course.thumbnailUrl),
                        height: 160,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Container(
                            height: 160,
                            color: KFTDesignSystem.getCardColor(context),
                            child: Center(
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  KFTDesignSystem.primaryColor.withOpacity(0.6),
                                ),
                              ),
                            ),
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          print('Course thumbnail loading error: $error');
                          return Container(
                            height: 160,
                            color: KFTDesignSystem.getCardColor(context),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.fitness_center,
                                  size: 48,
                                  color: KFTDesignSystem.getTextSecondaryColor(context),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Course',
                                  style: TextStyle(
                                    color: KFTDesignSystem.getTextSecondaryColor(context),
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    )
                  else
                    Container(
                      height: 160,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(theme.brightness == Brightness.dark ? 0.15 : 0.07),
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                      ),
                      child: Icon(
                        Icons.school,
                        size: 64,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  // Featured badge
                  if (course.isFeatured)
                    Positioned(
                      top: 12,
                      left: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: isDarkMode ? const Color(0xFFFFD166) : Colors.amber,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.star,
                              size: 16,
                              color: Colors.white,
                            ),
                            SizedBox(width: 4),
                            Text(
                              'Featured',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  // Category badge
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? Colors.black.withOpacity(0.8)
                            : Colors.black.withOpacity(0.6),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        course.category,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              // Course details
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Text(
                            course.title,
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 8),
                        _buildPriceTag(course),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      course.description,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 16,
                          color: isDarkMode ? theme.colorScheme.onSurface.withOpacity(0.5) : KFTDesignSystem.getTextTertiaryColor(context),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${course.durationWeeks} weeks',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: isDarkMode ? theme.colorScheme.onSurface.withOpacity(0.6) : KFTDesignSystem.getTextSecondaryColor(context),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Icon(
                          Icons.video_library,
                          size: 16,
                          color: isDarkMode ? theme.colorScheme.onSurface.withOpacity(0.5) : KFTDesignSystem.getTextTertiaryColor(context),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${course.totalVideos} videos',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: isDarkMode ? theme.colorScheme.onSurface.withOpacity(0.6) : KFTDesignSystem.getTextSecondaryColor(context),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    KFTButton(
                      label: course.isPurchased ? 'Contact Support' : 'Enroll Now',
                      icon: Icons.message,
                      onPressed: () => _launchWhatsAppWithCourse(course),
                      type: course.isPurchased ? KFTButtonType.secondary : KFTButtonType.primary,
                      isFullWidth: true,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriceTag(PurchasableCourse course) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final enrolledColor = isDarkMode ? const Color(0xFF2196F3) : Colors.blue;

    // If the course is already purchased, show an "Enrolled" badge
    if (course.isPurchased) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: enrolledColor.withOpacity(isDarkMode ? 0.2 : 0.1),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: enrolledColor),
        ),
        child: Text(
          'Enrolled',
          style: TextStyle(
            color: enrolledColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    // If the course is free
    if (course.price.original == 0) {
      final freeColor = isDarkMode ? const Color(0xFF4CAF50) : Colors.green;
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: freeColor.withOpacity(isDarkMode ? 0.2 : 0.1),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: freeColor),
        ),
        child: Text(
          'Free',
          style: TextStyle(
            color: freeColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    // For paid courses
    final priceColor = isDarkMode ? const Color(0xFF2196F3) : Colors.blue;
    final strikethroughColor = isDarkMode ? Colors.grey[400] : Colors.grey;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        if (course.price.hasDiscount)
          Text(
            '₹${course.price.original.toStringAsFixed(2)}',
            style: TextStyle(
              decoration: TextDecoration.lineThrough,
              color: strikethroughColor,
              fontSize: 12,
            ),
          ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: priceColor.withOpacity(isDarkMode ? 0.2 : 0.1),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: priceColor),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '₹${course.price.final_.toStringAsFixed(2)}',
                style: TextStyle(
                  color: priceColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (course.price.hasDiscount) ...[
                const SizedBox(width: 4),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  decoration: BoxDecoration(
                    color: isDarkMode ? const Color(0xFFE57373) : Colors.red,
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: Text(
                    '-${course.price.discountPercentage}%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusBadge(String status) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    Color badgeColor;
    String badgeText;

    switch (status.toLowerCase()) {
      case 'active':
        badgeColor = isDarkMode ? const Color(0xFF4CAF50) : Colors.green;
        badgeText = 'Active';
        break;
      case 'completed':
        badgeColor = isDarkMode ? const Color(0xFF2196F3) : Colors.blue;
        badgeText = 'Completed';
        break;
      case 'cancelled':
        badgeColor = isDarkMode ? Colors.grey[400]! : Colors.grey;
        badgeText = 'Cancelled';
        break;
      default:
        badgeColor = isDarkMode ? const Color(0xFFFFB74D) : Colors.orange;
        badgeText = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(isDarkMode ? 0.2 : 0.1),
        border: Border.all(color: badgeColor),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        badgeText,
        style: TextStyle(
          color: badgeColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Color _getProgressColor(int percentage) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    if (percentage < 30) {
      return isDarkMode ? const Color(0xFFE57373) : Colors.red;
    } else if (percentage < 70) {
      return isDarkMode ? const Color(0xFFFFB74D) : Colors.orange;
    } else {
      return isDarkMode ? const Color(0xFF4CAF50) : Colors.green;
    }
  }
}

class _AnimatedCourseCard extends StatefulWidget {
  final Widget child;
  const _AnimatedCourseCard({required this.child});
  @override
  State<_AnimatedCourseCard> createState() => _AnimatedCourseCardState();
}

class _AnimatedCourseCardState extends State<_AnimatedCourseCard> {
  bool _hovering = false;
  bool _pressed = false;

  void _setHover(bool value) => setState(() => _hovering = value);
  void _setPressed(bool value) => setState(() => _pressed = value);

  @override
  Widget build(BuildContext context) {
    final bool animate = _hovering || _pressed;
    return MouseRegion(
      onEnter: (_) => _setHover(true),
      onExit: (_) => _setHover(false),
      child: GestureDetector(
        onTapDown: (_) => _setPressed(true),
        onTapUp: (_) => _setPressed(false),
        onTapCancel: () => _setPressed(false),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 160),
          curve: Curves.easeOut,
          transform: animate ? (Matrix4.identity()..scale(0.98)) : Matrix4.identity(),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(22),
            boxShadow: animate
                ? [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.10),
                      blurRadius: 24,
                      offset: const Offset(0, 8),
                    ),
                  ]
                : [],
          ),
          child: widget.child,
        ),
      ),
    );
  }
}

String getFullImageUrl(String? url) {
  if (url == null || url.isEmpty) return '';
  if (url.startsWith('http://') || url.startsWith('https://')) return url;
  final cleanPath = url.startsWith('/') ? url.substring(1) : url;
  return '${ApiService.baseUrl.replaceAll('/api', '')}/$cleanPath';
}
