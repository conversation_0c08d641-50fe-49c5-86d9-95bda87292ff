import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/user_profile.dart';
import '../services/user_service.dart';
import '../services/api_service.dart';
import '../widgets/health_metrics_card.dart';
import '../widgets/bmi_indicator_card.dart';
import '../widgets/premium_header.dart';
import '../widgets/premium_card.dart';
import '../design_system/kft_design_system.dart';
import '../widgets/profile_avatar_enhanced.dart';
import '../widgets/default_avatar_widget.dart';
import '../widgets/water_goal_quick_edit_widget.dart';
import '../widgets/enhanced_whatsapp_support_fab.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({Key? key}) : super(key: key);

  @override
  _ProfilePageState createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final UserService _userService = UserService();
  late Future<UserProfile> _profileFuture;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadProfile();
  }

  void _loadProfile() {
    _profileFuture = _userService.getUserProfile();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: KFTDesignSystem.getBackgroundColor(context),
      body: SafeArea(
        child: FutureBuilder<UserProfile>(
          future: _profileFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return _buildErrorState(snapshot.error.toString());
            }

            if (!snapshot.hasData) {
              return _buildErrorState('No profile data available');
            }

            final userProfile = snapshot.data!;

            return Column(
              children: [
                // Header
                PremiumHeader(
                  title: 'Profile',
                  subtitle: 'Your personal information',
                  actions: [],
                  height: 160,
                ),

                // Content
                Expanded(
                  child: ListView(
                    controller: _scrollController,
                    padding: EdgeInsets.zero,
                    children: [
                      _buildProfileCard(context, userProfile),
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildSectionHeader(context, 'Health Metrics'),
                            _buildHealthMetricsCards(context, userProfile),
                          ],
                        ),
                      ),
                      const SizedBox(height: 100),
                      _buildSupportSection(context),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline_rounded,
            size: 64,
            color: KFTDesignSystem.getTextPrimaryColor(context),
          ),
          const SizedBox(height: 16),
          Text(
            'Error: $message',
            style: TextStyle(
              fontSize: 16,
              color: KFTDesignSystem.getTextPrimaryColor(context),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _loadProfile();
              });
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildProfileCard(BuildContext context, UserProfile userProfile) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: PremiumCard(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Row(
              children: [
                _buildProfileAvatar(userProfile),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        userProfile.name,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          letterSpacing: -0.5,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          _buildMetricChip(
                            icon: Icons.height,
                            value: '${userProfile.height.toStringAsFixed(1)} cm',
                            label: 'Height',
                          ),
                          const SizedBox(width: 12),
                          _buildMetricChip(
                            icon: Icons.monitor_weight_outlined,
                            value: '${userProfile.weight.toStringAsFixed(1)} kg',
                            label: 'Weight',
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _buildProgressSection(userProfile),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildStatsGrid(userProfile),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileAvatar(UserProfile userProfile) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: DefaultAvatarWidget(
        name: userProfile.name,
        radius: 36,
      ),
    );
  }

  Widget _buildMetricChip({
    required IconData icon,
    required String value,
    required String label,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: KFTDesignSystem.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
            icon,
                            size: 16,
            color: KFTDesignSystem.primaryColor,
                          ),
          const SizedBox(width: 6),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
                          Text(
                value,
                            style: TextStyle(
                              fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: KFTDesignSystem.primaryColor,
                          ),
              ),
                          Text(
                label,
                            style: TextStyle(
                  fontSize: 12,
                  color: KFTDesignSystem.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ],
      ),
    );
  }

  Widget _buildProgressSection(UserProfile userProfile) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Your Progress',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildProgressIndicator(
                value: 0.75,
                label: 'Workouts',
                icon: Icons.fitness_center,
                color: KFTDesignSystem.primaryColor,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildProgressIndicator(
                value: 0.6,
                label: 'Goals',
                icon: Icons.flag,
                color: KFTDesignSystem.secondaryColor,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildProgressIndicator(
                value: 0.9,
                label: 'Streak',
                icon: Icons.local_fire_department,
                color: KFTDesignSystem.accentColor,
                  ),
                ),
              ],
            ),
      ],
    );
  }

  Widget _buildProgressIndicator({
    required double value,
    required String label,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
                ),
          const SizedBox(height: 8),
          Text(
            '${(value * 100).toInt()}%',
                  style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: KFTDesignSystem.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsGrid(UserProfile userProfile) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard(
          title: 'Total Workouts',
          value: '24',
          icon: Icons.fitness_center,
          color: KFTDesignSystem.primaryColor,
        ),
        _buildStatCard(
          title: 'Active Streak',
          value: '7 days',
          icon: Icons.local_fire_department,
          color: KFTDesignSystem.accentColor,
        ),
        _buildStatCard(
          title: 'Calories Burned',
          value: '12,450',
          icon: Icons.whatshot,
          color: KFTDesignSystem.secondaryColor,
                  ),
        _buildStatCard(
          title: 'Workout Time',
          value: '18.5 hrs',
          icon: Icons.timer,
          color: KFTDesignSystem.primaryColor,
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: color,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  color: KFTDesignSystem.textSecondaryColor,
                ),
          ),
        ],
      ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHealthStatItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: KFTDesignSystem.getTextPrimaryColor(context),
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: KFTDesignSystem.getTextSecondaryColor(context),
          ),
        ),
      ],
    );
  }

  Color _getBMIColor(double bmi) {
    if (bmi < 18.5) {
      return Colors.blue; // Underweight
    } else if (bmi < 25) {
      return Colors.green; // Normal
    } else if (bmi < 30) {
      return Colors.orange; // Overweight
    } else {
      return Colors.red; // Obese
    }
  }

  Widget _buildHealthMetricsCards(BuildContext context, UserProfile userProfile) {
    return Column(
      children: [
        PremiumCard(
          margin: const EdgeInsets.only(bottom: 16),
          child: HealthMetricsCard(
            bmi: userProfile.currentBMI,
            bodyFat: userProfile.bodyFat ?? 0.0,
            muscleMass: userProfile.muscleMass ?? 0.0,
            waterPercentage: userProfile.waterPercentage ?? 0.0,
          ),
        ),
        PremiumCard(
          child: BMIIndicatorCard(
            userProfile: userProfile,
            onWeightUpdated: (weight) async {
              try {
                // Use the enhanced API service for real-time updates
                final apiService = ApiService();
                final response = await apiService.updateHealthMetrics(weight: weight);

                if (response['success'] == true && response['profile'] != null) {
                  // Update local profile with server response
                  final updatedProfile = UserProfile.fromJson(response['profile']);
                  await _userService.saveUserProfile(updatedProfile);

                  // Show success message
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Weight updated successfully! Changes will be visible after refresh.'),
                        backgroundColor: Colors.green,
                        duration: Duration(seconds: 3),
                      ),
                    );
                  }
                } else {
                  // Fallback to local update
                  await _userService.updateWeight(weight);
                }
              } catch (e) {
                print('Error updating weight: $e');
                // Fallback to local update
                await _userService.updateWeight(weight);
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Weight updated successfully! Changes will be visible after refresh.'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              }
            },
          ),
        ),
      ],
    );
  }

  void _showEditProfileDialog(BuildContext context, UserProfile userProfile) {
    final nameController = TextEditingController(text: userProfile.name);
    final heightController = TextEditingController(
      text: userProfile.height.toString(),
    );
    final weightController = TextEditingController(
      text: userProfile.weight.toString(),
    );

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Edit Profile',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: KFTDesignSystem.getTextPrimaryColor(context),
                ),
              ),
              const SizedBox(height: 24),
              _buildInputField(
                controller: nameController,
                label: 'Full Name',
                icon: Icons.person_outline,
              ),
              const SizedBox(height: 16),
              _buildInputField(
                controller: heightController,
                label: 'Height (cm)',
                icon: Icons.height,
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              _buildInputField(
                controller: weightController,
                label: 'Weight (kg)',
                icon: Icons.monitor_weight_outlined,
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 32),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      foregroundColor: KFTDesignSystem.getTextSecondaryColor(context),
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                    ),
                    child: const Text(
                      'Cancel',
                      style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () async {
                      final name = nameController.text;
                      final height = double.tryParse(heightController.text);
                      final weight = double.tryParse(weightController.text);

                      // 1. Update local profile instantly
                      if (name.isNotEmpty) {
                        await _userService.updateName(name);
                      }
                      if (height != null && height > 0) {
                        await _userService.updateHeight(height);
                      }
                      if (weight != null && weight > 0) {
                        await _userService.updateWeight(weight);
                      }

                      // 2. Refresh UI and close dialog immediately
                      if (context.mounted) {
                        setState(() {
                          _loadProfile();
                        });
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Profile updated! Changes will sync in background.'),
                            backgroundColor: Colors.green,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }

                      // 3. Sync with server in background (fire-and-forget)
                      Future(() async {
                        try {
                          final apiService = ApiService();
                          await apiService.updateProfile(
                            name: name.isNotEmpty ? name : null,
                            height: height != null && height > 0 ? height : null,
                            weight: weight != null && weight > 0 ? weight : null,
                          );
                        } catch (e) {
                          print('Background profile sync failed: $e');
                        }
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: KFTDesignSystem.getTextPrimaryColor(context),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: const Text(
                      'Save',
                      style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
  }) {
    return TextField(
      controller: controller,
      keyboardType: keyboardType,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: KFTDesignSystem.getTextPrimaryColor(context)),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: KFTDesignSystem.getTextPrimaryColor(context)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: KFTDesignSystem.getTextPrimaryColor(context)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: KFTDesignSystem.getTextPrimaryColor(context), width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
    );
  }

  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: KFTDesignSystem.getTextPrimaryColor(context).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.settings_outlined,
                        color: KFTDesignSystem.getTextPrimaryColor(context),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'Settings',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              // Removed Notifications settings item
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Divider(height: 32),
              ),
              _buildSettingsItem(
                icon: Icons.logout,
                title: 'Logout',
                subtitle: 'Sign out of your account',
                iconColor: KFTDesignSystem.getTextPrimaryColor(context),
                titleColor: KFTDesignSystem.getTextPrimaryColor(context),
                onTap: () {
                  Navigator.pop(context); // Close dialog
                  _confirmLogout(context);
                },
              ),
              const SizedBox(height: 16),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: KFTDesignSystem.getTextPrimaryColor(context),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text('Close'),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Color? iconColor,
    Color? titleColor,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: (iconColor ?? KFTDesignSystem.getTextPrimaryColor(context)).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: iconColor ?? KFTDesignSystem.getTextPrimaryColor(context),
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: titleColor ?? KFTDesignSystem.getTextPrimaryColor(context),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: KFTDesignSystem.getTextSecondaryColor(context),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios_rounded,
              size: 16,
              color: KFTDesignSystem.getTextSecondaryColor(context),
            ),
          ],
        ),
      ),
    );
  }

  void _confirmLogout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: KFTDesignSystem.getTextPrimaryColor(context).withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.logout_rounded,
                  color: KFTDesignSystem.getTextPrimaryColor(context),
                  size: 32,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Logout',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Are you sure you want to logout from your account?',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: KFTDesignSystem.getTextSecondaryColor(context),
                ),
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: KFTDesignSystem.getTextSecondaryColor(context),
                      side: BorderSide(color: KFTDesignSystem.getTextPrimaryColor(context)),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () async {
                      Navigator.pop(context); // Close dialog
                      await _userService.logout();
                      if (context.mounted) {
                        Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: KFTDesignSystem.getTextPrimaryColor(context),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text('Logout'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSupportSection(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.outlineVariant,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.message_outlined,
                color: Colors.green,
                size: 24,
              ),
            ),
            title: const Text(
              'WhatsApp Support',
              style: TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: const Text(
              'Get help from our support team',
              style: TextStyle(
                fontSize: 12,
              ),
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () async {
              try {
                final whatsappUrl = Uri.parse('https://wa.me/918714151952'); // Updated support number
                if (await canLaunchUrl(whatsappUrl)) {
                  await launchUrl(whatsappUrl, mode: LaunchMode.externalApplication);
                } else {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Could not launch WhatsApp'),
                      ),
                    );
                  }
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('An error occurred'),
                    ),
                  );
                }
              }
            },
          ),
        ],
      ),
    );
  }
}