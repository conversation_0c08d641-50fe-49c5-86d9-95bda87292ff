import '../utils/video_security_helper.dart';

class CourseVideo {
  final int id;
  final String title;
  final String description;
  final String videoUrl;
  final String? thumbnailUrl;
  final int? durationMinutes;
  final int weekNumber;
  final int sequenceNumber;
  final bool isUnlocked;
  final bool isCompleted;
  final String? unlockDate;
  final String? completionDate;
  final int? watchDurationSeconds;
  final int? lastPositionSeconds;
  // Vimeo-specific fields
  final String videoProvider;
  final String? videoEmbedUrl;
  final String? videoId;
  // Unlock status fields
  final int? daysUntilUnlock;
  final int? weeksUntilUnlock;
  final bool isCurrentWeek;
  // Course reference
  int? courseId;

  CourseVideo({
    required this.id,
    required this.title,
    required this.description,
    required this.videoUrl,
    this.thumbnailUrl,
    this.durationMinutes,
    required this.weekNumber,
    required this.sequenceNumber,
    required this.isUnlocked,
    required this.isCompleted,
    this.unlockDate,
    this.completionDate,
    this.watchDurationSeconds,
    this.lastPositionSeconds,
    this.videoProvider = 'unknown',
    this.videoEmbedUrl,
    this.videoId,
    this.daysUntilUnlock,
    this.weeksUntilUnlock,
    this.isCurrentWeek = false,
    this.courseId,
  });

  factory CourseVideo.fromJson(Map<String, dynamic> json) {
    // Helper function to safely convert values to int
    int? toInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) {
        final parsed = int.tryParse(value);
        return parsed;
      }
      if (value is double) return value.toInt();
      if (value is num) return value.toInt();
      return null;
    }

    // Helper function to safely convert values to bool
    bool toBool(dynamic value) {
      if (value == null) return false;
      if (value is bool) return value;
      if (value is int) return value == 1;
      if (value is String) {
        final lowerValue = value.toLowerCase().trim();
        return lowerValue == 'true' || lowerValue == '1' || lowerValue == 'yes';
      }
      if (value is num) return value != 0;
      return false;
    }

    // Helper function to safely convert values to string
    String? toStringNullable(dynamic value) {
      if (value == null) return null;
      if (value is String) return value.isEmpty ? null : value;
      return value.toString();
    }

    // Calculate days until unlock if unlock_date is provided
    int? daysUntilUnlock;
    int? weeksUntilUnlock;
    bool isCurrentWeek = false;

    if (json['unlock_date'] != null && !toBool(json['is_unlocked'])) {
      try {
        final unlockDateStr = toStringNullable(json['unlock_date']);
        if (unlockDateStr != null && unlockDateStr.isNotEmpty) {
          final unlockDate = DateTime.parse(unlockDateStr);
          final now = DateTime.now();
          final difference = unlockDate.difference(now);
          daysUntilUnlock = difference.inDays;
          weeksUntilUnlock = daysUntilUnlock != null ? (daysUntilUnlock! / 7).ceil() : null;

          // If it's unlocking this week (0-6 days)
          if (daysUntilUnlock != null && daysUntilUnlock! >= 0 && daysUntilUnlock! < 7) {
            isCurrentWeek = true;
          }
        }
      } catch (e) {
        print('Error parsing unlock date: $e');
        // Set safe defaults
        daysUntilUnlock = null;
        weeksUntilUnlock = null;
      }
    }

    // If it's already unlocked, it's either current week or past week
    if (toBool(json['is_unlocked'])) {
      // Check if it was unlocked recently (within the last 7 days)
      if (json['unlock_date'] != null) {
        try {
          final unlockDateStr = toStringNullable(json['unlock_date']);
          if (unlockDateStr != null && unlockDateStr.isNotEmpty) {
            final unlockDate = DateTime.parse(unlockDateStr);
            final now = DateTime.now();
            final difference = now.difference(unlockDate);
            if (difference.inDays < 7) {
              isCurrentWeek = true;
            }
          }
        } catch (e) {
          print('Error parsing unlock date for unlocked video: $e');
        }
      }
    }

    return CourseVideo(
      id: toInt(json['id']) ?? 0,
      title: toStringNullable(json['title']) ?? 'Untitled Video',
      description: toStringNullable(json['description']) ?? '',
      videoUrl: toStringNullable(json['video_url']) ?? '',
      thumbnailUrl: toStringNullable(json['thumbnail_url']),
      durationMinutes: toInt(json['duration_minutes']),
      weekNumber: toInt(json['week_number']) ?? 1,
      sequenceNumber: toInt(json['sequence_number']) ?? 1,
      isUnlocked: toBool(json['is_unlocked']),
      isCompleted: toBool(json['is_completed']),
      unlockDate: toStringNullable(json['unlock_date']),
      completionDate: toStringNullable(json['completion_date']),
      watchDurationSeconds: toInt(json['watch_duration_seconds']),
      lastPositionSeconds: toInt(json['last_position_seconds']),
      // Vimeo-specific fields
      videoProvider: toStringNullable(json['video_provider']) ?? 'unknown',
      videoEmbedUrl: toStringNullable(json['video_embed_url']),
      videoId: toStringNullable(json['video_id']),
      // Unlock status fields
      daysUntilUnlock: json['days_until_unlock'] != null ? toInt(json['days_until_unlock']) : daysUntilUnlock,
      weeksUntilUnlock: json['weeks_until_unlock'] != null ? toInt(json['weeks_until_unlock']) : weeksUntilUnlock,
      isCurrentWeek: json['is_current_week'] != null ? toBool(json['is_current_week']) : isCurrentWeek,
      courseId: json['course_id'] != null ? toInt(json['course_id']) : null,
    );
  }

  String getFullThumbnailUrl(String baseUrl) {
    if (thumbnailUrl == null || thumbnailUrl!.isEmpty) return '';
    if (thumbnailUrl!.startsWith('http://') || thumbnailUrl!.startsWith('https://')) {
      return thumbnailUrl!;
    }
    // Remove leading slash if present
    final cleanPath = thumbnailUrl!.startsWith('/') ? thumbnailUrl!.substring(1) : thumbnailUrl!;
    return '$baseUrl/$cleanPath';
  }

  String getVimeoThumbnailUrl() {
    final vimeoId = VideoSecurityHelper.extractVimeoId(videoUrl);
    if (vimeoId != null) {
      // Use Vimeo's official CDN for reliable thumbnail loading
      return 'https://i.vimeocdn.com/video/${vimeoId}_640x360.jpg';
    }
    return '';
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'video_url': videoUrl,
      'thumbnail_url': thumbnailUrl,
      'duration_minutes': durationMinutes,
      'week_number': weekNumber,
      'sequence_number': sequenceNumber,
      'is_unlocked': isUnlocked,
      'is_completed': isCompleted,
      'unlock_date': unlockDate,
      'completion_date': completionDate,
      'watch_duration_seconds': watchDurationSeconds,
      'last_position_seconds': lastPositionSeconds,
      'video_provider': videoProvider,
      'video_embed_url': videoEmbedUrl,
      'video_id': videoId,
      'days_until_unlock': daysUntilUnlock,
      'weeks_until_unlock': weeksUntilUnlock,
      'is_current_week': isCurrentWeek,
      'course_id': courseId,
    };
  }
}
