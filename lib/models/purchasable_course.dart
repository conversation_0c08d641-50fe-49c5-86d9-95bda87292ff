class PurchasableCourse {
  final int id;
  final String title;
  final String category;
  final String description;
  final String? thumbnailUrl;
  final int durationWeeks;
  final CoursePrice price;
  final bool isFeatured;
  final int totalVideos;
  final bool isPurchased;
  final String imageUrl;
  final String? instructor;
  final String? level;
  final int? rating;
  final int? ratingCount;
  final String whatsappNumber;
  final String? whatsappMessagePrefix;
  final int? assignedStaffId;

  PurchasableCourse({
    required this.id,
    required this.title,
    required this.category,
    required this.description,
    this.thumbnailUrl,
    required this.durationWeeks,
    required this.price,
    required this.isFeatured,
    required this.totalVideos,
    required this.isPurchased,
    String? imageUrl,
    this.instructor,
    this.level,
    this.rating,
    this.ratingCount,
    String? whatsappNumber,
    this.whatsappMessagePrefix,
    this.assignedStaffId,
  }) :
    imageUrl = imageUrl ?? thumbnailUrl ?? '',
    whatsappNumber = whatsappNumber ?? '+918714151952'; // Default WhatsApp number

  factory PurchasableCourse.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse integers
    int parseIntSafely(dynamic value, int defaultValue) {
      if (value == null) return defaultValue;
      if (value is int) return value;
      if (value is String) {
        try {
          return int.parse(value);
        } catch (_) {
          return defaultValue;
        }
      }
      if (value is double) return value.toInt();
      if (value is num) return value.toInt();
      return defaultValue;
    }

    // Helper function to safely convert values to bool
    bool toBool(dynamic value) {
      if (value == null) return false;
      if (value is bool) return value;
      if (value is int) return value == 1;
      if (value is String) {
        final lowerValue = value.toLowerCase().trim();
        return lowerValue == 'true' || lowerValue == '1' || lowerValue == 'yes';
      }
      if (value is num) return value != 0;
      return false;
    }

    // Helper function to safely convert values to string
    String? toStringNullable(dynamic value) {
      if (value == null) return null;
      if (value is String) return value.isEmpty ? null : value;
      return value.toString();
    }

    return PurchasableCourse(
      id: parseIntSafely(json['id'], 0),
      title: toStringNullable(json['title']) ?? 'Untitled Course',
      category: toStringNullable(json['category']) ?? 'General',
      description: toStringNullable(json['description']) ?? '',
      thumbnailUrl: toStringNullable(json['thumbnail_url']),
      durationWeeks: parseIntSafely(json['duration_weeks'], 1),
      price: CoursePrice.fromJson(json['price'] ?? {}),
      isFeatured: toBool(json['is_featured']),
      totalVideos: parseIntSafely(json['total_videos'], 0),
      isPurchased: toBool(json['is_purchased']),
      imageUrl: toStringNullable(json['image_url']) ?? toStringNullable(json['thumbnail_url']) ?? '',
      instructor: toStringNullable(json['instructor']),
      level: toStringNullable(json['level']) ?? toStringNullable(json['difficulty']),
      rating: json['rating'] != null ? parseIntSafely(json['rating'], 0) : null,
      ratingCount: json['rating_count'] != null ? parseIntSafely(json['rating_count'], 0) : null,
      whatsappNumber: toStringNullable(json['whatsapp_number']),
      whatsappMessagePrefix: toStringNullable(json['whatsapp_message_prefix']),
      assignedStaffId: json['assigned_staff_id'] != null ? parseIntSafely(json['assigned_staff_id'], 0) : null,
    );
  }
}

class CoursePrice {
  final double original;
  final double final_;
  final int discountPercentage;
  final bool hasDiscount;

  CoursePrice({
    required this.original,
    required this.final_,
    required this.discountPercentage,
    required this.hasDiscount,
  });

  factory CoursePrice.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse doubles
    double parseDoubleSafely(dynamic value, double defaultValue) {
      if (value == null) return defaultValue;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) {
        try {
          return double.parse(value);
        } catch (_) {
          return defaultValue;
        }
      }
      if (value is num) return value.toDouble();
      return defaultValue;
    }

    // Helper function to safely parse integers
    int parseIntSafely(dynamic value, int defaultValue) {
      if (value == null) return defaultValue;
      if (value is int) return value;
      if (value is String) {
        try {
          return int.parse(value);
        } catch (_) {
          return defaultValue;
        }
      }
      if (value is double) return value.toInt();
      if (value is num) return value.toInt();
      return defaultValue;
    }

    // Helper function to safely convert values to bool
    bool toBool(dynamic value) {
      if (value == null) return false;
      if (value is bool) return value;
      if (value is int) return value == 1;
      if (value is String) {
        final lowerValue = value.toLowerCase().trim();
        return lowerValue == 'true' || lowerValue == '1' || lowerValue == 'yes';
      }
      if (value is num) return value != 0;
      return false;
    }

    return CoursePrice(
      original: parseDoubleSafely(json['original'], 0.0),
      final_: parseDoubleSafely(json['final'], 0.0),
      discountPercentage: parseIntSafely(json['discount_percentage'], 0),
      hasDiscount: toBool(json['has_discount']),
    );
  }
}
