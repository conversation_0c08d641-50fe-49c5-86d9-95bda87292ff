{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984e6eb2d65cd5850f39ed34c23d343854", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98337e68e95060885707993540ea6902e5", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98337e68e95060885707993540ea6902e5", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9837b468635f01e1f2857ce8025ab9abd3", "guid": "bfdfe7dc352907fc980b868725387e98fe92b5afa8fe58bae3c9229d058e616a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851f96d674d816301fbaf5f04e0902846", "guid": "bfdfe7dc352907fc980b868725387e985965519b3ec2b9053365fa530bf643bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896f6f53fba84d2290df8874e53ec2c37", "guid": "bfdfe7dc352907fc980b868725387e98fa9d97f54a76579d59e404ebe634bcdc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801946638119ae68b92c81aa59a9a3e13", "guid": "bfdfe7dc352907fc980b868725387e9850bdaa7011aa3c91740c213b1a7fa2cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfb79d24dd22f884aec2c8af8ca29410", "guid": "bfdfe7dc352907fc980b868725387e98b69297aee86961e47e29414c45e849ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bd552f55524c8d6506560e979f95c6b", "guid": "bfdfe7dc352907fc980b868725387e985ca3c30a7f4851eb9b4ef33952a3f482", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98549b07a3582dd84f5df506eb2980cc32", "guid": "bfdfe7dc352907fc980b868725387e9812df9d9973c9f97679f7de972b48f817", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb561f06f3c4e9790a16b3c24a4ab174", "guid": "bfdfe7dc352907fc980b868725387e98abf8d1a3dd0a4ead2fb6398cea2425ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98265a345396a9c0a6010bbf4e45f44eb6", "guid": "bfdfe7dc352907fc980b868725387e986340d5835ff3d4930a87aff596993c8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833c9a12000966eec23ea8ddf6dd0da93", "guid": "bfdfe7dc352907fc980b868725387e98282e717420c34bd36656734c015ab0db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c37202c384c2109050f4219045740be", "guid": "bfdfe7dc352907fc980b868725387e98f79617ce3eea92130c3e8deb24ac6052", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5e0bb97281d95847a50a486cb4e58c6", "guid": "bfdfe7dc352907fc980b868725387e989a9c98af54d171ffc5dcfcd328a9f656", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5a15717e56b454d6f78f3c0f6946f95", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98538009b9a4c43fa97ecaec9de7b2ad1b", "guid": "bfdfe7dc352907fc980b868725387e98892ab7216e572450585dbb7fe836780a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5d15dc08e46aa0c7f7a8b92665ee6c6", "guid": "bfdfe7dc352907fc980b868725387e983d12f44f6ad546c0e51ad40a38a62758", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0a06bf6e931bcc8fdf6820d65e4503e", "guid": "bfdfe7dc352907fc980b868725387e98a42139f6edf79b3b7cbcf3d1471e4431", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d478358e02521d4de627c99fc77fd5da", "guid": "bfdfe7dc352907fc980b868725387e98d954eaec4de1c4988534c19c3b497a42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f812caa9ef2516ba49a6693412c73a50", "guid": "bfdfe7dc352907fc980b868725387e98a1ebbf17f73356fde3a4c9c9ccaaf541", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d54e12db17f527ed47a1f88a88383560", "guid": "bfdfe7dc352907fc980b868725387e9821e54dc62d1296156df602834f20fcc9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cb512931044ff3a0d3dd6ee863a1727", "guid": "bfdfe7dc352907fc980b868725387e986061ae92e4bce152885cc5c3a1c0e4d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98882283b3869cdb3bf14589c5eb31107e", "guid": "bfdfe7dc352907fc980b868725387e98e7e268d654b1604ef998bf4cc75336d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98460eaf9e6e68fff54bddae9f05fa6109", "guid": "bfdfe7dc352907fc980b868725387e9854f174ef0b05263e6dcd47023ed0496c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98958e0bc62f773c739defbef7170168c8", "guid": "bfdfe7dc352907fc980b868725387e98a3f25623979afa23466422cd7a073ec5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fc74cb40252ae46be0dd90709c533da9", "guid": "bfdfe7dc352907fc980b868725387e98ffd488b77237b8fc440d41413454bb07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc312a803c0dab5ee80a4d17bbeb2b25", "guid": "bfdfe7dc352907fc980b868725387e98867e367d9e5822a1ed3da2e5d879f4ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891f470754158f95379fd73aedd90ecb3", "guid": "bfdfe7dc352907fc980b868725387e98f4e8406eff8c37b5cf57678723c2865d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98574b9e47516662df8e29e46a7265ab18", "guid": "bfdfe7dc352907fc980b868725387e986a9a5442fb214160e10f186b31f4351e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98803747c1e4e25adf5a48b4b84d53e239", "guid": "bfdfe7dc352907fc980b868725387e9856735a0df7a10f65de67a62396d1be8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985330dfcdb9a836d55db55e31c2e35f5c", "guid": "bfdfe7dc352907fc980b868725387e98d490619ce99b46bd5cde304795058c5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6da80cbe5060de355b91474c89ffecf", "guid": "bfdfe7dc352907fc980b868725387e985b6b972d31abdb37d83e0217390f1111"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f85dd202ccf975165a84b1f4d34c9fc", "guid": "bfdfe7dc352907fc980b868725387e9826e5537c184a98a817fabddd8b1af46b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9a398d0af796a65c0d003d005d1a05a", "guid": "bfdfe7dc352907fc980b868725387e98a6f139e04c42be401173e09b8eb64304"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c003086413a38701095ea6d94145e0e", "guid": "bfdfe7dc352907fc980b868725387e98bba9b3f581dbe27be7f77e7dba39135c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98768151613b7933990087cbb83f64baeb", "guid": "bfdfe7dc352907fc980b868725387e98f5a18a02b63f8074b1935286ce097e7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835ca0c3159fd03dd10b25fda3b11666a", "guid": "bfdfe7dc352907fc980b868725387e982a4d902c47d2e168ad1b9faadb826b40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981883ad8eb6bbb50bbff91b7c1655e86d", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebf05decf76ce057c1152ba80d2b3393", "guid": "bfdfe7dc352907fc980b868725387e98c7209acb4bbc11473400b95ba264e570"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b978ef9130153fe2de1e6e956e5a3a28", "guid": "bfdfe7dc352907fc980b868725387e98b9ad21760386409e6a8418d260c875a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae5478ee42ab8565a58aef14eedc17f9", "guid": "bfdfe7dc352907fc980b868725387e9869daee10844bdc1377101bb06b07af6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b1ec8bc4220306e38ff01076dd2e945", "guid": "bfdfe7dc352907fc980b868725387e9880b83d2e94cb821fc34990ec5eb08f30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0c33cd5e95e833d27ec9175e0d3ebe4", "guid": "bfdfe7dc352907fc980b868725387e983fc554d229dec51832d227dc3c6cd4f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853ca434693cc73324a19e6ad0db48fef", "guid": "bfdfe7dc352907fc980b868725387e984d92ce2a6a775539ad9cd12b7b3c8702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98102965ca29342ba11d1d81af35552b05", "guid": "bfdfe7dc352907fc980b868725387e98b55b1eb31da0207c73caefaa35380afd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d383301216c918aa9f7a599c34c1893", "guid": "bfdfe7dc352907fc980b868725387e982a5ad3c71031968c83cf1f0dd2275285"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98097c8f22e817a37a81e327a63cc8f820", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}