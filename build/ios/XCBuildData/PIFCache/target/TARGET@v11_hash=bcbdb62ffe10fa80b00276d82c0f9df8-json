{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986600ad781e179dc69599d8c73d2e4846", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f926f1067797d2349afce6cd66139956", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9816fa172b1c5d7e87a4a47b0e315f21dd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9887db1e02e1449215e4082aaa6da01416", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9816fa172b1c5d7e87a4a47b0e315f21dd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e310260ec7fe338ba849ed55a642eba0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e88397ba90e672fea18c7b767e8f74b4", "guid": "bfdfe7dc352907fc980b868725387e98e7116b84d1e5250f4e37303eddd6c8eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e3bce094639ca8bcc0e05feeea51571", "guid": "bfdfe7dc352907fc980b868725387e9886cd20f553bb0770a28c6b8e5137b56a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98290e23479cb76e8b971cb8ebf74a9068", "guid": "bfdfe7dc352907fc980b868725387e98ae60389fec929e45dccdb1823bcdf0c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b0e8a12192e68aaaf311761941a44be", "guid": "bfdfe7dc352907fc980b868725387e980a0ecc3581cce9e4f6623ff19d673b4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98730d75c63f310b4e0ad04fae1e3015c1", "guid": "bfdfe7dc352907fc980b868725387e98aa941e7bd48fc0abfed4b61f1da822d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0712e300bb0db67525f6137735ffe49", "guid": "bfdfe7dc352907fc980b868725387e9809deef069d89137f24505bc1fbe2fdf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f4f792d90045beca3e0993d1b92c7fb", "guid": "bfdfe7dc352907fc980b868725387e98a33e60187ae14d4fe3b2586ccc3f6a51", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef58bf76521bac25da361c1240864538", "guid": "bfdfe7dc352907fc980b868725387e986ca883610482a9db3fbc3a22aafc308f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988513c4e977c607dc1d94c55721efbfd6", "guid": "bfdfe7dc352907fc980b868725387e988a38f0205199405084e730dbb7c806ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ebb7c2e5f226452f72a529e9618905b", "guid": "bfdfe7dc352907fc980b868725387e985c8be2db9591370cac2ab92edac706b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828c8bf2b1f0bd1b1fa490afa5d263920", "guid": "bfdfe7dc352907fc980b868725387e98856ba4ab471588199737d7264bbd97f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3abd9384c2efb7ce8ac527e230f5f3f", "guid": "bfdfe7dc352907fc980b868725387e98c3b7cd9b09f64df218143c8f73e3305c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862d3dd0d6eeaad9b6872d4c5225d4e4d", "guid": "bfdfe7dc352907fc980b868725387e98fb459b92a416fd34a1e557e275912db5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98510bafa4c7285c236876e7132569668b", "guid": "bfdfe7dc352907fc980b868725387e98b899743e09c258fc6d2beee4eef6fade"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c46f72460b771beeeb2639b8d61c094", "guid": "bfdfe7dc352907fc980b868725387e98e3420e114f422bc145d3e7b455f69566"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c61e53a968735e27a8f4c78c1577c2b3", "guid": "bfdfe7dc352907fc980b868725387e982103f4e78a2f9aa133bd52a1b914f49c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8990f12570b4523ecfe7318a6aa45fb", "guid": "bfdfe7dc352907fc980b868725387e98a1a286f892e58a062c4042ae345d3e0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a326a3b0d88914fd473f1559f839538e", "guid": "bfdfe7dc352907fc980b868725387e980357f06080b3f5400c5f07447d18a7f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8bc0f8e2c95db3182db1d4027803c50", "guid": "bfdfe7dc352907fc980b868725387e98ac26c077187577c3845ea764598e5857"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98929adf65d78397e1fbf60373bf49ee6e", "guid": "bfdfe7dc352907fc980b868725387e98aae3e7b16a1cb1929a495a78bbfaf5c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db1d7c5565aedaed0a44cc757dd6b69a", "guid": "bfdfe7dc352907fc980b868725387e9865d94ba681648b50999050554e90bf01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98045cf8f64125e203beba955e9869fad0", "guid": "bfdfe7dc352907fc980b868725387e98430ab7afb1b77e64630c46500ec14786"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0570bc9c6201f1fe17bc3c4cad04521", "guid": "bfdfe7dc352907fc980b868725387e9839d5bf52164568bc32869ba8ca5e5886"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890ff4e6efb20b4a1c62532649a53c62c", "guid": "bfdfe7dc352907fc980b868725387e98ecdc13f1d42b0f5cfb67ed67a0c1325e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868b0f3f0bb3ddbf08f47032dfded76b7", "guid": "bfdfe7dc352907fc980b868725387e989f0709de75282200cf931c022007f60f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835aec8a4e264908d3b0dd8e678d06256", "guid": "bfdfe7dc352907fc980b868725387e98eb598bb37502559479e26ae8dc02ffdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ce349f110f96e95a4f616db821a1b77", "guid": "bfdfe7dc352907fc980b868725387e982b69da8c3a7e318820b8cf2fc67ccd5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806f4e100a737f48cb54e4db23e464d20", "guid": "bfdfe7dc352907fc980b868725387e98fc307774fa5f1c7da33f01634def711c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862888d00782bf80575339a278ab33b0b", "guid": "bfdfe7dc352907fc980b868725387e98be8d87873bdcc48042d8e8490dad772f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98192b5143469381c9b157b39b12e47839", "guid": "bfdfe7dc352907fc980b868725387e98f737a1bad0c2fbdd6a69c07dd548bdc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98405baab57ce244cf52e626e7c28e43d2", "guid": "bfdfe7dc352907fc980b868725387e98caa42c844050e81f47ee4134b51fe1e6"}], "guid": "bfdfe7dc352907fc980b868725387e98ae3586dc705715af805682a7d99a8a2c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ba7bbe20a593b9e477e86ceb82bc93a0", "guid": "bfdfe7dc352907fc980b868725387e9829cd08b5eddb17e2198e210109f7197b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aad756adfd6bcca4b5f15885877bfd6e", "guid": "bfdfe7dc352907fc980b868725387e9814b803080362d05addeb0d358745e9f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9f75e3d6f252c91ad71495ca5c00b46", "guid": "bfdfe7dc352907fc980b868725387e98aeae529d4b329d52fc1fa90eb2c0b492"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98076eb70a0dc87c048dcfbcbb967d8ec0", "guid": "bfdfe7dc352907fc980b868725387e987518a644ca9ab5881767a6bde6c1ba1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984272d8434522c7c33a59e43fc77b9e3d", "guid": "bfdfe7dc352907fc980b868725387e981e74202eed13dc0234508215f0ee8a31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acbb8e2daaf578545979b74573694abb", "guid": "bfdfe7dc352907fc980b868725387e9849ab04e45e936c1e310ebdd1bf42971d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c430cf95b988a055c2c83f9ca1cd4bd", "guid": "bfdfe7dc352907fc980b868725387e9848b804861750a7d698fe6c82018a609c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b6b31d806d0e7c3a8b5faee4c55970c", "guid": "bfdfe7dc352907fc980b868725387e981aff2d3d57b9d049e1aad17393d5c4a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a42df50b991dce184e4da344ad0b101", "guid": "bfdfe7dc352907fc980b868725387e9896948759f7bfbe0032e208fd610e0bb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b5ebcc2eac8562c051edcfb5008888e", "guid": "bfdfe7dc352907fc980b868725387e98b20b38013cb94e469c93f425344375f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985339f10be864ee9494cee4b299a22978", "guid": "bfdfe7dc352907fc980b868725387e98fb4f49cbcbdc5ca951f2cd0c84f0ac4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb2d9c116b1830a7a1e4c1f91b6f4af0", "guid": "bfdfe7dc352907fc980b868725387e9824652f18d3cf4ee4215d2c367007a31d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7002cff7f0fb0ee11b053930c72a7f2", "guid": "bfdfe7dc352907fc980b868725387e98242cf1320f215b8998b981b55bebfd61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8fe182823d46aea77c5c70065ff6c82", "guid": "bfdfe7dc352907fc980b868725387e98a6b1d2bd3f1c130609514d5a06802ab5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdcc7997cd9dcb2df1fcf33c8e6c2adc", "guid": "bfdfe7dc352907fc980b868725387e98b12e9164dc2a25721887b20c5e943b5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877a2358311d76d2cb8068dbb273c1d4f", "guid": "bfdfe7dc352907fc980b868725387e98b484694e1325c041745e6d73fffc8b92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983638b06657b772c2160db6d74f9462e8", "guid": "bfdfe7dc352907fc980b868725387e9815c89c82822c5fabe04e629adfe60660"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879ef6c12f7cd0dfb25354c30a403d7af", "guid": "bfdfe7dc352907fc980b868725387e98739366c00c43adeb4548ace910f79d6c"}], "guid": "bfdfe7dc352907fc980b868725387e98aa45e3f8bc88808aae274951c4bd4d9e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98097c8f22e817a37a81e327a63cc8f820", "guid": "bfdfe7dc352907fc980b868725387e98bb44a9dbe5fb418fdcabe8ef0a77c5ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa7dd7704aab0133f40d8e24a0aca088", "guid": "bfdfe7dc352907fc980b868725387e98471db22831a1c6683a38eb781dc54ea6"}], "guid": "bfdfe7dc352907fc980b868725387e9810dcd5b31e5cda901ce12e56bee12ca9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981ecf1e726ce59e784a4f037fe0c0ddf8", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e984a8de32adf5e9180a67c4a38d89039c4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}