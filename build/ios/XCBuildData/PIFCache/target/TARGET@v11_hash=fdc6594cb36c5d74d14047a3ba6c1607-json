{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98295ed3883f17c7850e063535063f461e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988c8f5017a3bd70324cc899aac19bb837", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9862fad0d3589097fba33b16e2a7143a4b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9873a95698ebee0cd3f8e802a984082fe0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9862fad0d3589097fba33b16e2a7143a4b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b0f97a0ca68522dd1633a1467da735f9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98768052920db32d55081659a4eaba555a", "guid": "bfdfe7dc352907fc980b868725387e98ee618ddbaa3a677e68bc59c9a94c8424"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830f35c6d3902e4e7447ec99d18099ae9", "guid": "bfdfe7dc352907fc980b868725387e98b08a11cc6fe5df771993af6bb8c5aa9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983463953fd490ba4b46761adffdc1831e", "guid": "bfdfe7dc352907fc980b868725387e98b9d48c49358b20bfec557de983c0689f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a540a7a2cec62cb5b8bc902b7d87d3b0", "guid": "bfdfe7dc352907fc980b868725387e9888e09e6f5e884e30cb2a289dbf96ea04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837346e517d6eccc30773e379b9b0de27", "guid": "bfdfe7dc352907fc980b868725387e980b44e4e752786c595ea4d57c9151369b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e584dba3d4e30fe9728c2726e8a0e17", "guid": "bfdfe7dc352907fc980b868725387e980f5e3653d591dbf45430abd722db1d97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b110824edc3327eafbeda83daa3e05e", "guid": "bfdfe7dc352907fc980b868725387e98810827d9c315c341804c1f71a0fe86da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98685283ccaadb7b90a63d80b20d0b8a29", "guid": "bfdfe7dc352907fc980b868725387e989e570a8b2b675a5d8bc17ab354602419"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844c8af236d16e8f00068a55b46a1e361", "guid": "bfdfe7dc352907fc980b868725387e98d32a59dc9682189a3d46b9f8f90f3ef1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fc4454728985a48b97faa1575d9abe4", "guid": "bfdfe7dc352907fc980b868725387e98cfb5dbac6299b87f07b91a18e3670351"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98813248e50451f398ca656108b11b7c08", "guid": "bfdfe7dc352907fc980b868725387e98d240bba3d720338f2d00b0c451a8c0be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1738aa83fc5349bde2d1e3aac2e031f", "guid": "bfdfe7dc352907fc980b868725387e98e89dff95ecb4ab3d91d836b8f3b70e51", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988269cb312817d95c179febfac9019dc5", "guid": "bfdfe7dc352907fc980b868725387e98791b3cf0fe55bb4d869fa49e9e0d9146"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d08c083f1897bbd066e6a2548bb3e576", "guid": "bfdfe7dc352907fc980b868725387e98d87d679924048bcc99dd73a863ab8351"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885697370ace7b64eb72c0c6b54e5fd8f", "guid": "bfdfe7dc352907fc980b868725387e9875a5f88880e673fd6d78ee75ee3d3db8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836d4b456de12e9ecf5c939138dc92d04", "guid": "bfdfe7dc352907fc980b868725387e981d20e84cb36893dad795358dd8ca1b7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981664a640077f142f29c56787d72f4f70", "guid": "bfdfe7dc352907fc980b868725387e989a6d7c5ec76747fd2773e303df127d7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98410bab089c5440ef4f29fe306549fa97", "guid": "bfdfe7dc352907fc980b868725387e98ffedff254129cac736ec4c1a61937c69", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1c3a1e367d6bc11d41bb4684a223642", "guid": "bfdfe7dc352907fc980b868725387e9844fe9231ebfea620d40fd2d70d9b456a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855bba19dc729e3565465e3cd8e2795ab", "guid": "bfdfe7dc352907fc980b868725387e9803406638a8ac3d80a02d198567bca47b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b32246e9fbf9a9a32b3289a2e7405e14", "guid": "bfdfe7dc352907fc980b868725387e9880cd3e4fb1c37bfd5dce79319de4699c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d25a850244b13e2d4498eab36463c093", "guid": "bfdfe7dc352907fc980b868725387e98d2c83748fb3ea28eb4967637ef5f3590"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989258abadc8d024aea9b1014930f2bf9c", "guid": "bfdfe7dc352907fc980b868725387e98ecaaad6b8c5e5576f56789e4dfb32572", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98da97fd3b3ff367b48e31d2de5d458f36", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f3af842a1d2172d8182eb18f571616e7", "guid": "bfdfe7dc352907fc980b868725387e98348f3ad51fd5c6629bf276e2edc7fadb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98780df2a545748ade93b5cfb9ef6b63ad", "guid": "bfdfe7dc352907fc980b868725387e985afed5ae36980f7593680ef493d5eedb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9e598f49385c599fa80bd10a811fdde", "guid": "bfdfe7dc352907fc980b868725387e9837b4810fb6bd53a33d2bce00931cd29a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831b315e87fc18d4beddc480b07c44080", "guid": "bfdfe7dc352907fc980b868725387e98f98ede3330bf8148afeb0f3bdae47d49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98481849d9e57e757f5cf510847db44eec", "guid": "bfdfe7dc352907fc980b868725387e9836cb06b416e8354444b5346511fbc388"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878610f1bf7532b7bef51bed15b5a59f9", "guid": "bfdfe7dc352907fc980b868725387e98e42dddef1c9f8d3d2d36d364d0267e1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7d1bb70362cb2ed7101b78dbaecdee1", "guid": "bfdfe7dc352907fc980b868725387e98f7ec3921adcf3b48f191be5c984ee31e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6936b10e4f05ce790f18c8308a247bc", "guid": "bfdfe7dc352907fc980b868725387e985f0e8e1d7870dc6994893e7f6bae4213"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824239319a06c6160814b84a10a8a658a", "guid": "bfdfe7dc352907fc980b868725387e98a99d8b465d49021e3fb2edd0fa0c96df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fa32949cbec98b4b5ba5f8d3f354f2b", "guid": "bfdfe7dc352907fc980b868725387e9843b7432bfa396b6d88c39196a96781b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fa6a4cb5858470884f6e103d581ef88", "guid": "bfdfe7dc352907fc980b868725387e9809393afa85eb84cb9c2351b3b954a9c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec34f0294e1ab61fe1d1f6cc8843a09a", "guid": "bfdfe7dc352907fc980b868725387e98bb15aed8e23a426228f86550f1028e32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcd0bd940830e8ac11f679fb8353a21f", "guid": "bfdfe7dc352907fc980b868725387e985dcf453e47d0961f223594357582555b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d123f499acc707de16c9847903db017", "guid": "bfdfe7dc352907fc980b868725387e9810ca0f324f5007b5ecfe26eed0623632"}], "guid": "bfdfe7dc352907fc980b868725387e98b6fae2d9e73acfb8fa055e1675c67ec9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98097c8f22e817a37a81e327a63cc8f820", "guid": "bfdfe7dc352907fc980b868725387e9842ec2888bf9bae2b0f4ef100ab3486be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0f90f43c6c3690341b9daa0c1add153", "guid": "bfdfe7dc352907fc980b868725387e980a27244e8e10ac3f54908c42648e72d7"}], "guid": "bfdfe7dc352907fc980b868725387e987928e5e1bcda1a946f6d1976102109dd", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98382d0c0cd695f34ed026f5f92d44cd9b", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e984834975c34f03785d4f393641d617144", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}