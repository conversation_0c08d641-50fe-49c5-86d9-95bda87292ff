{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980e71fa2ffabab09b424638d06398e0de", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c5296c2751a1328739e82f135c00b453", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982059a66e3c789313fc86d39a6f622725", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f3ff5b4456be59803a5b9dabcc271210", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982059a66e3c789313fc86d39a6f622725", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ad8274c4b3c3c7722b3e279057c2fd8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983759d0bf3c166d181149f4a60a47bfe9", "guid": "bfdfe7dc352907fc980b868725387e9822b9114c2b65401bf25741fa5960a332", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f650a6ccd7976f42e5bd06730c784e1", "guid": "bfdfe7dc352907fc980b868725387e98934843028e8547e3a283d0242b472281", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebcf889fdbe9e6820ec24c55a81413b0", "guid": "bfdfe7dc352907fc980b868725387e98bd6f2ca7585ed168f37e1136a1868899", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989918bae3a40f12df789a500d8abf5288", "guid": "bfdfe7dc352907fc980b868725387e982edecba4782d0ed0695d0607a52919d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98730506caf6cd394e775b59afddf9ba78", "guid": "bfdfe7dc352907fc980b868725387e98e8bec1ae8659129cb20506e2b6a668e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f50958a0d4caeaf4a26fcbbd38cde200", "guid": "bfdfe7dc352907fc980b868725387e98a9a744e010dbbc5275c9fe2774867d9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a41d6ed1a2cb1b42cdce7a73c6d70cef", "guid": "bfdfe7dc352907fc980b868725387e98f0009311d6d1823a3fab3dc6ec0083b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d96781a3d3641dd329ca50db8f17799e", "guid": "bfdfe7dc352907fc980b868725387e98a7fe9db2377cd3da49e0259a8f354e69", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d04543e52aeb09244fb23a961d69ea1", "guid": "bfdfe7dc352907fc980b868725387e98629e742d86b83b4fb6f275f62bcf97e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d95945d381a83f050dcd76f78f98e179", "guid": "bfdfe7dc352907fc980b868725387e98b6d10ec8add7ce29db8b1a16eaae6283", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d141ce2dee06fca565b5a07371943cf", "guid": "bfdfe7dc352907fc980b868725387e98fa9a4e91da3fa746c24e7e5e7afdb671", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98362d60ae59724e6b9fca97f86c396c13", "guid": "bfdfe7dc352907fc980b868725387e982cf1f911127172aec2e53d836a9e7b74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec5605c047231283b913ca0b889524c1", "guid": "bfdfe7dc352907fc980b868725387e98acf2c41005c23bc994e2c9b74a73c252", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988db97a96aa2f2c46d6c2d5537c55911a", "guid": "bfdfe7dc352907fc980b868725387e9802a02f04b99c5d3dafee847ce314081f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808e99f4a8b7d5b65c9f7005201b07de6", "guid": "bfdfe7dc352907fc980b868725387e983536bb1b05357848b239bfcc75739c1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee82396a1061726b253dffda4088f8e6", "guid": "bfdfe7dc352907fc980b868725387e9887f646b13c36039e7ae859a3e54829ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983935398c961cc90a3be20320147f733d", "guid": "bfdfe7dc352907fc980b868725387e98282f998dbaf80c0590c04277b19f2bfb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f595840a1d787781215bef6f1bfcc07", "guid": "bfdfe7dc352907fc980b868725387e986c48c842990741d20934207afb984fe9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897ba7c194997160021d6c7750a068868", "guid": "bfdfe7dc352907fc980b868725387e982c11845cdb05c5132e7a7d1d5d20b72a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dac98587dd31bf6b260fee80652e087e", "guid": "bfdfe7dc352907fc980b868725387e98ba90deda406e62fd0695986de1af0b3a", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c405484abd1183ec4cd71373fb8c4cf", "guid": "bfdfe7dc352907fc980b868725387e982ee465315a9abf5e516865bed824d9da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ef00e5f9d5088139232b1204b49f4a3", "guid": "bfdfe7dc352907fc980b868725387e981962f721a7e8cd6cc9d4aa67bff82790", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988790b705840dbb8f44b5ce9932f96c35", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98434773837c1c6dfd74990de9407f6774", "guid": "bfdfe7dc352907fc980b868725387e98787f741db700989e0c0fdc22933f26a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863c764b7a823360897b48114916e3c02", "guid": "bfdfe7dc352907fc980b868725387e98fbf1c3a1c5f045042e0c2574127687fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c65e7560b5283809ed9d5506d9363126", "guid": "bfdfe7dc352907fc980b868725387e98b61e83352c08a60c8ae30184f2bb64ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98542b29a20ea668d89df7e9a640b9cf36", "guid": "bfdfe7dc352907fc980b868725387e98180e050091b80e54faf17eacfa87f63a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846d171553e80bbcf738bd775b1c31142", "guid": "bfdfe7dc352907fc980b868725387e980e9d9afbaf9d5e41c4d6ce9e7197eef7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3f57860e2130c8f48980cdbc771001f", "guid": "bfdfe7dc352907fc980b868725387e98aff65af197271ef866b158d74d2accfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e01cbb504775b03ffe16e4dd049e297", "guid": "bfdfe7dc352907fc980b868725387e987d671373e03266f377134180abfc5539"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a3196ced70a2db947fc03f92531877b", "guid": "bfdfe7dc352907fc980b868725387e98bc5f8c4df92f6d9cea689ea87a760806"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4f4aabbba3c62b5c8097b5a9a72021a", "guid": "bfdfe7dc352907fc980b868725387e98f3a9dafeac8cc7b8d02bc82f63c7f760"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdf5277b44aeabf06eaa364efcd4747a", "guid": "bfdfe7dc352907fc980b868725387e987a7f6cdd63f312b521e40a6f52370b0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce7cfde153939b1aab526f0cf2c40e74", "guid": "bfdfe7dc352907fc980b868725387e98e016dcdb42f72f270553fecf4278ffe2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c264510554a010ca0776341374aad38d", "guid": "bfdfe7dc352907fc980b868725387e9810f7198a7febd4a468c5b3da153c781a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b73b11ff449f1e730c05cc2d3716645", "guid": "bfdfe7dc352907fc980b868725387e983a919c8e1da8dfddc70f18ab24de0d88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e77072fc614fdc90948b0fae8a7ed3ed", "guid": "bfdfe7dc352907fc980b868725387e986b672f17787973b38d01212934579a2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d00f59c83408168752618b35b8ff347", "guid": "bfdfe7dc352907fc980b868725387e98e97df4590d5ab9a62f294d6790ac0574"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc158acc87006a2202c1460cec3d2bfb", "guid": "bfdfe7dc352907fc980b868725387e983e3dcd305b6d9ce7d7ec3ef49dcf59c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed3e1373ee7888a74276676171af290a", "guid": "bfdfe7dc352907fc980b868725387e98bcf3fbda27c0a267f20d11854da97750"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837d108ad9b1496ec2f2d3ea0d416e6ef", "guid": "bfdfe7dc352907fc980b868725387e98c96e105d17cc1a290b64715ab07e51aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da699055d65932ab2f19e9889d040f51", "guid": "bfdfe7dc352907fc980b868725387e98ad720b59bcca675b3f6b0b4e89e00f98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817126b9139537d49b8ca5899e2c644b4", "guid": "bfdfe7dc352907fc980b868725387e98d86c3c15fe01545e435f340320a1f7ce"}], "guid": "bfdfe7dc352907fc980b868725387e988ebdcbb6b80922f1e83789f3721b6005", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98097c8f22e817a37a81e327a63cc8f820", "guid": "bfdfe7dc352907fc980b868725387e98e57834f2f63a149b3f3cd2c98e0da310"}], "guid": "bfdfe7dc352907fc980b868725387e980e834e0fb91ec24c9501b7f04041fad3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987c8e8ab4a6f18ef3c5b2c081e47edcf7", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e987ccd2a55c13ff43491f16ee444184781", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}