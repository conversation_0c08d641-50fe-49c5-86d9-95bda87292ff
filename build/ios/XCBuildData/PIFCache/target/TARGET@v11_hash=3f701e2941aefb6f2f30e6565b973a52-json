{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98254732619240332c60783b2b81116ede", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OBJC_BRIDGING_HEADER": "", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d6da72224a0002e2c8d4ad6a4140a101", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c14bac6438adbaea7c4663630ec1bb1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OBJC_BRIDGING_HEADER": "", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984cab3693a69eb1c553c791758183685f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c14bac6438adbaea7c4663630ec1bb1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OBJC_BRIDGING_HEADER": "", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f480631d57f584ae82569893436b3d19", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e126395869ada82f9b6fdc90dd9ffa55", "guid": "bfdfe7dc352907fc980b868725387e98faa2d77c0ef7eb87f7f8421e51c1b40a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a1e3dac75ef5081293a3cce12f493338", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982b2e0a3c8fdb65210fc8e2af8569f3a9", "guid": "bfdfe7dc352907fc980b868725387e98fa3e3f0bc11484509bf86e3d64bd157a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c727bf262c50955b377848ccee0a8bf", "guid": "bfdfe7dc352907fc980b868725387e9864c8953fa94e569827e0c657001ce70a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a277ffef781010b7c5aeaff327d8200", "guid": "bfdfe7dc352907fc980b868725387e9859a84cf3bffd3871a87345f41608c313"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3288fc961ad5e63bcda9aab4a7782ea", "guid": "bfdfe7dc352907fc980b868725387e98792a83978ba12cc47c981203ceb0a6a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98728b737b9cdf6c5084556ea663177d9e", "guid": "bfdfe7dc352907fc980b868725387e98e3c05ef736d87c4ecfde73d2c69a09f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864ed359366887a6c6cd70d736dbe1084", "guid": "bfdfe7dc352907fc980b868725387e98c10f8ad0991db25cc36797815b22e879"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faf7b963b960d986d5973dda73ebd744", "guid": "bfdfe7dc352907fc980b868725387e98fcd5b2a5b5d74bc16671776b56c1bad3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d59ac4372d91846a3d6ed45990e839a", "guid": "bfdfe7dc352907fc980b868725387e98161cebf82ef2cee41561b189b81457fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817e6041bb25f5c247e2e180b5f00c16a", "guid": "bfdfe7dc352907fc980b868725387e98c6084da3d95092052f6052b4f30910b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980718b6cb15af14a250e4236d8af154f0", "guid": "bfdfe7dc352907fc980b868725387e987efe2b04a5728dac6bfe88fa9c080326"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae09f5d2733c3db9ef4f18076a03edda", "guid": "bfdfe7dc352907fc980b868725387e98976038e4c84d06f0e4aa9b6d4707def3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d654a913570442e1d3cd88d014e5b23", "guid": "bfdfe7dc352907fc980b868725387e9832c48f37803ddf2a992dca32d23f3b09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3fbcfc259877c60ad8981fb97d30ff2", "guid": "bfdfe7dc352907fc980b868725387e9813d30f72b4787879a62ed8b98594f6f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864314d70aceeb60d589c806fa0004f2f", "guid": "bfdfe7dc352907fc980b868725387e98d452709e9698d3cd41f9725c5b6e7c2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983edfd297972c164bea82ff42a8181c88", "guid": "bfdfe7dc352907fc980b868725387e9842b079d5c587e71174380ff58ebd66e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98675a6081c619a151c69e1d156c58ff90", "guid": "bfdfe7dc352907fc980b868725387e98de127be58b3d579fbc556dc154d7c431"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832805745ed23bdd85895fc55fbcb2448", "guid": "bfdfe7dc352907fc980b868725387e9874dcf20b567789aff666819d3294e2e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e142095035b2481e5228d06705203573", "guid": "bfdfe7dc352907fc980b868725387e98cf77ec6605af85b4d6736fcaed9c378c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d1098c6ad578a86137843a47287aaf2", "guid": "bfdfe7dc352907fc980b868725387e981735885e06877f15b983026304b4c223"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885644739b8210a244784d42881a9a3e0", "guid": "bfdfe7dc352907fc980b868725387e9899751671a043fc9bf2598917f035eac2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e0558bc10425b7600162c8cc400875d", "guid": "bfdfe7dc352907fc980b868725387e98939a608b2817e7e0a24e8f8b7e42764b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8ca8fe75f3322ae09039214a7e66054", "guid": "bfdfe7dc352907fc980b868725387e988b4c0e1b73013da3777b7d3f73517d90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce2d1b6d5c31a1fa23b9731faf68734d", "guid": "bfdfe7dc352907fc980b868725387e98575fa82d6f345c4fafa668b9585aa5da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b016cef2e1419ffbdd80a07a14a41e1d", "guid": "bfdfe7dc352907fc980b868725387e98a0e0172d9e297206367817d37a477649"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd67a7d177c39c76ea8d293b9c0418f1", "guid": "bfdfe7dc352907fc980b868725387e98f18aa597d0aefe31e160c1199693e392"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a816efde393b90f50b0449f5e2c3315d", "guid": "bfdfe7dc352907fc980b868725387e98981c2c0161408add3b761cca3d644e1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825c3c340385cf050093d931908896e43", "guid": "bfdfe7dc352907fc980b868725387e98ae3a1978e9194f232c5e1a2dc6fae9f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c68995ac633da8125fbd67b7958cd6da", "guid": "bfdfe7dc352907fc980b868725387e986f97b912b680c0434c19b6e373208345"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dcf9968fe7d541f847dd2cb1ff47651", "guid": "bfdfe7dc352907fc980b868725387e98468845043fc5833491c9332b4d68b8fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98059aa40823cad503b3003515d0f7318b", "guid": "bfdfe7dc352907fc980b868725387e98cf171f881233b5242074709693683913"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7312ab7772e96bedd60e90595d04754", "guid": "bfdfe7dc352907fc980b868725387e9839dfd4acc232f395c8fb617d576dfe4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c48b36d950c69051a6499cfccee4e30", "guid": "bfdfe7dc352907fc980b868725387e98641f05bd371a222692dc2c654038ca85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f670c2b5f5aaee15c10f57507149ca2f", "guid": "bfdfe7dc352907fc980b868725387e98cd212e177b9c42a1a6233cec7c2cc5f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986db2bd43966bd6336d5171199954a890", "guid": "bfdfe7dc352907fc980b868725387e98254ff070edfbe0041ecefa3e49d71831"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb0f4de6b5a2b2494d14e400da89fa2d", "guid": "bfdfe7dc352907fc980b868725387e98f24ba2c13805a88817c4a81ef046368f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98608d1d69e478d9b5aea667d0f6d4ffe1", "guid": "bfdfe7dc352907fc980b868725387e9858e39c751c0f377fad78c329515a7244"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98430c582dde2484c3b143ef2b5ff9dffa", "guid": "bfdfe7dc352907fc980b868725387e98eb9874e5f4175bd9dee2bde42465d6a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f63177308fc908a46698037529d763d6", "guid": "bfdfe7dc352907fc980b868725387e988b6340e5bfe7cc86f95f98a9031566d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d2fde9e750e5598d25d28adfc39703f", "guid": "bfdfe7dc352907fc980b868725387e98d259083229b4eed28de4a364a94222ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d6f04a5694231646e0d0e6dc1ef1e57", "guid": "bfdfe7dc352907fc980b868725387e98abdbdc46c4777a258e87db72ae55c2e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ee51503526794d503fe7f0be8e4df8c", "guid": "bfdfe7dc352907fc980b868725387e98ff93ab63383edd2228232f5abc7a7ace"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984356b6fbc2d8c39bc627300316681735", "guid": "bfdfe7dc352907fc980b868725387e98f3c9d9ba8b83859b2f08d288e052253f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c87cdc2cbf457ea7c23b50a437438b19", "guid": "bfdfe7dc352907fc980b868725387e98369441872a80d2673fc8a519160a5750"}], "guid": "bfdfe7dc352907fc980b868725387e980055fbf3a11bc15129d28ebbe5cc1480", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98097c8f22e817a37a81e327a63cc8f820", "guid": "bfdfe7dc352907fc980b868725387e987ead9e15fc09a570554fa963eed1137d"}], "guid": "bfdfe7dc352907fc980b868725387e98da9cd099e7190758cfc42156c0f4e548", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b8defae1b39d33d1c1b6a7ec3e8cf51c", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e987fd8694c1d36b88c99f78feb0d04dd25", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}