{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988858b5bf1a0d9770c9ea8dd6974d8b80", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98954e782024ad96bb587d764ae46d26e8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a84ffe2a9b5125659627c94278954d24", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9815520d963eafbd9f537f472a8e8c6519", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a84ffe2a9b5125659627c94278954d24", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982ed9a735eb9e3e74d514ca0c671ebabf", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9862ee718a9417c0585156e4d07bfd789a", "guid": "bfdfe7dc352907fc980b868725387e988f8f87ea1562bc6d73393583e0a3d340", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983fb0a37b1cc0986741bb2f74fe8b5adb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9856c46d639f07b40455347dbc30931a5f", "guid": "bfdfe7dc352907fc980b868725387e984c12593089218394cefaab6677ed1cb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cecce13c22a25c74a25bdfc2c757a028", "guid": "bfdfe7dc352907fc980b868725387e983918fc0a9f4a1c8c4c99b728969a9fc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98053f6f0fdb5bd4e9014b24cd85de1c48", "guid": "bfdfe7dc352907fc980b868725387e98a66597becd8b2dfda32129f85ae3ecff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845da0c9db44fe4f29ad5f3dc7b2e9f62", "guid": "bfdfe7dc352907fc980b868725387e98c0ea8959fda1752bb87e2fbcb3188d80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98decf7ebae0eef8d73c91fc7e05ea5496", "guid": "bfdfe7dc352907fc980b868725387e983523653e0d5c61540207109f7a9c339c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820b5d46f389f7e1d3aafe683d42c0264", "guid": "bfdfe7dc352907fc980b868725387e9895ff7098e45b7603109a9330e059bc41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edc29c08ce69fa288eea1b309c90b382", "guid": "bfdfe7dc352907fc980b868725387e98df6bb4c20b350c59e8064be1658459a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c58902556980ad5202837b936c4d2be9", "guid": "bfdfe7dc352907fc980b868725387e98dd265c53dd8339e79155b7eb7d1da963"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b3578892fe873ca39000b69937623a2", "guid": "bfdfe7dc352907fc980b868725387e9890276af59960aabeec05c986b3e3b934"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2314f040e7b2769e877317f03ca7f65", "guid": "bfdfe7dc352907fc980b868725387e9819699bda8495e834162441dcac3295cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98616336bff8e76f13bc5b0e87750da671", "guid": "bfdfe7dc352907fc980b868725387e98a9f15243618434068a025e70e9547742"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883b61812ade753ad29f7daebfc647291", "guid": "bfdfe7dc352907fc980b868725387e984e72ba7f44663b73e5e5b629bbe2843c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98806a20607d4a87cfbe96056d0ddf882f", "guid": "bfdfe7dc352907fc980b868725387e98b48e63107619503be22defdbc2648a6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c1fb96799ff8993f37958335057730b", "guid": "bfdfe7dc352907fc980b868725387e98479cd466095e1d1f5bc0bd3cebb9ec59"}], "guid": "bfdfe7dc352907fc980b868725387e98224c8ba588fbe7373916689b37473bad", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98097c8f22e817a37a81e327a63cc8f820", "guid": "bfdfe7dc352907fc980b868725387e98f496cd44db1f128141974e5c48d94239"}], "guid": "bfdfe7dc352907fc980b868725387e98f4f3eca4ce0d4c9c974a132f6fe6ef09", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98acc436478db290c41bb339e6371117ae", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98bf02bf20e32e434f9b2b3c0c04508486", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}