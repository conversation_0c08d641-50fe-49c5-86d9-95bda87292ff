{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986cf2e8df08f0765d460edfdec487be4b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986924be3699adadad3c0bc3b30667e62c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98246cce589fe5e95954c15de6c6b6557b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9892f3fbc714ec9d89c603fb2cbcf97e45", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98246cce589fe5e95954c15de6c6b6557b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98992aa2bf0998e7df0d8e9df55dcb580a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983e3a0b932c88749dfd27a3b3f3cd8527", "guid": "bfdfe7dc352907fc980b868725387e98f1d8bfe081a8ff1fad768ed7c36c1a52", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98317209eaf95c5ec93d26501fb239cc81", "guid": "bfdfe7dc352907fc980b868725387e987780df399b623bcbc0a204793846ce89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983336b1a708f6a06ca5881409d8895e2d", "guid": "bfdfe7dc352907fc980b868725387e983d56d24bd6f9f183c4436be74b65e54c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baa73656d266f9034143a996279b6410", "guid": "bfdfe7dc352907fc980b868725387e9825fe6bba55400242e8282f9f6792adcf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5be334ea46a52635473d164e433b4ee", "guid": "bfdfe7dc352907fc980b868725387e9844a6bdd475d375125fb40d40a34948ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98672ca634411fb300aefd090c55a29bbe", "guid": "bfdfe7dc352907fc980b868725387e985f4545dffd169ab094dacf0e2282ad97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875c9c2fc89d1a224b25ee82d6244dd55", "guid": "bfdfe7dc352907fc980b868725387e988a485fa41126a3879bcfb088980da396", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8550b10ea0d8c05b634433a0db0d7a9", "guid": "bfdfe7dc352907fc980b868725387e98cf9862e0a7f84b1c722d022f734069e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884507e4f5aee545bdd0166baa0d30480", "guid": "bfdfe7dc352907fc980b868725387e98d5255c0b71aa1b46081a394336d14d09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f340cd46feb7c458da2bdeb9ca9c55c5", "guid": "bfdfe7dc352907fc980b868725387e980873d61dc036a15410a686ad8259c6a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864d6be450870ca6af9bc9cbb24c06cce", "guid": "bfdfe7dc352907fc980b868725387e988fe5b8081e7d44163c0b56f2c5a89ce7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d14cef66fa52fc9d4b4d6d8d8e1aa4cb", "guid": "bfdfe7dc352907fc980b868725387e981ee9138550ff9dfbe22f55a5c853ffa7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fb3ea72334983134f94c108750138df", "guid": "bfdfe7dc352907fc980b868725387e98502d147940d0ed7325eb4c76b2249e52", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862ff25ef5de5b94aa457ecdca7561856", "guid": "bfdfe7dc352907fc980b868725387e98e26b04027621f012b65463f46397a000", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825fc15f8d499819a33323fcb834c09fe", "guid": "bfdfe7dc352907fc980b868725387e98af41eed25fd6e0f4b02788281952c335"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4228380ceca7b0f98ae7e7a422e6f42", "guid": "bfdfe7dc352907fc980b868725387e980d5b08e1964726ece2c23c132b5656be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987088af77b9a88244ddec455ea7eaf7a2", "guid": "bfdfe7dc352907fc980b868725387e981badde66fa729915636fc5c278ab3f01", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ecd7e40bbeeba53be39640c995e15a0", "guid": "bfdfe7dc352907fc980b868725387e98030e907aa914f5a46871b28e7e58679d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0e5bf6dc2fdd0c243ba57278a20f500", "guid": "bfdfe7dc352907fc980b868725387e98c087ad058a60d3a319351883395cf64e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d3e8ed47ab59bcb5a1b75489f3454bd", "guid": "bfdfe7dc352907fc980b868725387e98fb3b196a0d50d21c4813bdf8acc5fe23", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866978cdfd81b63ae8102f43b22a0d8a8", "guid": "bfdfe7dc352907fc980b868725387e9834a5d2544800c818218acb16aea341bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863883e0c657be1a5559d9c66a9e8d9bc", "guid": "bfdfe7dc352907fc980b868725387e98c69e034b08252387ec24db8b2f34b553"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22a68d7fd223e5dc0fd0fa0e90669cb", "guid": "bfdfe7dc352907fc980b868725387e98d43c8bc73a6f29ba0ed9b39d1d59ea6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b23b658a43233712ff89df955da4a3d2", "guid": "bfdfe7dc352907fc980b868725387e989c50880f51d702849b1b1212b2ce1755", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801edc7262bfc2282682db4c9b51b3e05", "guid": "bfdfe7dc352907fc980b868725387e989341039476b382d32f945e9dfe3a9c2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee0a2822e2a5970a4c4a7497f99d306", "guid": "bfdfe7dc352907fc980b868725387e98c17c0368fc98b137a748d3c5ad3748b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880de104c74da00b60ec62e1b8b435738", "guid": "bfdfe7dc352907fc980b868725387e98c4af544fff58c0a8358a0265c70a2886", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ec1decca86a1c54e6056f01f571e94f", "guid": "bfdfe7dc352907fc980b868725387e9803c69a85af6201dc1ecea114f667b1fe"}], "guid": "bfdfe7dc352907fc980b868725387e9859bf03661a2aa2ef8bcf89c330439c1f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c10342743dfd56e8631354f096ae5fcc", "guid": "bfdfe7dc352907fc980b868725387e98f3e68ca52dc404bd0683fd4af38b0c99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1406740340a00af1d7e014307d8fc7b", "guid": "bfdfe7dc352907fc980b868725387e98a58b87151ca90d1ad5f8e3dca4474e76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b3deb232fd76b2ae819c3e315c972a9", "guid": "bfdfe7dc352907fc980b868725387e9886ff6ed856ce6574966bad333542cbb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827f2180fafc8af37b77e17b25d5eecaa", "guid": "bfdfe7dc352907fc980b868725387e981bb8aeaff736e9cf5de41f746d962b67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c86e4a152ba65f80f56e40b48daadf5d", "guid": "bfdfe7dc352907fc980b868725387e98080f72e4a5712987bfb11c5113a16a34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b460ef6aa31e1b82ab12aaea5554072", "guid": "bfdfe7dc352907fc980b868725387e983b3098f464941c9d07f82eb08c2d11e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98191ae00d7bb29eed2989493e4de32fa0", "guid": "bfdfe7dc352907fc980b868725387e9860cd2ee89ca4d13402862ee6fbc0b08c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98456bf53ed51b32b520a795a04126b49e", "guid": "bfdfe7dc352907fc980b868725387e9822d6c03ea87a6e88b762c6bc0864d7e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986699dd312c0d366737a619f13347781d", "guid": "bfdfe7dc352907fc980b868725387e98bb5e3cc15e754671861bf8e6cb1371bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98303650af91f213850a2e848c4d5a4159", "guid": "bfdfe7dc352907fc980b868725387e98f4e2631c0d23feea39b0799ad2839340"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98465b94f129b4eaa7d2d28149b7de0c46", "guid": "bfdfe7dc352907fc980b868725387e9818b9bf4ef85cd2b07db6a8fd26c2b12a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820d27b63ece98ed495952e91345d9b87", "guid": "bfdfe7dc352907fc980b868725387e988600221b215325d620a08a0249937855"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9ace7d990a1472c338dc7f6e2953145", "guid": "bfdfe7dc352907fc980b868725387e985d583b973a5719578a686a140d734913"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877355aef61e85703fa0d2d5f875e56db", "guid": "bfdfe7dc352907fc980b868725387e987a194e2207b30667611d4fe4142e5fc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee8080f03c7bde2f2b01ac5fdb9d3409", "guid": "bfdfe7dc352907fc980b868725387e98d917d9671b79cf278f8a8a99f3032215"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98288655917af5ef8a21449a161fc8c3bd", "guid": "bfdfe7dc352907fc980b868725387e98551490380b62b834757232e224a14dfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f15668631435e35cf0d55f12196eefd", "guid": "bfdfe7dc352907fc980b868725387e98df50a1ad6bc62c3c1696fa5e8be4b27d"}], "guid": "bfdfe7dc352907fc980b868725387e98faf51f097a3e9a00ba3ffb7360f5e65f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98097c8f22e817a37a81e327a63cc8f820", "guid": "bfdfe7dc352907fc980b868725387e98c8f4b6b0098654d10a850442bda8a3e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa7dd7704aab0133f40d8e24a0aca088", "guid": "bfdfe7dc352907fc980b868725387e981133fc4f4c1db6f59d45314ce491b435"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bed58a9063d1c4ee5c3d97c1e34415c", "guid": "bfdfe7dc352907fc980b868725387e98875631728fe4c859a97b8d38d9da1344"}], "guid": "bfdfe7dc352907fc980b868725387e98a9ca4c957b813a2f3ccd18694e21ca81", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e06f68ab56d660a4ee96daf33cdf85ab", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98ae52c66e95fdcbccc9c41e868c127906", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}