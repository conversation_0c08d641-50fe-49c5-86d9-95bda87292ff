-- Fix token column size in api_tokens table
-- JWT tokens are typically 200-500 characters long, so we need to increase the column size

-- First, drop the unique index on token column
ALTER TABLE `api_tokens` DROP INDEX `api_tokens_token_unique`;

-- Modify the token column to be larger
ALTER TABLE `api_tokens` MODIFY COLUMN `token` varchar(1000) COLLATE utf8mb4_unicode_ci NOT NULL;

-- Recreate the unique index with a prefix to avoid key length issues
ALTER TABLE `api_tokens` ADD UNIQUE KEY `api_tokens_token_unique` (`token`(255));

-- Also ensure the table has the correct columns
-- Add created_at and updated_at if they don't exist with proper defaults
ALTER TABLE `api_tokens`
ADD COLUMN IF NOT EXISTS `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
